{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript", "sass"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": false, "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true, "es6": true, "condition": true, "uglifyFileName": true, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "swc": false, "compileWorklet": false, "uploadWithSourceMap": true, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableSWC": true, "disableUseStrict": false}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.8.9", "packOptions": {"ignore": [], "include": []}, "appid": "wxa3d5f856ff9495b3"}