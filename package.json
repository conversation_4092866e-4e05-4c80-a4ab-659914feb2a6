{"name": "miniprogram-ts-sass-quickstart", "version": "1.0.0", "description": "", "scripts": {"build:dev": "cross-env NODE_ENV=development run-s switch-env miniprogram-ci", "build:prod": "cross-env NODE_ENV=production run-s switch-env miniprogram-ci", "switch-env": "node switch-env.js", "miniprogram-ci": "node miniprogram-ci.js", "lint": "eslint --fix", "lint-all": "eslint . --fix", "format": "prettier --write", "format-all": "prettier . --write", "prepare": "husky"}, "keywords": [], "author": "", "license": "", "lint-staged": {"*.ts": ["npm run lint"], "*.{ts,wxs,wxml,wxss,json}": ["npm run format"]}, "dependencies": {"@miniprogram-component-plus/select-text": "^1.0.1", "@vant/weapp": "^1.11.7", "dayjs": "^1.11.13"}, "devDependencies": {"@eslint/js": "^9.21.0", "cross-env": "^7.0.3", "eslint": "^9.21.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "miniprogram-api-typings": "^4.0.5", "miniprogram-ci": "^2.0.10", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "typescript-eslint": "^8.26.0"}, "type": "module", "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "@swc/core", "core-js", "protobufjs"]}}