/**
 * WebSocket通信管理模块
 * 负责管理全局WebSocket连接、心跳机制和消息处理
 */

const config = require('../config/index');

let wsInstance = null;  // WebSocket实例
let pingTimer = null;   // 心跳定时器
let pongTimeoutTimer = null; // Pong响应超时定时器
let reconnectTimer = null; // 重连定时器
let messageListeners = {}; // 消息监听器
let token = '';        // 用户token
let reconnectAttempts = 0; // 重连尝试次数
let isReconnecting = false; // 是否正在重连
let connectionState = 'disconnected'; // 连接状态：disconnected, connecting, connected, error
let connectionPromise = null; // 当前连接的Promise，用于防止创建多个连接实例
const PING_INTERVAL = 30000; // 心跳间隔，30秒
const PONG_TIMEOUT = 20000; // Pong响应超时时间，增加到20秒，提高容错性
const INITIAL_RECONNECT_DELAY = 1000; // 首次重连前的延迟，改为1秒
const RECONNECT_INTERVAL = 1000; // 初始重连间隔，改为1秒
const MAX_RECONNECT_INTERVAL = 10000; // 最大重连间隔，增加到10秒
const MAX_RECONNECT_ATTEMPTS = 10; // 最大重连次数
let networkType = 'unknown'; // 当前网络类型

// WebSocket状态
const WS_STATUS = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
};

/**
 * 获取WebSocket URL
 * @param {string} token - 用户token
 * @returns {string} WebSocket URL
 */
const getWebSocketUrl = (token) => {
  // 将HTTP/HTTPS替换为WS/WSS
  const wsBaseUrl = config.api.baseUrl.replace(/^http/, 'ws');
  return `${wsBaseUrl}/ws/msg?token=${token}`;
};

/**
 * 初始化WebSocket连接
 * @param {string} userToken - 用户token
 * @returns {Promise} 连接建立的Promise
 */
const initWebSocket = (userToken) => {
  // 如果已经有一个连接Promise正在处理中，直接返回它
  if (connectionPromise && connectionState === 'connecting') {
    console.log('[WS] 已有连接正在建立中，复用现有连接Promise');
    return connectionPromise;
  }

  connectionPromise = new Promise((resolve, reject) => {
    if (!userToken) {
      connectionState = 'error';
      connectionPromise = null;
      reject(new Error('token不能为空'));
      return;
    }

    // 检查网络状态
    wx.getNetworkType({
      success: function(res) {
        networkType = res.networkType;
        console.log('[WS] 当前网络类型:', networkType);
        
        // 如果网络不可用，不尝试连接
        if (networkType === 'none') {
          console.log('[WS] 当前无网络连接，等待网络恢复');
          connectionState = 'error';
          connectionPromise = null;
          reject(new Error('无网络连接'));
          return;
        }
        
        continueInit();
      },
      fail: function() {
        // 无法获取网络状态，假设网络可用继续尝试
        console.warn('[WS] 无法获取网络状态，继续尝试连接');
        continueInit();
      }
    });
    
    function continueInit() {
    // 保存token
    token = userToken;
    
    // 设置连接状态为connecting
    connectionState = 'connecting';

    // 如果已存在连接，先关闭
    if (wsInstance && wsInstance.readyState !== WS_STATUS.CLOSED) {
      closeWebSocket();
    }

    // 清除定时器
    clearTimers();

    try {
      const wsUrl = getWebSocketUrl(token);
      wsInstance = wx.connectSocket({
        url: wsUrl,
          timeout: 10000, // 增加超时时间到10秒
        success: () => {
          console.log('[WS] 连接请求已发送');
        },
        fail: (error) => {
          console.error('[WS] 连接失败', error);
          connectionState = 'error';
          connectionPromise = null;
          handleReconnect(false);
          reject(error);
        }
      });

      // 监听WebSocket连接打开
      wsInstance.onOpen(() => {
        console.log('[WS] 连接已打开');
        connectionState = 'connected';
        reconnectAttempts = 0; // 重置重连尝试次数
        isReconnecting = false;
        clearTimeout(reconnectTimer); // 清除可能存在的重连定时器
        reconnectTimer = null;
        startHeartbeat();
        
        // 通知所有监听器连接已恢复
        notifyConnectionRestored();
        
        // 清除连接Promise，允许新的连接尝试
        connectionPromise = null;
        
        resolve(wsInstance);
      });

      // 监听WebSocket连接关闭
      wsInstance.onClose((event) => {
        console.log('[WS] 连接已关闭', `Code: ${event.code}`, `Reason: ${event.reason}`);
        connectionState = 'disconnected';
        // 重置连接Promise
        connectionPromise = null;
        
        // Don't trigger reconnect immediately if closeWebSocket was called intentionally
        if (connectionState !== 'closing') {
             handleReconnect(false); // Pass false as isManualClose
        } else {
             console.log('[WS] Intentional close, not reconnecting.');
             connectionState = 'disconnected'; // Ensure state is correct
        }
      });

      // 监听WebSocket错误
      wsInstance.onError((error) => {
        console.error('[WS] 连接错误', error);
        connectionState = 'error';
        // 重置连接Promise
        connectionPromise = null;
        
        // Check if wsInstance exists before trying to access readyState
        // The error might occur before the instance is fully established or after it's nullified
        if (wsInstance && wsInstance.readyState !== WS_STATUS.CLOSED) {
           // Optionally close explicitly on error before reconnecting
           // wsInstance.close({ code: 1011, reason: 'Client-side error detected' }); 
        }
        handleReconnect(false); // Pass false as isManualClose
        reject(error); // Still reject the promise for initWebSocket if it fails
      });

      // 监听WebSocket消息
      wsInstance.onMessage((res) => {
        handleMessage(res);
      });

    } catch (error) {
      console.error('[WS] 创建连接异常', error);
      connectionState = 'error';
      connectionPromise = null;
      reject(error);
      }
    }
  });

  return connectionPromise;
};

/**
 * 发送消息
 * @param {string|object} message - 要发送的消息
 * @returns {Promise} 发送结果的Promise
 */
const sendMessage = (message) => {
  return new Promise((resolve, reject) => {
    if (!wsInstance || wsInstance.readyState !== WS_STATUS.OPEN) {
      // 如果不是正在重连，则尝试重连
      if (!isReconnecting && token) {
        console.log('[WS] 连接已断开，尝试重连后发送消息');
        
        // 先重新连接，然后再发送消息
        handleReconnect();
        
        // 如果已存在连接Promise，等待其完成后再发送
        if (connectionPromise) {
          connectionPromise
            .then(() => {
              if (wsInstance && wsInstance.readyState === WS_STATUS.OPEN) {
                // 连接成功后重新尝试发送
                sendMessage(message)
                  .then(resolve)
                  .catch(reject);
              } else {
                reject(new Error('WebSocket重连后仍未连接'));
              }
            })
            .catch(error => {
              console.error('[WS] 重连失败，无法发送消息', error);
              reject(new Error('WebSocket重连失败'));
            });
          return;
        }
      }
      
      reject(new Error('WebSocket未连接'));
      return;
    }

    try {
      const sendData = typeof message === 'object' ? JSON.stringify(message) : message;
      
      wsInstance.send({
        data: sendData,
        success: (res) => {
          console.log('[WS] 消息发送成功', sendData);
          resolve(res);
        },
        fail: (error) => {
          console.error('[WS] 消息发送失败', error);
          
          // 检查是否是因为连接断开导致的发送失败
          if (wsInstance.readyState !== WS_STATUS.OPEN) {
            handleReconnect();
          }
          
          reject(error);
        }
      });
    } catch (error) {
      console.error('[WS] 发送消息异常', error);
      reject(error);
    }
  });
};

/**
 * 发送心跳消息
 */
const sendPing = () => {
  if (wsInstance && wsInstance.readyState === WS_STATUS.OPEN) {
    console.log('[WS] 发送心跳 Ping');
    
    // 在发送前检查当前网络状态
    if (networkType === 'none') {
      console.warn('[WS] 当前无网络连接，不发送心跳');
      return;
    }
    
    sendMessage('ping')
      .then(() => {
        // 设置Pong超时定时器
        clearTimeout(pongTimeoutTimer); // 清除上一个超时定时器
        pongTimeoutTimer = setTimeout(() => {
          console.error('[WS] Pong响应超时');
          // 如果超时，认为连接丢失，尝试关闭并重连
          if (wsInstance) {
             // 使用特定代码关闭连接
             try {
             wsInstance.close({code: 1001, reason: "Pong timeout"}); 
             } catch (e) {
               console.error('[WS] 关闭连接失败:', e);
               // 强制清理
               wsInstance = null;
               connectionState = 'disconnected';
               handleReconnect(false);
             }
          }
        }, PONG_TIMEOUT);
      })
      .catch(error => {
        console.error('[WS] 心跳发送失败', error);
        // 心跳发送失败时，不应该立即触发重连
        // 而是等待onClose/onError事件处理
      });
  } else {
     console.warn('[WS] 尝试发送心跳时连接不处于OPEN状态');
  }
};

/**
 * 启动心跳机制
 */
const startHeartbeat = () => {
  console.log('[WS] 启动心跳');
  // 清除现有的定时器
  clearInterval(pingTimer);
  clearTimeout(pongTimeoutTimer);

  // 立即发送一次心跳，然后设置定时器
  sendPing();
  pingTimer = setInterval(sendPing, PING_INTERVAL);
};

/**
 * 处理WebSocket消息
 * @param {object} res - 收到的消息对象
 */
const handleMessage = (res) => {
  try {
    const { data } = res;
    
    // 尝试解析消息
    let parsedData;
    try {
      parsedData = typeof data === 'string' ? JSON.parse(data) : data;
    } catch (e) {
      parsedData = data;
    }

    // 处理心跳响应
    if (data === 'pong' || (parsedData && parsedData.type === 'pong')) {
      console.log('[WS] 收到心跳响应 Pong');
      // 清除Pong超时定时器
      clearTimeout(pongTimeoutTimer);
      return;
    }

    console.log('[WS] 收到消息:', data); // 记录非心跳消息

    // 通知所有消息监听器
    let handled = false;
    Object.entries(messageListeners).forEach(([id, listener]) => {
      try {
        if (listener(parsedData)) {
          handled = true;
        }
      } catch (error) {
        console.error(`[WS] 消息监听器 ${id} 处理异常`, error);
      }
    });
    
    if (!handled) {
      console.log('[WS] 消息未被任何监听器处理');
    }
  } catch (error) {
    console.error('[WS] 处理消息异常', error);
  }
};

/**
 * 处理重连
 * @param {boolean} isManualClose - Indicates if the disconnect was initiated by closeWebSocket
 */
const handleReconnect = (isManualClose = false) => {
  // If intentionally closed, do not reconnect
  if (isManualClose) {
      console.log('[WS] 手动关闭连接，不进行重连');
      connectionState = 'disconnected'; 
      isReconnecting = false;
      return;
  }

  // 如果处于关闭中的状态，也不进行重连
  if (connectionState === 'closing') {
      console.log('[WS] 连接正在关闭中，不进行重连');
      connectionState = 'disconnected';
      isReconnecting = false;
      return;
  }

  // 检查网络状态
  wx.getNetworkType({
    success: function(res) {
      networkType = res.networkType;
      if (networkType === 'none') {
        console.log('[WS] 当前无网络连接，等待网络恢复后重连');
        connectionState = 'error';
        isReconnecting = false;
        // 通知所有监听器连接已断开且需要手动重连
        notifyConnectionLost(true);
        return;
      }
      
      continueReconnect();
    },
    fail: function() {
      // 无法获取网络状态，继续尝试重连
      console.warn('[WS] 无法获取网络状态，继续尝试重连');
      continueReconnect();
    }
  });
  
  function continueReconnect() {
  // 如果已经在重连中，不重复执行
  if (isReconnecting && reconnectTimer) {
    console.log('[WS] 已经在计划重连，忽略本次触发');
    return;
  }

  isReconnecting = true;

  // 清除心跳和Pong超时定时器，因为连接已断开
  clearHeartbeatTimers();

  // 检查是否超过最大重连次数
  if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
    console.error(`[WS] 已达到最大重连次数 ${MAX_RECONNECT_ATTEMPTS}，停止自动重连`);
      connectionState = 'error';
    isReconnecting = false;
      clearTimeout(reconnectTimer);
    reconnectTimer = null;
    // 通知所有监听器连接已断开且需要手动重连
    notifyConnectionLost(true);
    return;
  }

    // 使用指数退避策略计算重连延迟
    const backoffTime = Math.min(
      INITIAL_RECONNECT_DELAY * Math.pow(1.5, reconnectAttempts),
      MAX_RECONNECT_INTERVAL
    );

  reconnectAttempts++;

    // 通知所有监听器连接已断开且正在尝试重连
    if (connectionState !== 'connecting') {
      notifyConnectionLost(false);
      connectionState = 'connecting';
  }

    console.log(`[WS] 计划 ${backoffTime}ms 后重连 (尝试 ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);

  // 清除可能存在的旧重连定时器
  clearTimeout(reconnectTimer);

  // 设置重连定时器
  if (token) {
    reconnectTimer = setTimeout(() => {
        // 再次检查连接状态
      if (connectionState === 'connecting' || connectionState === 'disconnected' || connectionState === 'error') {
          console.log(`[WS] 尝试重连 (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
          isReconnecting = false; 
          initWebSocket(token).catch(error => {
              console.error('[WS] 重连尝试失败', error);
            // 避免立即重试，使用较短但有一定延迟的时间
              if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                  setTimeout(() => {
                      if (!isReconnecting) handleReconnect(false);
              }, 2000); // 2秒后重试
              }
          });
      } else {
          console.log('[WS] 重连取消，状态已改变:', connectionState);
          isReconnecting = false;
          reconnectTimer = null;
      }
    }, backoffTime);
  } else {
    console.error('[WS] 无法重连：token为空');
    isReconnecting = false;
      connectionState = 'error';
      notifyConnectionLost(true);
    }
  }
};

/**
 * 通知所有监听器连接已断开
 * @param {boolean} needManualReconnect - 是否需要手动重连
 */
const notifyConnectionLost = (needManualReconnect) => {
  const connectionMessage = {
    type: 'connection_status',
    status: 'disconnected',
    needManualReconnect: needManualReconnect,
    attemptCount: reconnectAttempts,
    maxAttempts: MAX_RECONNECT_ATTEMPTS
  };
  
  Object.entries(messageListeners).forEach(([id, listener]) => {
    try {
      listener(connectionMessage);
    } catch (error) {
      console.error(`[WS] 监听器 ${id} 处理连接断开通知异常`, error);
    }
  });
};

/**
 * 通知所有监听器连接已恢复
 */
const notifyConnectionRestored = () => {
  const connectionMessage = {
    type: 'connection_status',
    status: 'connected'
  };
  
  Object.entries(messageListeners).forEach(([id, listener]) => {
    try {
      listener(connectionMessage);
    } catch (error) {
      console.error(`[WS] 监听器 ${id} 处理连接恢复通知异常`, error);
    }
  });
};

/**
 * 清除所有定时器
 */
const clearTimers = () => {
  clearHeartbeatTimers();
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
};

/**
 * 清除心跳相关定时器
 */
const clearHeartbeatTimers = () => {
    if (pingTimer) {
        clearInterval(pingTimer);
        pingTimer = null;
    }
    if (pongTimeoutTimer) {
        clearTimeout(pongTimeoutTimer);
        pongTimeoutTimer = null;
    }
}

/**
 * 关闭WebSocket连接
 */
const closeWebSocket = () => {
  console.log('[WS] 请求关闭WebSocket连接...');
  // 设置状态，防止onClose触发自动重连
  connectionState = 'closing'; 

  // 清除所有定时器
  clearTimers();

  // 重置状态
  isReconnecting = false;
  reconnectAttempts = 0;
  connectionPromise = null; // 重置连接Promise

  if (wsInstance) {
    // Check readyState before closing
    if (wsInstance.readyState === WS_STATUS.OPEN || wsInstance.readyState === WS_STATUS.CONNECTING) {
        try {
            wsInstance.close({
                code: 1000, // Normal closure
                reason: 'Client requested close',
                success: () => {
                    console.log('[WS] 关闭请求发送成功');
                },
                fail: (error) => {
                    console.error('[WS] 关闭请求发送失败', error);
                     // Force state to disconnected even if close fails
                    connectionState = 'disconnected';
                    wsInstance = null; // Nullify instance on failure too
                }
            });
        } catch (error) {
            console.error('[WS] 调用关闭连接异常', error);
            connectionState = 'disconnected';
            wsInstance = null; // Nullify instance on exception
        }
    } else {
        console.log('[WS] 连接已处于关闭或正在关闭状态，无需再次关闭');
        // Ensure state is disconnected if it wasn't already closing
        if (connectionState !== 'closing') {
             connectionState = 'disconnected';
        }
        wsInstance = null; // Nullify if not open/connecting
    }
  } else {
      console.log('[WS] 没有活动的连接实例可关闭');
      connectionState = 'disconnected'; // Ensure state is correct
  }
  // The onClose handler will eventually set connectionState to 'disconnected' and nullify wsInstance
};

/**
 * 注册消息监听器
 * @param {string} id - 监听器ID
 * @param {function} callback - 回调函数
 */
const registerMessageListener = (id, callback) => {
  if (typeof callback === 'function') {
    messageListeners[id] = callback;
    console.log(`[WS] 注册消息监听器: ${id}`);
  }
};

/**
 * 注销消息监听器
 * @param {string} id - 监听器ID
 */
const unregisterMessageListener = (id) => {
  if (messageListeners[id]) {
    delete messageListeners[id];
    console.log(`[WS] 注销消息监听器: ${id}`);
  }
};

/**
 * 获取WebSocket实例
 * @returns {object|null} WebSocket实例
 */
const getWebSocketInstance = () => {
  return wsInstance;
};

/**
 * 获取WebSocket连接状态
 * @returns {boolean} 是否已连接
 */
const isConnected = () => {
  return wsInstance && wsInstance.readyState === WS_STATUS.OPEN;
};

/**
 * 获取连接状态
 * @returns {string} 连接状态: 'disconnected', 'connecting', 'connected', 'error'
 */
const getConnectionState = () => {
  return connectionState;
};

/**
 * 手动重新连接
 * @returns {Promise} 连接结果的Promise
 */
const reconnect = () => {
  // 重置重连计数和状态
  reconnectAttempts = 0;
  isReconnecting = false;
  
  // 尝试重新连接
  return initWebSocket(token);
};

// 添加网络状态监听
wx.onNetworkStatusChange(function(res) {
  networkType = res.networkType;
  console.log('[WS] 网络状态变化:', networkType, '已连接:', res.isConnected);
  
  if (res.isConnected) {
    // 网络恢复，检查WebSocket状态
    if (!isConnected() && 
        connectionState !== 'connecting' && 
        !isReconnecting && 
        token) {
      console.log('[WS] 网络已恢复，尝试重新连接');
      reconnectAttempts = 0; // 重置重连计数
      handleReconnect(false);
    }
  } else {
    // 网络断开，可以考虑清理资源
    console.log('[WS] 网络已断开，等待网络恢复');
    // 不主动关闭，等待网络恢复自动重连
  }
});

export default {
  initWebSocket,
  sendMessage,
  registerMessageListener,
  unregisterMessageListener,
  closeWebSocket,
  getWebSocketInstance,
  isConnected,
  getConnectionState,
  reconnect
}; 