/**
 * API Module
 * Central point for all API requests
 */

// Import API modules
import user<PERSON>pi from './modules/user'
import contentApi from './modules/data'
import chatApi from './modules/chat'
import authApi from './modules/auth'
import bookApi from './modules/book'
import websocket from './websocket'

// Import utility and interceptors
import { http, request } from './utils/request'
import { clearAuthAndRedirect } from './interceptors/auth'
import { storage } from '../config/index'

/**
 * API service object
 */
const API = {
  // User-related APIs
  user: userApi,
  
  // Content-related APIs
  content: contentApi,
  
  // Chat-related APIs
  chat: chatApi,
  
  // Book-related APIs
  book: bookApi,
  
  // Auth-related APIs
  auth: {
    ...authApi,
    clearAuth: clearAuthAndRedirect,
    isAuthenticated: () => {
      const token = wx.getStorageSync(storage.accessToken);
      return !!token;
    }
  },
  
  // Expose the base request functions for direct use if needed
  request,
  http,
  websocket,
  
  // Standard method to check if user is authenticated
  isAuthenticated: () => {
    const token = wx.getStorageSync(storage.accessToken);
    return !!token;
  },
  
  // Helper method to check for active session
  hasActiveSession: (sessionId) => {
    return !!sessionId;
  },
  
  // Utility to refresh API modules if needed
  refreshAPI: async function() {
    console.log('Refreshing API modules...');
    try {
      // 不使用动态导入，直接使用已导入的模块
      // 重新初始化已导入的模块
      this.user = userApi;
      this.content = contentApi;
      this.chat = chatApi;
      this.book = bookApi;
      this.auth = {
        ...authApi,
        clearAuth: clearAuthAndRedirect,
        isAuthenticated: () => {
          const token = wx.getStorageSync(storage.accessToken);
          return !!token;
        }
      };
      
      console.log('API modules re-initialized successfully.', 
        'Chat API methods:', Object.keys(this.chat)
      );
      return true;
    } catch (error) {
      console.error('Failed to re-initialize API modules:', error);
      return false;
    }
  }
}

// Log the API structure during initialization for debugging
console.log('API structure initialized:', 
  Object.keys(API).map(key => ({ key, exists: !!API[key] })),
  'Chat API methods:', Object.keys(chatApi)
);

export default API 