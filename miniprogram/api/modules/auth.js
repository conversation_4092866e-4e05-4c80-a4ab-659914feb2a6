/**
 * Authentication API Module
 * Handles all authentication-related API calls
 */

import { api, appId, storage } from '../../config/index';
import { http } from '../utils/request';

/**
 * Verify token validity
 * @param {string} token - The access token to verify
 * @returns {Promise<Object>} Result with valid status
 */
async function checkToken(token) {
  try {
    const result = await http.post(api.checkStatusUrl, { token });
    return { valid: result.code === 0, uuid: result.data.uuid, exp: result.data.exp };
  } catch (error) {
    console.error('Token verification failed', error);
    return { valid: false };
  }
}

/**
 * 基础登录方法 - 内部使用
 * @param {Object} params - 登录参数
 * @returns {Promise<Object>} 登录结果
 */
async function _baseLogin(params) {
  try {
    const result = await http.post(api.wechatLoginUrl, params);
    return {
      success: true,
      access_token: result.access_token || result.token,
      refresh_token: result.refresh_token,
      uuid: result.uuid,
      userInfo: result.user_info
    };
  } catch (error) {
    console.error('Login failed', error);
    return { success: false, error: error.message || '登录失败' };
  }
}

/**
 * Silent login with WeChat code
 * @param {string} code - WeChat login code
 * @returns {Promise<Object>} Login result with token and user info
 */
async function silentLogin(code) {
  return _baseLogin({
    code,
    app_id: appId,
    login_type: 'silent'
  });
}

/**
 * Login with WeChat code and user info
 * @param {string} code - WeChat login code
 * @param {Object} userInfo - User profile information
 * @returns {Promise<Object>} Login result with token and user info
 */
async function login(code, userInfo) {
  return _baseLogin({
    code,
    app_id: appId,
    user_info: userInfo
  });
}

/**
 * Login with just WeChat code
 * @param {string} code - WeChat login code
 * @returns {Promise<Object>} Login result with token and user info
 */
async function loginWithCode(code) {
  return _baseLogin({ code });
}

/**
 * Logout user
 * @returns {Promise<Object>} Logout result
 */
async function logout() {
  try {
    const refreshToken = wx.getStorageSync(storage.refreshToken);
    if (!refreshToken) {
      console.warn('退出登录时未找到refresh_token');
      return { success: true }; // 如果没有token，视为已登出状态
    }
    
    // 将refresh_token作为URL查询参数
    const url = `${api.logoutUrl}?refresh_token=${encodeURIComponent(refreshToken)}`;
    await http.post(url);
    return { success: true };
  } catch (error) {
    console.error('Logout failed', error);
    throw error;
  }
}

/**
 * Refresh token
 * @returns {Promise}
 */
function refreshToken() {
  const refreshToken = wx.getStorageSync(storage.refreshToken)
  return http.post(api.refreshTokenUrl, {
    refresh_token: refreshToken
  });
}

export default {
  checkToken,
  silentLogin,
  refreshToken,
  login,
  loginWithCode,
  logout,
}; 