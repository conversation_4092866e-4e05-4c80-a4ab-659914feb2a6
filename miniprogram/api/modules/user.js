import { request, http } from '../utils/request'
import { api } from '../../config/index'

/**
 * User API Module
 */

/**
 * Login with WeChat OAuth credentials
 * @param {string} code - WeChat login code
 * @param {object} userInfo - User information from WeChat
 * @returns {Promise}
 */

/**
 * 更新用户信息
 * @param {object} userInfo - 用户信息
 * @returns {Promise}
 */
function updateUserInfo(userInfo) {
  return http.put(api.user.profileUrl, {
    userInfo
  })
}

/**
 * 上传或更新用户详细资料 (FormData格式)
 * @param {object} profileData - 用户详细资料
 * @returns {Promise}
 */
function updateProfile(profileData) {
  return new Promise((resolve, reject) => {
    // 准备请求头 - FormData不需要设置content-type，让浏览器自动设置
    const headers = {}

    // 添加认证头
    const token = wx.getStorageSync('access_token')
    if (token) {
      headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`
    }

    // 构建FormData
    const formData = {}
    Object.keys(profileData).forEach(key => {
      formData[key] = profileData[key]
    })

    wx.request({
      url: `${api.baseUrl}${api.user.updateProfileUrl}`,
      method: 'PUT',
      data: formData,
      header: {
        ...headers,
        'content-type': 'multipart/form-data'
      },
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data)
        } else {
          reject(res)
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

/**
 * Get user profile
 * @returns {Promise}
 */
function getProfile() {
  return http.get(api.user.profileUrl)
}

/**
 * Get user initialization message
 * @returns {Promise} - Promise that resolves to initial message with q and chain_id
 */
function getUserInit() {
  return http.post(api.user.userInitUrl)
}

/**
 * Update user avatar
 * @param {string} avatarUrl - Avatar URL
 * @returns {Promise}
 */
function updateAvatar(avatarUrl) {
  return http.put(api.user.updateProfileUrl, {
    avatar_url: avatarUrl
  })
}

export default {
  getProfile,
  updateUserInfo,
  updateAvatar,
  getUserInit,
  updateProfile
} 