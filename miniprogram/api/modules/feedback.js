import { http } from '../utils/request'
import { api } from '../../config/index'

const feedbackApi = {
  /**
   * 提交反馈
   */
  submitFeedback: async (data) => {
    try {
      const response = await http.post(api.feedback.submitFeedbackUrl, data);
      return response;
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      throw error;
    }
  }
}

export default feedbackApi