import { http } from '../utils/request';
import { api } from '../../config/index';

// 导入模拟数据
const mockChatData = require('../../service/mocks/chatMockData');

// 是否使用模拟数据（开发环境中使用模拟数据）
const USE_MOCK_DATA = false;

/**
 * Chat-related API endpoints
 */
const chatApi = {
  /**
   * Get chat data
   * @param {string} sessionId - Session ID of the chat
   * @returns {Promise} Promise with chat data containing messages or a direct question
   */

  /**
   * 获取最新问题
   * @returns {Promise} 包含最新问题的Promise
   */
  getQuestion: async () => {
    if (USE_MOCK_DATA) {
      console.log('使用模拟问题数据');
      return Promise.resolve({
        q: '你未来有什么职业规划或人生目标？',
        chain_id: 'chain-4',
      });
    }

    try {
      const response = await http.get(api.chat.getQuestionUrl);

      if (!response) {
        throw new Error('No response from server');
      }

      console.log('getQuestion API返回数据:', response);

      // 如果响应是字符串（可能是JSON字符串），尝试解析
      if (typeof response === 'string') {
        try {
          return JSON.parse(response);
        } catch (e) {
          console.error('Failed to parse question response as JSON:', e);
          return response;
        }
      }

      // 返回完整的响应，由组件处理格式
      return response;
    } catch (error) {
      console.error('Failed to get question:', error);
      throw error;
    }
  },

  getQuestionUrlV2: async (topic_id) => {
    console.log('topic_id', topic_id);
    try {
      const url = `${api.chat.getQuestionUrlV2}?topic_id=${topic_id}`;
      const response = await http.get(url);

      if (!response) {
        throw new Error('No response from server');
      }
      return response;
    } catch (error) {
      console.error('Failed to get question:', error);
      throw error;
    }
  },

  getTopics: async () => {
    const response = await http.get(api.chat.topicsUrl);

    if (!response) {
      throw new Error('No response from server');
    }
    return response;
  },

  getProgress: async() => {
    const response = await http.get(api.chat.progressUrl);
      if (!response) {
      throw new Error('No response from server');
    }
    return response;
  },
  /**
   * 更新消息内容
   * @param {string} sessionId - 会话ID
   * @param {string} messageId - 消息ID
   * @param {string} content - 新的消息内容
   * @returns {Promise} 更新结果的Promise
   */
  updateMessage: async (sessionId, messageId, content) => {
    if (USE_MOCK_DATA) {
      console.log('使用模拟更新消息数据');
      return Promise.resolve({ success: true });
    }

    if (!sessionId || !messageId || !content) {
      return Promise.reject(new Error('Session ID, message ID and content are required'));
    }

    try {
      const response = await http.put(api.chat.messageUrl, {
        session_id: sessionId,
        message_id: messageId,
        content: content,
      });

      if (!response) {
        throw new Error('No response from server');
      }

      return response;
    } catch (error) {
      console.error('Failed to update message:', error);
      throw error;
    }
  },

  /**
   * 删除消息
   * @param {string} sessionId - 会话ID
   * @param {string} messageId - 消息ID
   * @returns {Promise} 删除结果的Promise
   */
  deleteMessage: async (sessionId, messageId) => {
    if (USE_MOCK_DATA) {
      console.log('使用模拟删除消息数据');
      return Promise.resolve({ success: true });
    }

    if (!sessionId || !messageId) {
      return Promise.reject(new Error('Session ID and message ID are required'));
    }

    try {
      const response = await http.delete(api.chat.messageUrl, {
        session_id: sessionId,
        message_id: messageId,
      });

      if (!response) {
        throw new Error('No response from server');
      }

      return response;
    } catch (error) {
      console.error('Failed to delete message:', error);
      throw error;
    }
  },

  /**
   * 播放音频消息
   * @param {string} audioUrl - 音频URL
   * @returns {Promise} 音频URL的Promise
   */
  getAudioUrl: async (audioId) => {
    if (USE_MOCK_DATA) {
      console.log('使用模拟音频URL');
      return Promise.resolve('https://example.com/audio/demo.mp3');
    }

    if (!audioId) {
      return Promise.reject(new Error('Audio ID is required'));
    }

    try {
      const response = await http.get(api.chat.audioUrl, { audio_id: audioId });

      if (!response) {
        throw new Error('No response from server');
      }

      return response.url || response;
    } catch (error) {
      console.error('Failed to get audio URL:', error);
      throw error;
    }
  },

  /**
   * 发送回复消息（可包含图片）
   * @param {Object} data - 包含回复内容、问题和链ID的对象
   * @param {string} [data.reply] - 回复内容（可选）
   * @param {string} data.chain_id - 链ID
   * @param {string} data.question - 当前问题
   * @param {string} [data.image_file_base64] - 图片的Base64编码（可选）
   * @param {string} [data.image_format] - 图片格式，如jpg、png（可选）
   * @returns {Promise} 发送结果的Promise
   */
  sendReply: async (data, methods) => {
    console.log('methods', methods);
    if (USE_MOCK_DATA) {
      console.log('使用模拟发送回复数据');
      return Promise.resolve({ success: true });
    }

    if (!data.chain_id) {
      return Promise.reject(new Error('Chain ID is required'));
    }

    // 检查是否至少有文本回复或图片
    if (!data.reply && !data.image_file_base64) {
      return Promise.reject(new Error('Either reply text or image is required'));
    }

    console.log('发送回复, 数据:', {
      ...data,
      image_file_base64: data.image_file_base64 ? '(BASE64数据已省略)' : null,
    });

    try {
      let response;
      if (methods === 'post') {
        response = await http.post(api.chat.replyUrl, data);
      }
      if (methods === 'put') {
        response = await http.put(api.chat.replyUrl, data);
      }

      if (!response) {
        throw new Error('No response from server');
      }

      console.log('回复发送成功, 响应:', response);
      return response;
    } catch (error) {
      console.error('Failed to send reply:', error);
      throw error;
    }
  },

  sendReplyV2: async (data, methods) => {
    console.log('methods', methods);
    if (USE_MOCK_DATA) {
      console.log('使用模拟发送回复数据');
      return Promise.resolve({ success: true });
    }

    if (!data.topic_id) {
      return Promise.reject(new Error('topic_id is required'));
    }

    // 检查是否至少有文本回复或图片
    if (!data.reply && !data.image_file_base64) {
      return Promise.reject(new Error('Either reply text or image is required'));
    }

    try {
      let response;
      if (methods === 'post') {
        response = await http.post(api.chat.replyUrlV2, data);
      }
      if (methods === 'put') {
        response = await http.put(api.chat.replyUrlV2, data);
      }

      if (!response) {
        throw new Error('No response from server');
      }

      console.log('回复发送成功, 响应:', response);
      return response;
    } catch (error) {
      console.error('Failed to send reply:', error);
      throw error;
    }
  },

  // 删除照片
  delImage: async (data) => {
    if (!data.message_id) {
      return Promise.reject(new Error('Message ID is required'));
    }

    try {
      const response = await http.delete(api.chat.delImage, data);

      if (!response) {
        throw new Error('No response from server');
      }

      console.log('回复发送成功, 响应:', response);
      return response;
    } catch (error) {
      console.error('Failed to send reply:', error);
      throw error;
    }
  },

  /**
   * 获取下一个问题链
   * @returns {Promise} 包含下一个问题链信息的Promise
   */
  nextChain: async () => {
    if (USE_MOCK_DATA) {
      console.log('使用模拟下一个问题链数据');
      return Promise.resolve({
        code: 0,
        data: {
          chain_id: 'chain-' + (Math.floor(Math.random() * 10) + 1),
          question: '这是一个模拟的下一个问题',
        },
      });
    }

    try {
      const response = await http.get(api.chat.nextChainUrl);

      if (!response) {
        throw new Error('No response from server');
      }

      return response;
    } catch (error) {
      console.error('Failed to get next chain:', error);
      throw error;
    }
  },

  /**
   * 请求AI生成回答
   * @param {Object} data - 请求参数
   * @param {string} data.chain_id - 问题链ID
   * @param {string} data.question - 当前问题
   * @returns {Promise} 包含AI生成回答的Promise
   */
  askAI: async (data) => {
    if (USE_MOCK_DATA) {
      console.log('使用模拟AI回答数据');
      return Promise.resolve({
        answer:
          '这是AI生成的模拟回答。在实际情况中，AI会根据当前问题生成个性化的回答，帮助您完成回忆录的创建。模拟回答可能包含一些通用的话题和观点，供您参考和编辑。',
      });
    }

    if (!data.chain_id || !data.question) {
      return Promise.reject(new Error('Chain ID and question are required'));
    }

    try {
      const response = await http.post(api.chat.askAIUrl, data);

      if (!response) {
        throw new Error('No response from server');
      }

      return response;
    } catch (error) {
      console.error('Failed to get AI answer:', error);
      throw error;
    }
  },

  /**
   * 更新消息媒体内容（文本、图片和音频）
   * @param {Object} data - 更新数据
   * @param {string} data.message_id - 消息ID
   * @param {string} data.session_id - 会话ID
   * @param {string} [data.content] - 新的消息内容（可选）
   * @param {string} [data.image_file_base64] - 图片的Base64编码（可选，null表示删除图片，undefined表示保留原图）
   * @param {string} [data.image_format] - 图片格式，如jpg、png（可选）
   * @param {string} [data.audio_file_base64] - 音频的Base64编码（可选）
   * @param {string} [data.audio_format] - 音频格式（可选）
   * @returns {Promise} 更新结果的Promise
   */
  updateMessageMedia: async (data) => {
    if (USE_MOCK_DATA) {
      console.log('使用模拟更新消息媒体数据');
      return Promise.resolve({
        success: true,
        saved_messages: {
          message_id: data.message_id,
          session_id: data.session_id,
          text: data.content,
          image_path: data.image_file_base64 ? '@https://example.com/images/mock-image.jpg' : '',
          audio_path: data.audio_file_base64 ? 'https://example.com/audio/mock-audio.mp3' : '',
          sender: 'user',
          created_at: new Date().toISOString(),
          tags: '[]',
        },
      });
    }

    if (!data.message_id || !data.session_id) {
      return Promise.reject(new Error('Message ID and session ID are required'));
    }

    // 检查是否有要更新的内容
    if (!data.content && data.image_file_base64 === undefined && data.audio_file_base64 === undefined) {
      return Promise.reject(new Error('At least one of content, image or audio is required for update'));
    }

    // 准备要发送的数据
    const requestData = {
      message_id: data.message_id,
      session_id: data.session_id,
      content: data.content,
    };

    // 只有在明确定义了image_file_base64时才添加到请求
    if (data.image_file_base64 !== undefined) {
      requestData.image_file_base64 = data.image_file_base64;
      requestData.image_format = data.image_format;
    }

    // 只有在明确定义了audio_file_base64时才添加到请求
    if (data.audio_file_base64 !== undefined) {
      requestData.audio_file_base64 = data.audio_file_base64;
      requestData.audio_format = data.audio_format;
    }

    console.log('更新消息媒体, 数据:', {
      message_id: data.message_id,
      session_id: data.session_id,
      content: data.content,
      has_new_image: data.image_file_base64 !== undefined,
      clear_image: data.image_file_base64 === null,
      keep_original_image: data.image_file_base64 === undefined,
      image_format: data.image_format,
      has_new_audio: data.audio_file_base64 !== undefined,
      audio_format: data.audio_format,
    });

    try {
      // 使用专用的更新媒体消息端点
      const response = await http.put(api.chat.updateMessageMediaUrl, requestData);

      if (!response) {
        throw new Error('No response from server');
      }

      console.log('消息媒体更新成功, 响应:', response);
      return response;
    } catch (error) {
      console.error('Failed to update message media:', error);
      throw error;
    }
  },

  /**
   * 跳过当前问题，获取新问题
   * @param {string} topic_id,qa_record_id - 当前问题链ID（可选）
   * @returns {Promise} 跳过操作结果
   */
  skipQuestion: async (topic_id, qa_record_id) => {
    if (USE_MOCK_DATA) {
      return Promise.resolve({
        code: 2000,
        message: '已跳过当前问题，获取新问题成功',
      });
    }
    try {
      const newSkipUrl = api.chat.skipUrl.replace(/{topic_id}/g, topic_id);
      console.log('跳过问题, URL:', newSkipUrl);
      // 确保skipUrl已在配置中正确设置
      if (!api.chat.skipUrl) {
        console.error('skipUrl未在配置中设置');
        throw new Error('Skip URL not configured');
      }

      const response = await http.post(api.chat.skipUrl, { qa_record_id });
      return response;
    } catch (error) {
      console.error('跳过问题失败:', error);
      throw error;
    }
  },
};

export default chatApi;
