/**
 * Book API Module
 * Handles all book-related API calls
 */

import { api } from '../../config/index';
import { http } from '../utils/request';

/**
 * Get book content data
 * @param {string} bookId - Optional book ID for specific books
 * @returns {Promise<Object>} Book data with chapters and content
 */
async function getBook(bookId = null) {
  try {
    // 构建请求URL
    const url = bookId ? `${api.data.bookUrl}/${bookId}` : api.data.bookUrl;
    console.log('[API] getBook 请求URL:', url);
    
    // 发起请求
    const response = await http.get(url);
    
    console.log('[API] getBook 响应:', response);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from book API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] getBook 请求失败:', error);
    throw error;
  }
}

/**
 * Generate book outline (memoir structure)
 * @returns {Promise<Object>} Result of outline generation task submission
 */
async function generateOutline() {
  try {
    console.log('[API] generateOutline 被调用');
    
    // 构建请求URL - 使用配置中的专用路径
    const url = `${api.baseUrl}${api.data.generateOutlineUrl}`;
    
    console.log('[API] generateOutline 请求URL:', url);
    
    // 发起请求
    const response = await http.post(url);
    
    console.log('[API] generateOutline 响应:', response);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from outline generation API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] generateOutline 请求失败:', error);
    throw error;
  }
}

/**
 * Get book generation status
 * @returns {Promise<Object>} Status of book generation
 */
async function getBookStatus() {
  try {
    const response = await http.get(api.data.bookStatusUrl);
    return response;
  } catch (error) {
    console.error('[API] getBookStatus 请求失败:', error);
    throw error;
  }
}
/**
 * Generate chapter content
 * @param {Object} params - Parameters for chapter generation
 * @param {number} params.chapter_id - Chapter ID
 * @param {number} params.subchapter_id - Subchapter ID
 * @param {string} params.reference_id - Reference ID for the chapter
 * @returns {Promise<Object>} Generated chapter content
 */
async function generateChapter(params) {
  try {
    console.log('[API] generateChapter 被调用:', params);
    
    // 构建请求URL - 使用流式生成的端点
    const url = `${api.baseUrl}${api.data.generateChapterUrl}`;
    
    console.log('[API] generateChapter 请求URL:', url);
    
    // 发起请求
    const response = await http.post(url, params);
    
    console.log('[API] generateChapter 响应:', response);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from chapter generation API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] generateChapter 请求失败:', error);
    throw error;
  }
}

/**
 * Polish chapter content
 * @param {Object} params - Parameters for chapter polishing
 * @param {string} params.subchapter_id - Subchapter ID
 * @param {string} params.hint_text - Hint text for polishing
 * @returns {Promise<Object>} Result of polishing task submission
 */
async function polishTextStream(params) {
  try {
    console.log('[API] polishTextStream 被调用:', params);
    
    // 构建请求URL
    const url = `${api.baseUrl}${api.data.polishTextStreamUrl}`;
    
    console.log('[API] polishTextStream 请求URL:', url);
    
    // 发起请求
    const response = await http.post(url, params);
    
    console.log('[API] polishTextStream 响应:', response);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from polish text stream API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] polishTextStream 请求失败:', error);
    throw error;
  }
}

/**
 * Format subchapter content
 * @param {Object} params - Parameters for subchapter formatting
 * @param {string} params.session_id - Session ID
 * @param {number} params.subchapter_id - Subchapter ID
 * @param {boolean} params.mini_program - Whether the request is from mini program
 * @returns {Promise<Object>} Formatted subchapter content
 */
async function formatSubchapter(params) {
  try {
    console.log('[API] formatSubchapter 被调用:', params);
    
    // 构建请求URL - 使用配置中的专用路径
    const url = `${api.baseUrl}${api.data.formatSubchapterUrl}`;
    
    console.log('[API] formatSubchapter 请求URL:', url);
    
    // 发起请求
    const response = await http.post(url, params);
    
    console.log('[API] formatSubchapter 响应:', response);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from format subchapter API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] formatSubchapter 请求失败:', error);
    throw error;
  }
}

/**
 * 上传书籍封面
 * @param {Object} params
 * @param {string} params.session_id
 * @param {string} params.image_file_base64
 * @param {string} params.image_format
 * @returns {Promise<Object>}
 */
async function uploadBookCover(params) {
  try {
    console.log('[API] uploadBookCover 被调用:', params);
    const url = `${api.baseUrl}/v1/book/upload_book_cover`;
    const response = await http.post(url, params);
    console.log('[API] uploadBookCover 响应:', response);
    if (!response) throw new Error('No data received from upload book cover API');
    return response;
  } catch (error) {
    console.error('[API] uploadBookCover 请求失败:', error);
    throw error;
  }
}

/**
 * Generate book timeline
 * @returns {Promise<Object>} Result of timeline generation task submission
 */
async function generateTimeline() {
  try {
    console.log('[API] generateTimeline 被调用');
    
    // 构建请求URL - 使用配置中的专用路径
    const url = `${api.baseUrl}${api.data.generateTimelineUrl}`;
    
    console.log('[API] generateTimeline 请求URL:', url);
    
    // 发起请求
    const response = await http.get(url);
    
    console.log('[API] generateTimeline 响应:', response);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from timeline generation API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] generateTimeline 请求失败:', error);
    throw error;
  }
}

async function updateOutline() {
  try {
    console.log('[API] generateTimeline 被调用');
    
    // 构建请求URL - 使用配置中的专用路径
    const url = `${api.baseUrl}${api.data.updateOutlineUrl}`;
    
    console.log('[API] generateTimeline 请求URL:', url);
    
    // 发起请求
    const response = await http.get(url);
    
    console.log('[API] generateTimeline 响应:', response);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from timeline generation API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] generateTimeline 请求失败:', error);
    throw error;
  }
}

async function updateOutlineByVersion(params) {
  try {
    console.log('[API] updateOutlineByVersion 被调用');
    
    // 构建请求URL - 使用配置中的专用路径
    const url = `${api.baseUrl}${api.data.updateOutlineByVersion}`;
    
    // 发起请求
    const response = await http.post(url,params);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from timeline generation API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] generateTimeline 请求失败:', error);
    throw error;
  }
}


async function bookEdit(params) {
  try {
    console.log('bookEdit被调用');
    
    // 构建请求URL - 使用配置中的专用路径
    const url = `${api.baseUrl}${api.data.bookEdit}`;
    
    // 发起请求
    const response = await http.post(url,params);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from timeline generation API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] bookEdit 请求失败:', error);
    throw error;
  }
}


/**
 * Get book timeline
 * @returns {Promise<Object>} Book timeline data
 */
async function getTimeline() {
  try {
    console.log('[API] getTimeline 被调用');
    
    // 构建请求URL - 使用配置中的专用路径
    const url = `${api.baseUrl}${api.data.getTimelineUrl}`;
    
    console.log('[API] getTimeline 请求URL:', url);
    
    // 发起请求
    const response = await http.get(url);
    
    console.log('[API] getTimeline 响应:', response);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from timeline API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] getTimeline 请求失败:', error);
    throw error;
  }
}

/**
 * Generate PDF for the current book
 * @returns {Promise<Object>} Result of PDF generation with PDF URL
 */
async function generatePDF() {
  try {
    console.log('[API] generatePDF 被调用');
    
    // 构建请求URL
    const url = `${api.baseUrl}/v1/book/generate_pdf`;
    
    console.log('[API] generatePDF 请求URL:', url);
    
    // 发起请求
    const response = await http.get(url);
    
    console.log('[API] generatePDF 响应:', response);
    
    // 确保返回有效数据
    if (!response) {
      throw new Error('No data received from PDF generation API');
    }
    
    return response;
  } catch (error) {
    console.error('[API] generatePDF 请求失败:', error);
    throw error;
  }
}

/**
 * Get PDF URL for the earliest session or by session_id
 * @param {string} sessionId - Optional session ID
 * @returns {Promise<Object>} PDF URL response
 */
async function getPdfUrl(sessionId = null) {
  try {
    console.log('[API] getPdfUrl 被调用', sessionId);
    const url = `${api.baseUrl}${api.data.getPdfUrl}`;
    const response = await http.get(url);
    console.log('[API] getPdfUrl 响应:', response);
    return response;
  } catch (error) {
    console.error('[API] getPdfUrl 请求失败:', error);
    throw error;
  }
}


export default {
  getBook,
  getBookStatus,
  generateChapter,
  generateOutline,
  polishTextStream,
  formatSubchapter,
  uploadBookCover,
  generateTimeline,
  getTimeline,
  generatePDF,
  getPdfUrl,
  updateOutline,
  updateOutlineByVersion,
  bookEdit
}; 