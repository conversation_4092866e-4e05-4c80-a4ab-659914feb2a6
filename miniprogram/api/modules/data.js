import { http } from '../utils/request'
import { api } from '../../config/index'

// 导入模拟数据
const mockChatData = require('../../service/mocks/chatMockData')

// 是否使用模拟数据（开发环境中使用模拟数据）
const USE_MOCK_DATA = false

// 缓存系统
const cache = {
  chatData: {}, // 按sessionId存储聊天数据
  progressData: null, // 存储进度数据
  expiryTimes: {}, // 缓存过期时间
  DEFAULT_TTL: 300000, // 默认缓存时间5分钟 (从1分钟延长)
  
  /**
   * 从缓存获取数据
   * @param {string} key - 缓存键
   * @returns {any|null} 缓存的数据或null
   */
  get(key) {
    // 检查缓存是否存在且未过期
    if (this.chatData[key] && this.expiryTimes[key] > Date.now()) {
      console.log(`[Cache] 使用缓存数据: ${key}`);
      return this.chatData[key];
    }
    return null;
  },
  
  /**
   * 将数据存入缓存
   * @param {string} key - 缓存键
   * @param {any} data - 要缓存的数据
   * @param {number} ttl - 缓存生存时间(毫秒)
   */
  set(key, data, ttl = this.DEFAULT_TTL) {
    this.chatData[key] = data;
    this.expiryTimes[key] = Date.now() + ttl;
    console.log(`[Cache] 缓存数据: ${key}, 过期时间: ${ttl}ms`);
  },
  
  /**
   * 清除指定键的缓存
   * @param {string} key - 缓存键
   */
  clear(key) {
    if (key) {
      delete this.chatData[key];
      delete this.expiryTimes[key];
      console.log(`[Cache] 清除缓存: ${key}`);
    }
  },
  
  /**
   * 清除所有缓存
   */
  clearAll() {
    this.chatData = {};
    this.expiryTimes = {};
    this.progressData = null;
    console.log('[Cache] 清除所有缓存');
  }
};

/**
 * Content-related API endpoints
 */
const contentApi = {
  getTime: () => http.get('https://api.timelessq.com/time'),

  getBookVersion:() =>{
    return http.get(api.data.getBookVersion)
  },

  getBookByVersion:(version_number) => {
    const url = version_number ? `${api.data.getBookByVersion}/${version_number}` : api.data.getBookByVersion
    return http.get(url)
  },

  /**
   * Get book content
   * @param {string} id - Book ID (optional)
   * @returns {Promise} Promise with book data
   */
  getBook: (id) => {
    const url = id ? `${api.data.bookUrl}/${id}` : api.data.bookUrl
    return http.get(url)
  },

  /**
   * Get shared book content with session_id
   * @param {object} params - Parameters containing session_id
   * @returns {Promise} Promise with shared book data
   */
  getSharedBook: (params) => {
    return http.get(api.data.sharedBookUrl, params)
  },

  /**
   * Get chat data with caching
   * @param {string} sessionId - Session ID (optional)
   * @param {boolean} forceRefresh - Force refresh ignoring cache
   * @returns {Promise} Promise with chat data containing session_id and messages
   */
  getChat: async (sessionId, forceRefresh = false) => {
    console.log(`[API] getChat 被调用: sessionId=${sessionId}, forceRefresh=${forceRefresh}`);
    
    // 获取调用栈以便调试
    const stack = new Error().stack;
    console.log(`[API] getChat 调用栈: ${stack}`);
    
    // 构造缓存键
    const cacheKey = sessionId || 'default';
    
    // 如果未强制刷新，尝试从缓存获取
    if (!forceRefresh) {
      const cachedData = cache.get(cacheKey);
      if (cachedData) {
        console.log(`[API] 返回缓存数据: sessionId=${sessionId}`);
        return cachedData;
      }
    } else {
      console.log(`[API] 强制刷新: sessionId=${sessionId}`);
    }
    
    // 如果启用了模拟数据，直接返回模拟数据
    if (USE_MOCK_DATA) {
      console.log("使用模拟聊天数据");
      cache.set(cacheKey, mockChatData);
      return mockChatData;
    }
    
    try {
      // 直接调用 get_chat 端点，无需传递 session_id 参数
      // 后端会自动返回最早的会话
      // 如果提供了 sessionId，则作为查询参数传递
      const response = await http.get(api.data.chatUrl, sessionId ? { session_id: sessionId } : {});
      console.log("get_chat response:", response);
      
      if (!response) {
        throw new Error('No response from server');
      }
      
      // 如果响应是字符串（可能是JSON字符串），尝试解析
      let parsedResponse;
      if (typeof response === 'string') {
        try {
          parsedResponse = JSON.parse(response);
        } catch (e) {
          console.error('Failed to parse chat response as JSON:', e);
          parsedResponse = response;
        }
      } else {
        parsedResponse = response;
      }
      
      // 缓存结果 - 延长缓存时间为5分钟
      cache.set(cacheKey, parsedResponse, 300000); // 5分钟缓存
      
      return parsedResponse;
    } catch (error) {
      console.error("[API] getChat 请求失败:", error);
      throw error;
    }
  },

    /**
   * Get chat data with caching
   * @param {string} sessionId - Session ID (optional)
   * @param {boolean} forceRefresh - Force refresh ignoring cache
   * @returns {Promise} Promise with chat data containing session_id and messages
   */
  getChatOverView: async (sessionId = null, forceRefresh = false) => {
    console.log(`[API] getChatOverView 被调用: sessionId=${sessionId}, forceRefresh=${forceRefresh}`);
    
    // // 获取调用栈以便调试
    // const stack = new Error().stack;
    // console.log(`[API] getChatOverView 调用栈: ${stack}`);
    
    // 构造缓存键
    const cacheKey = `chatOverView_${sessionId || 'default'}`;
    
    // 如果未强制刷新，尝试从缓存获取
    if (!forceRefresh) {
      const cachedData = cache.get(cacheKey);
      if (cachedData) {
        console.log(`[API] 返回缓存数据: sessionId=${sessionId}`);
        return cachedData;
      }
    } else {
      console.log(`[API] 强制刷新: sessionId=${sessionId}`);
    }
    
    // 如果启用了模拟数据，直接返回模拟数据
    if (USE_MOCK_DATA) {
      console.log("使用模拟聊天数据");
      cache.set(cacheKey, mockChatData);
      return mockChatData;
    }
    
    try {
      // 调用 chat_overview 端点
      // 如果提供了 sessionId，则作为查询参数传递
      const response = await http.get(api.data.chatOverView, sessionId ? { session_id: sessionId } : {});
      console.log("getChatOverView response:", response);
      
      if (!response) {
        throw new Error('No response from server');
      }
      
      // 如果响应是字符串（可能是JSON字符串），尝试解析
      let parsedResponse;
      if (typeof response === 'string') {
        try {
          parsedResponse = JSON.parse(response);
        } catch (e) {
          console.error('Failed to parse chat response as JSON:', e);
          parsedResponse = response;
        }
      } else {
        parsedResponse = response;
      }
      
      // 缓存结果 - 延长缓存时间为5分钟
      cache.set(cacheKey, parsedResponse, 300000); // 5分钟缓存
      
      return parsedResponse;
    } catch (error) {
      console.error("[API] getChat 请求失败:", error);
      throw error;
    }
  },

  /**
   * Get chat data with caching
   * @param {string} sessionId - Session ID (optional)
   * @param {boolean} forceRefresh - Force refresh ignoring cache
   * @returns {Promise} Promise with chat data containing session_id and messages
   */
  getChatOverViewV2: async (sessionId = null, forceRefresh = false) => {
    console.log(`[API] getChatOverView 被调用: sessionId=${sessionId}, forceRefresh=${forceRefresh}`);
    
    // // 获取调用栈以便调试
    // const stack = new Error().stack;
    // console.log(`[API] getChatOverView 调用栈: ${stack}`);
    
    // 构造缓存键
    const cacheKey = `chatOverView_${sessionId || 'default'}`;
    
    // 如果未强制刷新，尝试从缓存获取
    if (!forceRefresh) {
      const cachedData = cache.get(cacheKey);
      if (cachedData) {
        console.log(`[API] 返回缓存数据: sessionId=${sessionId}`);
        return cachedData;
      }
    } else {
      console.log(`[API] 强制刷新: sessionId=${sessionId}`);
    }
    
    // 如果启用了模拟数据，直接返回模拟数据
    if (USE_MOCK_DATA) {
      console.log("使用模拟聊天数据");
      cache.set(cacheKey, mockChatData);
      return mockChatData;
    }
    
    try {
      // 调用 chat_overview 端点
      // 如果提供了 sessionId，则作为查询参数传递
      const response = await http.get(api.data.getChatOverViewV2, sessionId ? { session_id: sessionId } : {});
      
      if (!response) {
        throw new Error('No response from server');
      }
      
      // 如果响应是字符串（可能是JSON字符串），尝试解析
      let parsedResponse;
      if (typeof response === 'string') {
        try {
          parsedResponse = JSON.parse(response);
        } catch (e) {
          parsedResponse = response;
        }
      } else {
        parsedResponse = response;
      }
      
      // 缓存结果 - 延长缓存时间为5分钟
      cache.set(cacheKey, parsedResponse, 300000); // 5分钟缓存
      
      return parsedResponse;
    } catch (error) {
      console.error("[API] getChat 请求失败:", error);
      throw error;
    }
  },

  // 购买服务
  submitPurchase: async (data) => {
    try {
      const response = await http.post(api.data.purchaseUrl, data);
      return response;
    } catch (error) {
      console.error('Failed to purchaseUrl:', error);
      throw error;
    }
  },

  /**
   * Get progress data for the interview timeline
   * @param {boolean} forceRefresh - Force refresh ignoring cache
   * @returns {Promise} Promise with progress data containing periods and chains
   */
  getProgress: async (forceRefresh = false) => {
    console.log(`[API] getProgress 被调用: forceRefresh=${forceRefresh}`);
    
    // 如果未强制刷新，尝试从缓存获取
    if (!forceRefresh && cache.progressData) {
      console.log(`[API] 返回缓存的进度数据`);
      return cache.progressData;
    }
    
    try {
      const response = await http.get(api.data.progressUrl);
      console.log("get_progress response:", response);
      
      if (!response) {
        throw new Error('No response from server');
      }
      
      // 如果响应是字符串（可能是JSON字符串），尝试解析
      let parsedResponse;
      if (typeof response === 'string') {
        try {
          parsedResponse = JSON.parse(response);
        } catch (e) {
          console.error('Failed to parse progress response as JSON:', e);
          parsedResponse = response;
        }
      } else {
        parsedResponse = response;
      }
      
      // 缓存结果
      cache.progressData = parsedResponse;
      
      return parsedResponse;
    } catch (error) {
      console.error("[API] getProgress 请求失败:", error);
      throw error;
    }
  },

  /**
   * Invalidate chat cache for a session
   * @param {string} sessionId - Session ID (optional)
   */
  invalidateChatCache: (sessionId) => {
    const cacheKey = sessionId || 'default';
    cache.clear(cacheKey);
  },

  /**
   * Invalidate progress cache
   */
  invalidateProgressCache: () => {
    cache.progressData = null;
    console.log('[API] 进度数据缓存已清除');
  },

  /**
   * Get all sessions for the current user
   * @returns {Promise} Promise with all user sessions
   */
  getAllSessions: () => {
    return http.get(api.data.allSessionsUrl);
  },

  /**
   * Reset all interview data for the current user
   * This deletes all chat sessions and related data
   * @returns {Promise} Promise with reset result
   */
  resetUserData: (params) => {
    return http.post(api.data.resetUserUrl, params);
  }
}

export default contentApi 