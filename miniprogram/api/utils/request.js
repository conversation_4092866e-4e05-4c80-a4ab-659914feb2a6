import { debug, api, storage } from '../../config/index'
import { handleUnauthorized, addAuthHeader } from '../interceptors/auth'

/**
 * 基础请求函数
 * @param {object} options - 请求选项
 * @returns {Promise} - 请求Promise
 */
const request = (options) => {
  // 提取选项
  const { 
    url, 
    method, 
    data = {}, 
    onLoadingChange = null, // 新增：加载状态变更回调
    loadingTitle = '加载中',
    retry = true, // 是否允许重试
    retryCount = 0, // 当前重试次数
    maxRetries = 1 // 最大重试次数
  } = options

  // 通知上层组件更新加载状态
  if (onLoadingChange && retryCount === 0) {
    onLoadingChange(true, loadingTitle);
  }

  // 记录请求日志
  if (debug.logRequests) {
    console.log(`API请求: ${method} ${url}`, data, retryCount > 0 ? `(重试 #${retryCount})` : '')
  }

  // 实际请求函数
  const makeRequest = (token = null) => {
    console.log(`发送请求 (${method}): ${url}${token ? ' [带新token]' : ''}`)
    
    return new Promise((resolve, reject) => {
      // 准备请求头
      const headers = {
        'content-type': 'application/json'
      }
      
      // 添加认证头
      if (token) {
        headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`
      } else {
        addAuthHeader(headers)
      }
      
      wx.request({
        url: /^https?:\/\//.test(url) ? url : `${api.baseUrl}${url}`,
        method,
        data,
        header: headers,
        success: (res) => {
          // 记录响应
          if (debug.logRequests) {
            console.log(`API响应 (${res.statusCode}): ${method} ${url}`, res.data)
          }
          
          if (res.statusCode >= 200 && res.statusCode < 300) {
            console.log(`请求成功: ${url}`)
            resolve(res.data)
          } else {
            console.log(`请求失败 (${res.statusCode}): ${url}`)
            // 处理不同状态码，传入resolve和reject函数
            handleErrorResponse(res, reject, makeRequest, options, resolve)
          }
        },
        fail: (err) => {
          console.error(`请求网络错误: ${url}`, err)
          
          // 网络错误自动重试
          if (retry && retryCount < maxRetries) {
            console.log(`网络错误自动重试 (${retryCount+1}/${maxRetries}): ${url}`)
            
            // 延迟一段时间再重试
            setTimeout(() => {
              const retryOptions = {
                ...options,
                retryCount: retryCount + 1,
                onLoadingChange: null // 重试时不触发加载状态变更
              }
              
              request(retryOptions)
                .then(result => {
                  console.log(`网络错误重试成功: ${url}`);
                  resolve(result); // 确保将结果传递给原始Promise
                })
                .catch(error => {
                  console.error(`网络错误重试失败: ${url}`, error);
                  reject(error); // 确保将错误传递给原始Promise
                });
            }, 1000) // 延迟1秒重试
          } else {
            reject(err)
          }
        },
        complete: () => {
          // 只有在初始请求（非重试）完成时才通知上层组件更新加载状态
          if (onLoadingChange && retryCount === 0) {
            onLoadingChange(false);
          }
        }
      })
    })
  }

  return makeRequest()
}

/**
 * 处理错误响应
 * @param {object} res - 错误响应
 * @param {function} reject - Promise拒绝函数
 * @param {function} retry - 重试函数
 * @param {object} options - 原始请求选项
 * @param {function} resolve - Promise解决函数
 */
const handleErrorResponse = (res, reject, retry, options, resolve) => {
  // 提取错误信息
  const errorMessage = res.data?.detail || res.data?.message || '请求失败'
  
  switch (res.statusCode) {
    case 401:
      console.log(`认证错误(401): ${options.url}`)
      
      // 检查是否有access_token
      const token = wx.getStorageSync(storage.accessToken)
      if (!token) {
        console.log('本地没有access_token，跳转到登录页面')
        wx.navigateTo({
          url: '/pages/interview/index'
        })
        reject(res)
        return
      }
      
      if (options.retryCount >= options.maxRetries) {
        console.log(`已达到最大重试次数 (${options.maxRetries}): ${options.url}`)
        wx.showToast({
          title: errorMessage,
          icon: 'none'
        })
        reject(res)
        return
      }
      
      // 处理认证错误 - 注意这里不使用return，而是直接在结果中处理原始Promise
      console.log(`开始处理401错误 (${options.url})`)
      handleUnauthorized((token) => {
        console.log(`401处理中，使用新token重试请求: ${options.url}`);
        // 返回重试操作的Promise
        return retry(token);
      })
      .then(result => {
        console.log(`认证错误处理完成，请求重试成功: ${options.url}`);
        // 这里我们直接解决原始Promise，而不是返回一个新Promise
        // 这样就不会在wx.request的success回调中创建一个无人等待的Promise
        resolve(result);
      })
      .catch(err => {
        console.error(`认证错误处理失败: ${options.url}`, err);
        wx.showToast({
          title: errorMessage,
          icon: 'none'
        });
        // 使用传入的reject函数拒绝原始Promise
        reject(err);
      });
      
      // 防止继续执行下面的reject(res)
      return;
    
    case 403:
      wx.showToast({
        title: errorMessage || '没有权限访问',
        icon: 'none'
      })
      break
      
    case 404:
      wx.showToast({
        title: errorMessage || '请求的资源不存在',
        icon: 'none'
      })
      break
      
    case 422:
      wx.showToast({
        title: errorMessage || '数据验证失败',
        icon: 'none'
      })
      break
      
    case 500:
    case 502:
    case 503:
      // 服务器错误可以尝试重试
      if (options.retry && options.retryCount < options.maxRetries) {
        console.log(`服务器错误自动重试 (${options.retryCount+1}/${options.maxRetries}): ${options.url}`)
        
        // 返回一个新的Promise，确保Promise链不会断裂
        setTimeout(() => {
          const retryOptions = {
            ...options,
            retryCount: options.retryCount + 1,
            onLoadingChange: null // 重试时不触发加载状态变更
          }
          
          request(retryOptions)
            .then(result => {
              console.log(`服务器错误重试成功: ${options.url}`);
              resolve(result);
            })
            .catch(error => {
              console.error(`服务器错误重试失败: ${options.url}`, error);
              reject(error);
            });
        }, 1000);
        
        // 防止继续执行reject(res)
        return;
      }
      
      wx.showToast({
        title: errorMessage || '服务器错误，请稍后再试',
        icon: 'none'
      })
      break
      
    default:
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
  }
  
  reject(res)
}

/**
 * HTTP方法便捷封装
 */
const http = {
  /**
   * GET请求
   * @param {string} url - 请求URL
   * @param {object} params - 查询参数
   * @param {object} options - 请求选项
   * @returns {Promise} - 请求Promise
   */
  get: (url, params = {}, options = {}) => {
    // 将查询参数添加到URL
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    const queryUrl = params && Object.keys(params).length > 0
      ? `${url}${url.includes('?') ? '&' : '?'}${queryString}`
      : url
    
    return request({
      url: queryUrl,
      method: 'GET',
      ...options
    })
  },
  
  /**
   * POST请求
   * @param {string} url - 请求URL
   * @param {object} data - 请求数据
   * @param {object} options - 请求选项
   * @returns {Promise} - 请求Promise
   */
  post: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'POST',
      data,
      ...options
    })
  },
  
  /**
   * PUT请求
   * @param {string} url - 请求URL
   * @param {object} data - 请求数据
   * @param {object} options - 请求选项
   * @returns {Promise} - 请求Promise
   */
  put: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  },
  
  /**
   * DELETE请求
   * @param {string} url - 请求URL
   * @param {object} data - 请求数据
   * @param {object} options - 请求选项
   * @returns {Promise} - 请求Promise
   */
  delete: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }
}

// 导出
export { request, http }
export default {
  request,
  http
} 