/**
 * Authentication interceptor
 * Handles token refresh and authentication state
 */
import { storage, api } from '../../config/index'
// Remove direct import to avoid circular dependency
// const { http } = require('../utils/request') 

// 全局状态管理
const authState = {
  // 是否正在刷新token
  isRefreshing: false,
  // 待处理的请求队列
  pendingRequests: [],
  // 刷新token的Promise，用于多个请求共享同一个刷新过程
  refreshPromise: null
}

/**
 * 处理待处理的请求队列
 * @param {string} token - 新token
 */
const processPendingRequests = (token) => {
  console.log(`处理待处理请求队列, 数量: ${authState.pendingRequests.length}`)
  
  // 处理所有待处理的请求
  authState.pendingRequests.forEach(request => {
    try {
      request.resolve(token)
    } catch (err) {
      console.error('处理待处理请求失败:', err)
      request.reject(err)
    }
  })
  
  // 清空队列
  authState.pendingRequests = []
}

/**
 * 添加授权头
 * @param {Object} headers - 请求头
 * @returns {Object} - 更新后的请求头
 */
export const addAuthHeader = (headers = {}) => {
  const token = wx.getStorageSync(storage.accessToken)
  if (token) {
    headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`
  }
  return headers
}

/**
 * 发送内部请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求数据
 * @returns {Promise} - 请求Promise
 */
const internalRequest = (url, data) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: /^https?:\/\//.test(url) ? url : `${api.baseUrl}${url}`,
      method: 'POST',
      data,
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data)
        } else {
          reject(res)
        }
      },
      fail: reject
    })
  })
}

/**
 * 刷新token
 * @returns {Promise<string>} - 返回新token的Promise
 */
const refreshAccessToken = () => {
  // 如果已经有一个刷新过程在进行，返回该过程的Promise
  if (authState.refreshPromise) {
    console.log('使用现有的刷新token过程')
    return authState.refreshPromise
  }
  
  console.log('开始新的刷新token过程')
  const refreshToken = wx.getStorageSync(storage.refreshToken)
  
  if (!refreshToken) {
    console.error('没有可用的刷新token')
    clearAuthAndRedirect('登录已过期，请重新登录')
    return Promise.reject(new Error('没有可用的刷新token'))
  }
  
  // 创建新的刷新Promise
  authState.refreshPromise = internalRequest(api.refreshTokenUrl, { refresh_token: refreshToken })
    .then(res => {
      if (res && res.access_token) {
        console.log('刷新token成功')
        wx.setStorageSync(storage.accessToken, res.access_token)
        if (res.refresh_token) {
          wx.setStorageSync(storage.refreshToken, res.refresh_token)
        }
        return res.access_token
      } else {
        throw new Error('无效的刷新token响应')
      }
    })
    .catch(err => {
      console.error('刷新token失败:', err)
      clearAuthAndRedirect('登录已过期，请重新登录')
      throw err
    })
    .finally(() => {
      // 清除刷新Promise
      authState.refreshPromise = null
    })
  
  return authState.refreshPromise
}

/**
 * 处理未授权错误
 * @param {Function} retry - 重试原始请求的函数
 * @returns {Promise} - 处理结果Promise
 */
export const handleUnauthorized = (retry) => {
  console.log('处理401未授权错误')
  
  // 如果已经在刷新，将请求添加到队列
  if (authState.isRefreshing) {
    console.log('token刷新进行中，将请求加入队列')
    return new Promise((resolve, reject) => {
      authState.pendingRequests.push({ 
        resolve: (token) => {
          console.log('从队列中执行请求')
          // 明确处理retry的Promise结果，确保结果传递给外部Promise
          retry(token)
            .then(result => {
              console.log('队列中的请求重试成功');
              resolve(result);
            })
            .catch(error => {
              console.error('队列中的请求重试失败:', error);
              reject(error);
            });
        },
        reject: (error) => {
          console.error('队列中请求被拒绝:', error);
          reject(error);
        }
      });
    });
  }
  
  // 标记开始刷新
  authState.isRefreshing = true
  
  // 开始刷新token流程
  return refreshAccessToken()
    .then(token => {
      // 处理队列中的请求
      processPendingRequests(token)
      
      // 重试当前请求
      console.log('用新token重试原始请求')
      return retry(token)
        .then(result => {
          console.log('重试请求成功');
          return result; // 明确返回结果
        })
        .catch(error => {
          console.error('重试请求失败:', error);
          throw error; // 显式重新抛出错误
        });
    })
    .catch(err => {
      console.error('处理未授权错误失败:', err)
      authState.isRefreshing = false; // 确保在出错时重置刷新状态
      throw err; // 显式重新抛出错误
    })
    .finally(() => {
      // 重置刷新状态
      authState.isRefreshing = false
    })
}

/**
 * 清除认证数据并重定向到登录页
 * @param {string} message - 显示的消息
 */
export function clearAuthAndRedirect(message = '请重新登录') {
  // 清除所有认证相关数据
  wx.removeStorageSync(storage.accessToken)
  wx.removeStorageSync(storage.refreshToken)
  wx.removeStorageSync(storage.userInfo)
  
  // 重置认证状态
  authState.isRefreshing = false
  authState.pendingRequests = []
  authState.refreshPromise = null
  
  // 显示提示
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
  
  // 延迟跳转，确保用户看到提示
  setTimeout(() => {
    wx.redirectTo({
      url: '/pages/login/index'
    })
  }, 2000)
}

// 默认导出
export default {
  handleUnauthorized,
  clearAuthAndRedirect,
  addAuthHeader
} 