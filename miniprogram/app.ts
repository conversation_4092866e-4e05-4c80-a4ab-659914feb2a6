// app.ts
import API from './api/index';
import { checkLoginAndInitialize, clearUserData } from './utils/init';
import { authService } from './service/auth/index';
import { initUserData } from './utils/init';

App({
  globalData: {
    userInfo: null,
    userAvatarAndName: null,
    isLoggedIn: false,
    systemInfo: null,
    sharedSessionId: null, // 添加分享的session_id
    API: API, // 将API添加到globalData中确保在组件中可以访问到
    authService: authService,
  },

  // 应用程序全局API对象
  API: API,

  // 全局事件系统
  globalEvent: {
    // 储存所有监听器
    _listeners: {},

    // 添加事件监听
    on: function (eventName: string | number, callback: any) {
      if (!this._listeners[eventName]) {
        this._listeners[eventName] = [];
      }
      this._listeners[eventName].push(callback);
    },

    // 移除事件监听
    off: function (eventName: string | number, callback: any) {
      if (!this._listeners[eventName]) return;

      if (callback) {
        this._listeners[eventName] = this._listeners[eventName].filter((listener: any) => listener !== callback);
      } else {
        // 如果没有指定回调，则移除该事件的所有监听器
        delete this._listeners[eventName];
      }
    },

    // 触发事件
    emit: function (eventName: string | number, data: any) {
      if (!this._listeners[eventName]) return;

      this._listeners[eventName].forEach((callback: (arg0: any) => void) => {
        try {
          callback(data);
        } catch (err) {
          console.error(`Event listener error for ${eventName}:`, err);
        }
      });
    },
  },

  onLaunch(options: any) {
    console.log('App onLaunch 被调用', new Date().toISOString());
    console.log('完整 options 参数:', options);

    // Get system info
    const systemInfo = wx.getSystemSetting();
    this.globalData.systemInfo = systemInfo;

    // 检查登录状态并初始化用户数据
    this.checkLoginStatusAndInit(options);
  },

  // 检查登录状态并初始化用户数据
  async checkLoginStatusAndInit(options: { query: { session_id: null } }) {
    // 检查是否为首次登录
    const isFirstLogin = !wx.getStorageSync('hasLoggedInBefore');
    if (isFirstLogin) {
      wx.setStorageSync('hasLoggedInBefore', true);
      console.log('用户首次登录');
    } else {
      wx.setStorageSync('hasLoggedInBefore', false);
      console.log('用户非首次登录');
    }
    try {
      // 检查登录状态并初始化用户数据
      console.log('App onLaunch: 检查登录状态并初始化用户数据');
      const isLoggedIn = await checkLoginAndInitialize();
      console.log('isLoggedIn', isLoggedIn);
      if (!isLoggedIn) {
        // 如果未登录，重定向到登录页
        console.log('未检测到有效的登录令牌，跳转到登录页面');
       
        wx.redirectTo({
          url: '/pages/interview/index',
        });
        return;
      }

      // 用户已登录，继续初始化应用程序
      console.log('用户已登录，继续初始化应用程序');

      // 初始化API确保在全局可用
      console.log('App onLaunch: API初始化', Object.keys(API));

      // 确保API在globalData中可用
      this.globalData.API = API;

      // 刷新API以确保最新的方法可用
      await this.refreshAPIModules();

      // 检查是否有分享的session_id参数
      if (options && options.query && options.query.session_id) {
        console.log('检测到分享链接，session_id:', options.query.session_id);
        // 保存到全局数据，而不是直接跳转
        this.globalData.sharedSessionId = options.query.session_id;
      }

      // 用户已登录且应用初始化完成，跳转到回忆录页面
      wx.reLaunch({
        url: '/pages/interview/index',
      });
    } catch (error) {
      console.error('初始化过程中出错:', error);
      // 出错时也跳转到登录页面
      wx.redirectTo({
        url: '/pages/interview/index',
      });
    }
  },

  // 刷新API模块
  async refreshAPIModules() {
    try {
      if (this.API && typeof this.API.refreshAPI === 'function') {
        console.log('App: 刷新API模块...');
        await this.API.refreshAPI();

        // 确保chat API模块已加载
        if (this.API.chat) {
          console.log('Chat API 模块已加载，方法列表:', Object.keys(this.API.chat));
        } else {
          console.error('Chat API 模块未能正确加载');
        }

        // 确保book API模块已加载
        if (this.API.book) {
          console.log('Book API 模块已加载');
        } else {
          console.error('Book API 模块未能正确加载');
        }

        // 更新全局数据中的API
        this.globalData.API = this.API;
      } else {
        console.warn('API刷新方法不可用');

        // 老的检查方式
        if (this.API.chat) {
          console.log('Chat API 模块已加载, getQuestion方法:', typeof this.API.chat.getQuestion);
        } else {
          console.error('Chat API 模块未能正确加载');
        }

        if (this.API.book) {
          console.log('Book API 模块已加载');
        } else {
          console.error('Book API 模块未能正确加载');
        }
      }
    } catch (error) {
      console.error('刷新API模块失败:', error);
    }
  },

  onShow(options: { query: { session_id: null } }) {
    console.log('App onShow 被调用', new Date().toISOString());
    console.log('完整 options 参数:', options);

    // 检查是否有登录令牌，如果没有则重定向到登录页面
    if (!API.isAuthenticated()) {
      console.log('未检测到登录令牌，需要登录');
      wx.redirectTo({
        url: '/pages/interview/index',
      });
      return;
    }

    // 检查是否有分享的session_id参数
    if (options && options.query && options.query.session_id) {
      console.log('检测到分享链接，session_id:', options.query.session_id);
      // 保存到全局数据，而不是直接跳转
      this.globalData.sharedSessionId = options.query.session_id;
    }
  },

  // 登出方法
  async logout() {
    try {
      // 尝试调用API登出
      try {
        if (this.API && this.API.auth && typeof this.API.auth.logout === 'function') {
          await this.API.auth.logout();
        }
      } catch (err) {
        console.warn('调用API登出方法失败，将直接清除本地数据', err);
      }

      // 关闭WebSocket连接
      if (this.API && this.API.websocket && typeof this.API.websocket.closeWebSocket === 'function') {
        this.API.websocket.closeWebSocket();
      }

      // 清除所有用户数据
      clearUserData();

      // 跳转到登录页
      wx.reLaunch({
        url: '/pages/login/index',
      });

      return { success: true };
    } catch (err) {
      console.error('登出过程中出错:', err);
      return { success: false, error: err.message || '登出失败' };
    }
  },

  lifetimes: {
    attached() {
      console.log('QuestionChainPanel: 组件attached');
      // 监听WebSocket消息处理对话回复
      this.setupWebSocketListener();
    },

    ready() {
      console.log('QuestionChainPanel: 组件ready');
      // if (this.properties.sessionId && !this.data.chainIds.length && !this.data.loading) {
      //   // 仅在sessionId存在、尚未加载数据且非加载状态下才加载问题链
      //   this.loadChainIds();
      // } // <--- 已移除此处的调用
    },

    detached() {
      console.log('QuestionChainPanel: 组件detached');
      // 注销WebSocket消息监听
      API.websocket.unregisterMessageListener('QuestionChainPanel');
    },
  },

  observers: {
    sessionId: function (sessionId: any) {
      // 现在初始化逻辑主要由 sessionId 的 observer 触发
      if (sessionId && !this.data.chainIds.length && !this.data.loading) {
        // 仅在sessionId存在、尚未加载数据且非加载中状态下才调用loadChainIds
        this.loadChainIds();
      }
    },

    // 当问题链状态发生变化时，更新已完成问题链数量
    chainStates: function (chainStates: any) {
      if (!chainStates) return;

      // 计算已完成的问题链数量
      this.updateCompletedChainCount();
    },
  },
});
