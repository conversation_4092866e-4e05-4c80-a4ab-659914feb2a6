<wxs src="../../utils/selectTextPlus.wxs" module="computed" />
<view 
  class="weui-select-text" 
  style="{{computed.containerStyle({showToolTip, showCopyBtn, activeBgColor})}}"
  catch:tap="stopPropagation"
>
  <view class="stp-container" style="{{textStyle}}">
    <block wx:if="{{sentences && sentences.length}}">
      <block wx:for="{{sentences}}" wx:key="index">
        <text 
          class="sentence {{computed.isSelected(index, selectionStart, selectionEnd) ? 'sentence--selected' : ''}}"
          data-idx="{{index}}"
          bindtap="onSentenceTap"
          catch:longpress="onSentenceLongPress"
        >{{item}}</text>
      </block>
    </block>
    <text wx:else class="sentence">{{value}}</text>
  </view>

  <view 
    wx:if="{{showToolTip}}"
    class="weui-tooltip weui-tooltip__{{placement}}"
    style="z-index: {{zIndex}};"
    catch:tap="stopPropagation"
  >
    <view class="tooltip-menu">
      <view class="tooltip-menu-item">
        <image class="tooltip-icon" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/WritingFluently.png" mode="aspectFit" />
        <view class="tooltip-item highlight" catch:tap="handleEnhance">润色</view>
      </view>
        
      <view class="tooltip-menu-item" bindtap="handleEdit">
        <van-icon class="tooltip-icon" size="34rpx" name="edit" />
        <view class="highlight">编辑</view>
      </view>
    </view>
  </view>
</view>