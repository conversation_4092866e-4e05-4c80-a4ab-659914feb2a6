Component({
  options: { addGlobalClass: true },
  properties: {
    space: { type: String, value: '' },
    decode: { type: Boolean, value: false },
    placement: { type: String, value: 'top' },
    showCopyBtn: { type: Boolean, value: true },
    value: { type: String, value: '' },
    zIndex: { type: Number, value: 99 },
    activeBgColor: { type: String, value: '#DEDEDE' },
    textStyle: { type: String, value: '' },
  },
  observers: {
    value: function (v) {
      const incoming = String(v || '');
      if (incoming === this.data.lastValue) return;
      this.splitSentences(incoming);
    },
  },
  data: {
    showToolTip: false,
    sentences: [],
    selectionStart: null,
    selectionEnd: null,
    lastValue: '',
  },
  lifetimes: {
    attached() {
      const incoming = String(this.data.value || '');
      this.splitSentences(incoming);
    },
  },
  methods: {
    splitSentences(text) {
      const raw = String(text || '');
      const input = raw.replace(/\r/g, '');
      // 线性分割：拆分为 [片段, 标点, 片段, 标点, ...] 再组合
      const tokens = input.split(/([。！？!?\n])/);
      const parts = [];
      for (let i = 0; i < tokens.length; i += 1) {
        const t = tokens[i];
        if (t === undefined || t === null) continue;
        if (/[。！？!?\n]/.test(t)) {
          // 标点独立时，与前一个拼接
          if (parts.length > 0) {
            parts[parts.length - 1] = parts[parts.length - 1] + t;
          } else {
            parts.push(t);
          }
        } else if (t.trim() !== '') {
          parts.push(t);
        }
      }
      const safe = parts.length ? parts : raw ? [raw] : [];
      this.setData({ sentences: safe, selectionStart: null, selectionEnd: null, showToolTip: false, lastValue: raw });
    },
    onSentenceTap(e) {
      const idx = Number(e.currentTarget.dataset.idx);
      const { selectionStart, selectionEnd } = this.data;
      if (selectionStart === null || selectionEnd === null) {
        this.setData({ selectionStart: idx, selectionEnd: idx });
      } else {
        this.setData({ selectionEnd: idx });
      }
    },
    onSentenceLongPress(e) {
      const idx = Number(e.currentTarget.dataset.idx);
      const { selectionStart, selectionEnd } = this.data;
      if (selectionStart === null || selectionEnd === null) {
        this.setData({ selectionStart: idx, selectionEnd: idx });
      }
      this.setData({ showToolTip: true });
    },
    getSelectedText() {
      const { sentences, selectionStart, selectionEnd, lastValue } = this.data;
      if (selectionStart === null || selectionEnd === null || sentences.length === 0) return lastValue || '';
      const start = Math.min(selectionStart, selectionEnd);
      const end = Math.max(selectionStart, selectionEnd);
      console.log('ssssss', sentences.slice(start, end + 1).join(''));
      return sentences.slice(start, end + 1).join('');
    },
    handleCopy() {
      const text = this.getSelectedText();
      this.setData({ showToolTip: false });
      wx.setClipboardData({ data: text });
      this.triggerEvent('copy', { text });
    },
    handleEnhance() {
      const text = this.getSelectedText();
      this.setData({ showToolTip: false });
      this.triggerEvent('enhance', { text });
    },
    stopPropagation() {},
    handleEdit(){
       this.triggerEvent('edit');
    }
  },
});
