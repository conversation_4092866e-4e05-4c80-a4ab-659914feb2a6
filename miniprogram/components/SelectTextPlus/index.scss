/* Base styles adapted from @miniprogram-component-plus/select-text */
.weui-select-text{width:100%;min-height:1em;position:relative;display:block}
.stp-container{display:bolck;white-space:pre-wrap;word-break:break-word;width: 100%;min-height: 1em;}
.sentence{display:inline}
.sentence--selected{background: rgba(255, 209, 102, 0.45)}

.weui-tooltip{position:absolute;background:#fff;color:#535150;font-size:30rpx;line-height:1;padding:6px 8px;border-radius:40rpx;box-shadow:0 2px 8px rgba(0,0,0,.15)}
.weui-tooltip__top{transform:translateY(-8px)}
.weui-tooltip__bottom{transform:translateY(8px)}
.weui-tooltip__left{transform:translateX(-8px)}
.weui-tooltip__right{transform:translateX(8px)}

/* Custom menu layout */
.tooltip-menu{display:flex;align-items:center}
.tooltip-item{
    margin-left: -50rpx;
}
.tooltip-icon{
    width: 32rpx;
    height: 32rpx;
}
.tooltip-menu-item {
    display: flex;
    align-items: center;
}