<!--components/TimeQuoteCard/index.wxml-->
<view class="{{computedClass}} {{customClass}}" bind:tap="onCardTap">
  
  <view class="time-quote-content">
    <view class="quote-text">
      "{{currentQuote.text}}"
    </view>
    
    <view class="quote-attribution" wx:if="{{currentQuote.author || currentQuote.source}}">
      <view> —— </view>
      <view class="quote-author" wx:if="{{currentQuote.author}}">
       {{currentQuote.author}}
      </view>
      <view class="quote-source" wx:if="{{currentQuote.source}}">
        《{{currentQuote.source}}》
      </view>
    </view>
  </view>
</view>
