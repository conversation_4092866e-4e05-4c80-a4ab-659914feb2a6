/* components/TimeQuoteCard/index.wxss */
.time-quote-card {
  border-radius: 16rpx;
  padding: 32rpx 0rpx;
  margin: 70rpx 0 0 0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  
  
  &.card-shadow {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  }
  
  // 主题样式
  &.theme-default {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
  
  &.theme-warm {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  }
  
  &.theme-elegant {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }
  
  &.theme-classic {
    background: linear-gradient(135deg, #e0c3fc 0%, #9bb5ff 100%);
  }
  
  // 头部区域
  .time-quote-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
    
    .time-quote-icon {
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 24rpx;
        line-height: 1;
      }

      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .time-quote-title {
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
      flex: 1;
      margin-left: 12rpx;
    }
    
    .time-quote-refresh {
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      // opacity: 0.6;
      transition: opacity 0.3s ease, transform 0.3s ease;

      &:active {
        opacity: 1;
        transform: rotate(180deg);
      }

      .icon-text {
        font-size: 24rpx;
        line-height: 1;
      }

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  // 内容区域
  .time-quote-content {
    .quote-text {
      font-size: 40rpx;
      line-height: 1.6;
      color: #282624CC;
      margin-bottom: 20rpx;
      text-align: center;
      font-weight: 400;
      letter-spacing: 1rpx;
      font-style: Huiwen-mincho;
    }
    
    .quote-attribution {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #2826244D;
      .quote-author {
        font-size: 24rpx;
        color: #2826244D;
        margin-bottom: 4rpx;
        // font-style: italic;
      }
      
      .quote-source {
        font-size: 24rpx;
        color: #2826244D;
        // font-style: italic;
      }
    }
  }
  
  // 装饰元素
  .time-quote-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24rpx;
    
    .decoration-line {
      width: 60rpx;
      height: 2rpx;
      background: linear-gradient(90deg, transparent, #ccc, transparent);
    }
    
    .decoration-dot {
      width: 8rpx;
      height: 8rpx;
      border-radius: 50%;
      background-color: #ccc;
      margin: 0 16rpx;
    }
  }
  
  // 背景装饰
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    // background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
  
  // 点击效果
  &:active {
    transform: scale(0.98);
  }
}

// 响应式适配
@media (max-width: 750rpx) {
  .time-quote-card {
    .time-quote-content {
      .quote-text {
        font-size: 30rpx;
      }
    }
  }
}


