Component({
  properties: {
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 自定义内联样式
    customStyle: {
      type: String,
      value: ''
    },
    // 是否显示边框
    bordered: {
      type: Boolean,
      value: true
    },
    // 是否有阴影
    shadow: {
      type: Boolean,
      value: true
    },
    // 自定义语句库
    customQuotes: {
      type: Array,
      value: []
    }
  },
  data: {
    currentQuote: {
      text: '',
      author: '',
      source: ''
    },
    // 时光相关的古诗词和文章语句库
    quotes: [
      {
        text: "逝者如斯夫，不舍昼夜。",
        author: "孔子",
        source: "论语"
      },
      {
        text: "花有重开日，人无再少年。",
        author: "关汉卿",
        source: "窦娥冤"
      },
      {
        text: "一寸光阴一寸金，寸金难买寸光阴。",
        author: "王贞白",
        source: "白鹿洞二首"
      },
      {
        text: "昨日之日不可留，今日之日多烦忧。",
        author: "李白",
        source: "宣州谢朓楼饯别校书叔云"
      },
      {
        text: "年年岁岁花相似，岁岁年年人不同。",
        author: "刘希夷",
        source: "代悲白头翁"
      },
      {
        text: "时光不老，我们不散。愿所有的美好都能在时间的长河中闪闪发光。",
        author: "佚名",
        source: "青春感悟"
      },
      {
        text: "落红不是无情物，化作春泥更护花。",
        author: "龚自珍",
        source: "己亥杂诗"
      },
      {
        text: "盛年不重来，一日难再晨。及时当勉励，岁月不待人。",
        author: "陶渊明",
        source: "杂诗"
      },
      {
        text: "青春是一本太仓促的书，我们含着眼泪，一读再读。",
        author: "席慕蓉",
        source: "青春"
      },
      {
        text: "少壮不努力，老大徒伤悲。",
        author: "佚名",
        source: "长歌行"
      },
      {
        text: "时光如水，总是无言。若你安好，便是晴天。",
        author: "白落梅",
        source: "时光深处的优雅"
      },
      {
        text: "黑发不知勤学早，白首方悔读书迟。",
        author: "颜真卿",
        source: "劝学诗"
      },
      {
        text: "时间是世界上一切成就的土壤。",
        author: "麦金西",
        source: "成功学"
      },
      {
        text: "流光容易把人抛，红了樱桃，绿了芭蕉。",
        author: "蒋捷",
        source: "一剪梅·舟过吴江"
      },
      {
        text: "当时明月在，曾照彩云归。",
        author: "晏几道",
        source: "临江仙·梦后楼台高锁"
      },
      {
        text: "想当年，金戈铁马，气吞万里如虎。",
        author: "辛弃疾",
        source: "永遇乐·京口北固亭怀古"
      },
    
    ],
    computedClass: ''
  },
  lifetimes: {
    attached() {
      this.selectRandomQuote();
      this._updateClassAndStyle();
    }
  },
  observers: {
    'bordered, shadow, theme': function() {
      this._updateClassAndStyle();
    },
    'customQuotes': function(newQuotes: any[]) {
      // 当自定义语句库发生变化时，重新选择语句
      if (newQuotes && newQuotes.length > 0) {
        this.selectRandomQuote();
      }
    }
  },
  methods: {
    // 随机选择一句话
    selectRandomQuote() {
      // 优先使用自定义语句库，否则使用默认语句库
      const quotes = this.data.customQuotes.length > 0 ? this.data.customQuotes : this.data.quotes;
      let randomIndex;

      // 获取上次显示的语句索引和时间
      const lastQuoteIndex = wx.getStorageSync('lastQuoteIndex') || -1;
      const lastQuoteTime = wx.getStorageSync('lastQuoteTime') || 0;
      const currentTime = Date.now();

      // 如果距离上次显示超过1小时，或者是第一次使用，则可以重复显示
      const oneHour = 60 * 60 * 1000;
      const canRepeat = (currentTime - lastQuoteTime) > oneHour;

      // 避免连续显示相同内容
      if (!canRepeat && quotes.length > 1) {
        do {
          randomIndex = Math.floor(Math.random() * quotes.length);
        } while (randomIndex === lastQuoteIndex);
      } else {
        randomIndex = Math.floor(Math.random() * quotes.length);
      }

      const selectedQuote = quotes[randomIndex];

      this.setData({
        currentQuote: selectedQuote
      });

      // 存储到本地
      wx.setStorageSync('lastQuoteIndex', randomIndex);
      wx.setStorageSync('lastQuoteTime', currentTime);
    },
    
    // 更新样式类
    _updateClassAndStyle() {
      let classNames = 'time-quote-card';
      
      if (this.data.bordered) {
        classNames += ' card-bordered';
      }
      
      // if (this.data.shadow) {
      //   classNames += ' card-shadow';
      // }
      
      this.setData({
        computedClass: classNames
      });
    },
    
    // 手动刷新语句
    refreshQuote() {
      this.selectRandomQuote();
      
      // 触发自定义事件
      this.triggerEvent('refresh', {
        quote: this.data.currentQuote
      });
    },
    
    // 点击卡片事件
    onCardTap() {
      this.triggerEvent('cardtap', {
        quote: this.data.currentQuote
      });
    }
  }
})
