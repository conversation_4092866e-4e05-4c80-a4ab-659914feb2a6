.custom-step {
  width: 100%;
  height: 380rpx;
  overflow-y: scroll;
  
  &.horizontal {
    .custom-steps {
      display: flex;
      // align-items: flex-start;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 0;
      
      .step-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        padding: 0 10rpx;
        
        .step-icon {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          // background: #f7f8fa;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 16rpx;
          position: relative;
          z-index: 2;
          
          .custom-icon {
            width: 32rpx;
            height: 32rpx;
            
            &.current-icon {
              width: 36rpx;
              height: 36rpx;
              transform: scale(1.1);
            }
          }
        }
        
        .step-content {
          text-align: center;
          flex: 1;
          
          .step-title {
            font-weight: 500;
            margin-bottom: 8rpx;
            line-height: 1.4;
            color: #282624E5;
          }
          
          .step-desc {
            color: #969799;
            line-height: 1.3;
          }
        }
        
        .step-line {
          position: absolute;
          top: 30rpx;
          left: 50%;
          width: calc(100% - 60rpx);
          height: 2rpx;
          z-index: 1;
        }
        
        &.active {
          .step-icon {
            // background: rgba(7, 193, 96, 0.1);
          }
        }
      }
    }
  }
  
  &.vertical {
    .custom-steps {
      display: flex;
      flex-direction: column;
      padding: 20rpx 0;
      
      .step-item {
        display: flex;
        align-items: flex-start;
        align-items: center;
        position: relative;
        padding: 20rpx 0;
        
        .step-icon {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          // background: #f7f8fa;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 24rpx;
          flex-shrink: 0;
          position: relative;
          z-index: 2;
          
          .custom-icon {
            width: 32rpx;
            height: 32rpx;
            
            &.current-icon {
              width: 36rpx;
              height: 36rpx;
              transform: scale(1.1);
            }
          }
        }
        
        .step-content {
          flex: 1;
          padding-top: 8rpx;
          display: flex;
          align-items: center;
          
          .step-title {
            font-weight: 500;
            // margin-bottom: 8rpx;
            margin-right: 8rpx;
            line-height: 1.4;
            color: #282624E5;
          }
          
          .step-desc {
            color: #969799;
            // line-height: 1.3;
          }
        }
        
        .step-line {
          position: absolute;
          left: 27rpx;
          top: 67rpx;
          width: 4rpx;
          height: 100%;
          z-index: 1;
        }
        
        &.active {
          .step-icon {
            // background: rgba(7, 193, 96, 0.1);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .custom-step {
    &.horizontal {
      .custom-steps {
        .step-item {
          .step-content {
            .step-title {
              font-size: 24rpx !important;
            }
            
            .step-desc {
              font-size: 20rpx !important;
            }
          }
        }
      }
    }
    
    &.vertical {
      .custom-steps {
        .step-item {
          .step-content {
            .step-title {
              font-size: 26rpx !important;
            }
            
            .step-desc {
              font-size: 22rpx !important;
            }
          }
        }
      }
    }
  }
}
