Component({
  properties: {
    // 步骤数据
    steps: {
      type: Array,
      value: []
    },
    // 当前激活的步骤索引
    active: {
      type: Number,
      value: 0
    },
    // 排列方向：horizontal | vertical
    direction: {
      type: String,
      value: 'horizontal'
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 是否显示步骤条
    showStepBar: {
      type: Boolean,
      value: true
    },
    // 步骤条颜色
    activeColor: {
      type: String,
      value: '#E5E5E5'
    },
    // 未激活颜色
    inactiveColor: {
      type: String,
      value: '#E5E5E5'
    },
    // 当前选项图标
    currentIcon: {
      type: String,
      value: ''
    },
    // 当前选项自定义图标
    customCurrentIcon: {
      type: String,
      value: ''
    },
    // 图标大小
    iconSize: {
      type: String,
      value: '22px'
    },
    // 标题字体大小
    titleSize: {
      type: String,
      value: '12px'
    },
    // 副标题字体大小
    descSize: {
      type: String,
      value: '12px'
    }
  },

  data: {
    // 当前步骤数据
    currentSteps: [] as any[],
    // 默认步骤数据
    defaultSteps: [
      {
        title: '步骤一',
        desc: '描述信息',
        icon: 'location-o',
        activeIcon: 'success',
        customIcon: '',
        customActiveIcon: ''
      },
      {
        title: '步骤二',
        desc: '描述信息',
        icon: 'like-o',
        activeIcon: 'plus',
        customIcon: '',
        customActiveIcon: ''
      },
      {
        title: '步骤三',
        desc: '描述信息',
        icon: 'star-o',
        activeIcon: 'cross',
        customIcon: '',
        customActiveIcon: ''
      },
      {
        title: '步骤四',
        desc: '描述信息',
        icon: 'phone-o',
        activeIcon: 'fail',
        customIcon: '',
        customActiveIcon: ''
      }
    ]
  },

  lifetimes: {
    attached() {
      this.updateCurrentSteps();
    }
  },

  observers: {
    'steps, active': function() {
      this.updateCurrentSteps();
    }
  },

  methods: {
    // 更新当前步骤数据
    updateCurrentSteps() {
      const currentSteps = this.data.steps.length > 0 ? this.data.steps : this.data.defaultSteps;
      this.setData({ currentSteps });
    },

    // 点击步骤事件
    onStepClick(e: any) {
      const { index } = e.currentTarget.dataset;
      this.triggerEvent('stepclick', { index, step: this.data.currentSteps[index] });
    },

    // 验证图片URL是否有效
    isValidImageUrl(url: string): boolean {
      if (!url) return false;
      
      // 支持本地路径、网络图片、base64等格式
      const validPatterns = [
        /^https?:\/\//,           // http/https
        /^\/\//,                  // 协议相对路径
        /^\/[^/]/,                // 绝对路径
        /^\.\//,                  // 相对路径
        /^data:image\//,          // base64
        /^cloud:\/\//,            // 云开发路径
        /^wxfile:\/\//,           // 微信文件路径
        /^wxlocalresource:\/\//   // 微信本地资源路径
      ];
      
      return validPatterns.some(pattern => pattern.test(url));
    },

    // 处理图片加载错误
    onImageError(e: any) {
      const { index, type } = e.currentTarget.dataset;
      console.warn(`图片加载失败: ${type} at index ${index}`, e.detail);
      
      // 可以在这里设置默认图标
      // this.setData({
      //   [`currentSteps[${index}].${type}`]: '/assets/images/default-icon.png'
      // });
    },

    // 处理图片加载成功
    onImageLoad(e: any) {
      const { index, type } = e.currentTarget.dataset;
      console.log(`图片加载成功: ${type} at index ${index}`);
    }
  }
});
