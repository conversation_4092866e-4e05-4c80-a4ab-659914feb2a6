# Step 步骤条组件

一个功能完整的步骤条组件，支持自定义图标、标题、副标题、横向/竖向排列和自定义样式。

## 功能特性

- ✅ 支持自定义图标（Vant图标或自定义图片）
- ✅ 支持标题和副标题
- ✅ 支持横向和竖向排列
- ✅ 支持自定义样式
- ✅ 支持点击事件
- ✅ 支持激活状态控制
- ✅ 响应式设计

## 使用方法

### 1. 基础用法

```wxml
<!-- 使用默认数据 -->
<Step active="{{ 1 }}" />

<!-- 使用自定义数据 -->
<Step 
  steps="{{ steps }}" 
  active="{{ active }}" 
  bind:stepclick="onStepClick"
/>
```

### 2. 横向排列（默认）

```wxml
<Step 
  direction="horizontal"
  steps="{{ steps }}" 
  active="{{ 1 }}"
/>
```

### 3. 竖向排列

```wxml
<Step 
  direction="vertical"
  steps="{{ steps }}" 
  active="{{ 1 }}"
/>
```

### 4. 自定义样式

```wxml
<Step 
  steps="{{ steps }}" 
  active="{{ 1 }}"
  customStyle="background: #f5f5f5; padding: 20rpx; border-radius: 8rpx;"
  activeColor="#1989fa"
  inactiveColor="#c8c9cc"
  iconSize="24px"
  titleSize="16px"
  descSize="12px"
/>
```

### 5. 自定义图标

```wxml
<Step 
  steps="{{ customIconSteps }}" 
  active="{{ 1 }}"
/>
```

### 6. 当前选项图标

```wxml
<!-- 使用Vant图标作为当前选项图标 -->
<Step 
  steps="{{ steps }}" 
  active="{{ 1 }}"
  currentIcon="fire-o"
/>

<!-- 使用自定义图片作为当前选项图标 -->
<Step 
  steps="{{ steps }}" 
  active="{{ 1 }}"
  customCurrentIcon="https://example.com/current-icon.png"
/>
```

## 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| steps | Array | [] | 步骤数据数组 |
| active | Number | 0 | 当前激活的步骤索引 |
| direction | String | 'horizontal' | 排列方向：horizontal \| vertical |
| customStyle | String | '' | 自定义样式 |
| showStepBar | Boolean | true | 是否显示步骤条 |
| activeColor | String | '#07c160' | 激活状态颜色 |
| inactiveColor | String | '#969799' | 未激活状态颜色 |
| iconSize | String | '22px' | 图标大小 |
| titleSize | String | '14px' | 标题字体大小 |
| descSize | String | '12px' | 副标题字体大小 |
| currentIcon | String | '' | 当前选项的Vant图标名 |
| customCurrentIcon | String | '' | 当前选项的自定义图标URL |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| stepclick | 点击步骤时触发 | { index, step } |

## 数据结构

### steps 数组项结构

```javascript
{
  title: '步骤标题',           // 必填，步骤标题
  desc: '步骤描述',            // 可选，步骤描述
  icon: 'location-o',         // 可选，未激活时的Vant图标名
  activeIcon: 'success',      // 可选，激活时的Vant图标名
  customIcon: '',             // 可选，未激活时的自定义图标URL
  customActiveIcon: ''        // 可选，激活时的自定义图标URL
}
```

### 支持的图片格式

自定义图标支持以下格式：

1. **网络图片**
   - HTTPS: `https://example.com/icon.png`
   - HTTP: `http://example.com/icon.png`
   - 协议相对: `//example.com/icon.png`

2. **本地图片**
   - 绝对路径: `/assets/images/icon.png`
   - 相对路径: `./images/icon.png`

3. **Base64图片**
   - `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...`

4. **微信小程序特殊路径**
   - 云开发: `cloud://xxx`
   - 微信文件: `wxfile://xxx`
   - 本地资源: `wxlocalresource://xxx`

## 使用示例

### JavaScript 数据

```javascript
Page({
  data: {
    active: 1,
    steps: [
      {
        title: '提交订单',
        desc: '订单已提交，等待确认',
        icon: 'orders-o',
        activeIcon: 'success'
      },
      {
        title: '支付完成',
        desc: '支付成功，等待发货',
        icon: 'paid',
        activeIcon: 'success'
      },
      {
        title: '商品发货',
        desc: '商品已发出',
        icon: 'logistics',
        activeIcon: 'success'
      },
      {
        title: '确认收货',
        desc: '请确认收货',
        icon: 'completed',
        activeIcon: 'success'
      }
    ],
    customIconSteps: [
      {
        title: '本地图标',
        desc: '使用本地图片作为图标',
        customIcon: '/images/inactive-icon.png',
        customActiveIcon: '/images/active-icon.png'
      },
      {
        title: '网络图标',
        desc: '使用HTTPS图片作为图标',
        customIcon: 'https://img.icons8.com/ios/50/000000/circle.png',
        customActiveIcon: 'https://img.icons8.com/ios-filled/50/000000/checkmark.png'
      }
    ],
    // 当前选项图标示例
    currentIconSteps: [
      {
        title: '步骤一',
        desc: '开始处理',
        icon: 'location-o',
        activeIcon: 'success'
      },
      {
        title: '步骤二',
        desc: '处理中',
        icon: 'like-o',
        activeIcon: 'plus'
      },
      {
        title: '步骤三',
        desc: '即将完成',
        icon: 'star-o',
        activeIcon: 'cross'
      },
      {
        title: '步骤四',
        desc: '完成',
        icon: 'phone-o',
        activeIcon: 'fail'
      }
    ]
  },

  onStepClick(e) {
    const { index, step } = e.detail;
    console.log('点击步骤:', index, step);
    this.setData({ active: index });
  }
});
```

### 完整示例

```wxml
<view class="container">
  <!-- 横向步骤条 -->
  <view class="section">
    <view class="title">横向步骤条</view>
    <Step 
      direction="horizontal"
      steps="{{ steps }}" 
      active="{{ active }}"
      bind:stepclick="onStepClick"
    />
  </view>

  <!-- 竖向步骤条 -->
  <view class="section">
    <view class="title">竖向步骤条</view>
    <Step 
      direction="vertical"
      steps="{{ steps }}" 
      active="{{ active }}"
      customStyle="background: #f8f9fa; padding: 20rpx; border-radius: 12rpx;"
      activeColor="#1989fa"
      inactiveColor="#c8c9cc"
    />
  </view>

  <!-- 自定义图标步骤条 -->
  <view class="section">
    <view class="title">自定义图标</view>
    <Step 
      steps="{{ customIconSteps }}" 
      active="{{ 0 }}"
      iconSize="28px"
      titleSize="16px"
      descSize="14px"
    />
  </view>

  <!-- 当前选项图标步骤条 -->
  <view class="section">
    <view class="title">当前选项图标</view>
    <Step 
      steps="{{ currentIconSteps }}" 
      active="{{ 1 }}"
      currentIcon="fire-o"
      customCurrentIcon="https://img.icons8.com/ios-filled/50/ff6b35/fire-element.png"
      iconSize="28px"
      titleSize="16px"
      descSize="14px"
    />
  </view>
</view>
```

## 注意事项

1. 如果同时设置了 `icon` 和 `customIcon`，优先使用 `customIcon`
2. 如果同时设置了 `activeIcon` 和 `customActiveIcon`，优先使用 `customActiveIcon`
3. 如果同时设置了 `currentIcon` 和 `customCurrentIcon`，优先使用 `customCurrentIcon`
4. 当前选项图标优先级：`customCurrentIcon` > `currentIcon` > `item.activeIcon` > `item.icon`
5. 自定义图标建议使用 PNG 或 SVG 格式，大小建议 32x32 像素
6. 竖向排列时，建议控制步骤数量，避免页面过长
7. 响应式设计会自动在小屏幕设备上调整字体大小 