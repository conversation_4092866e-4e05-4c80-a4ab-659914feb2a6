<view class="custom-step {{ direction }}" style="{{ customStyle }}">
  <!-- 使用vant-steps组件作为基础 -->
  <van-steps 
    wx:if="{{ showStepBar }}"
    steps="{{ currentSteps }}" 
    active="{{ active }}"
    direction="{{ direction }}"
    active-color="{{ activeColor }}"
    inactive-color="{{ inactiveColor }}"
    bind:click-step="onStepClick"
  />
  
  <!-- 自定义步骤组件 -->
  <view wx:else class="custom-steps">
    <view 
      wx:for="{{ currentSteps }}" 
      wx:key="index"
      class="step-item {{ index <= active ? 'active' : '' }} {{ direction }}"
      data-index="{{ index }}"
      bind:tap="onStepClick"
    >
      <!-- 步骤图标 -->
      <view class="step-icon" style="font-size: {{ iconSize }};">
        <!-- 当前选项自定义图标 -->
        <image 
          wx:if="{{ index === active && customCurrentIcon }}"
          class="custom-icon current-icon"
          src="{{ customCurrentIcon }}"
          mode="aspectFit"
          data-index="{{ index }}"
          data-type="customCurrentIcon"
          bind:error="onImageError"
          bind:load="onImageLoad"
        />
        <!-- 激活状态自定义图标 -->
        <image 
          wx:elif="{{ index <= active && item.customActiveIcon }}"
          class="custom-icon"
          src="{{ item.customActiveIcon }}"
          mode="aspectFit"
          data-index="{{ index }}"
          data-type="customActiveIcon"
          bind:error="onImageError"
          bind:load="onImageLoad"
        />
        <!-- 未激活状态自定义图标 -->
        <image 
          wx:elif="{{ index > active && item.customIcon }}"
          class="custom-icon"
          src="{{ item.customIcon }}"
          mode="aspectFit"
          data-index="{{ index }}"
          data-type="customIcon"
          bind:error="onImageError"
          bind:load="onImageLoad"
        />
        <!-- 当前选项Vant图标 -->
        <van-icon 
          wx:elif="{{ index === active && currentIcon }}"
          name="{{ currentIcon }}"
          color="{{ activeColor }}"
        />
        <!-- 激活状态Vant图标 -->
        <van-icon 
          wx:elif="{{ index <= active && (item.activeIcon || item.icon) }}"
          name="{{ item.activeIcon || item.icon }}"
          color="{{ activeColor }}"
        />
        <!-- 未激活状态Vant图标 -->
        <van-icon 
          wx:else
          name="{{ item.icon }}"
          color="{{ inactiveColor }}"
        />
      </view>
      
      <!-- 步骤内容 -->
      <view class="step-content">
        <view 
          class="step-title" 
          style="font-size: {{ titleSize }};"
        >
          {{ item.title }}
        </view>
       
        <view 
          wx:if="{{ item.desc }}"
          class="step-desc" 
          style="font-size: {{ descSize }};"
        >
        ·{{ item.desc }}
        </view>
      </view>
      
      <!-- 连接线 -->
      <view 
        wx:if="{{ index < currentSteps.length - 1 }}"
        class="step-line {{ direction }}"
        style="background-color: {{ index <= active ? activeColor : inactiveColor }};"
      ></view>
    </view>
  </view>
</view>