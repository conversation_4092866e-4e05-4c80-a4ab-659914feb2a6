.user-guide {
  .guide-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .guide-step {
    position: absolute;
    display: none;
    
    &.active {
      display: block;
    }

    .highlight-area {
      // border: 3px solid #F5A630;
      border-radius: 12px;
      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
      position: relative;
      z-index: 10000;
      background: transparent;
      pointer-events: none;
    }

    .guide-content {
      position: absolute;
      border-radius: 16px;
      max-width: 500px;
      z-index: 10001;
      height:150px;
      width: 260px;
      
      image {
        width: 100%;
        height: 150px;
      }

      .guide-title {
        font-size: 32px;
        font-weight: 600;
        color: #282624;
        margin-bottom: 16px;
      }

      .guide-desc {
        font-size: 28px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 32px;
      }

      .guide-actions {
        display: flex;
        gap: 16px;
        justify-content: flex-end;

        .guide-btn {
          padding: 16px 32px;
          border-radius: 8px;
          font-size: 28px;
          cursor: pointer;
          transition: all 0.3s ease;

          &.prev-btn {
            background: #f5f5f5;
            color: #666;
          }

          &.next-btn {
            background: #F5A630;
            color: #fff;
          }

          &.finish-btn {
            background: #F5A630;
            color: #fff;
          }
        }
      }
    }

    .skip-btn {
      position: absolute;
      top: -60px;
      right: 0;
      background: rgba(255, 255, 255, 0.9);
      color: #999;
      padding: 12px 24px;
      border-radius: 20px;
      font-size: 24px;
      cursor: pointer;
      z-index: 10002;
    }
  }
} 