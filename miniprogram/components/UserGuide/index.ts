Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  data: {
    currentStep: 0,
    guideSteps: [
      {
        title: '人生阶段卡片',
        description: '滑动查看不同的人生阶段，点击话题开始记录您的故事',
        left: 80,
        top: 560,
        width: 590,
        height: 850,
        contentLeft: -20,
        contentTop: -300
      }
    ]
  },

  lifetimes: {
    attached() {
      // 组件加载时延迟计算位置，确保页面完全渲染
      setTimeout(() => {
        // this.calculateCardPosition();
        this.setGuideRect(null)
      }, 200);
    }
  },

  methods: {

    // 完成指引
    finishGuide() {
      this.setData({
        visible: false,
        currentStep: 0
      });
      // 标记用户已完成指引
      wx.setStorageSync('userGuideCompleted', true);
      this.triggerEvent('guideComplete');
    },

    setGuideRect(rect) {
      // rect: {left, top, width, height}
      console.log("rect: ", rect);
      if(rect){
        this.setData({
          'guideSteps[0].left': rect.left,
          'guideSteps[0].top': rect.top,
          'guideSteps[0].width': rect.width,
          'guideSteps[0].height': rect.height,
          'guideSteps[0].contentLeft': (rect.width - 260) / 2,
          'guideSteps[0].contentTop': -100
        });
      }else {
         this.setData({
            'guideSteps[0].left': 40,
            'guideSteps[0].top': 250,
            'guideSteps[0].width': 300,
            'guideSteps[0].height': 450,
            'guideSteps[0].contentLeft': 0,
            'guideSteps[0].contentTop': -100
          });
      }
     
    }
  }
}); 