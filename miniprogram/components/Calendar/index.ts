Component({
  data: {
    currentYear: '',
    currentMonth: '',
    dateList: [] as Array<{
      date: number,
      dayName: string,
      isToday: boolean,
      fullDate: string
    }>
  },

  lifetimes: {
    attached() {
      this.initCalendar();
    }
  },

  methods: {
    initCalendar() {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;

      // 设置年月显示
      this.setData({
        currentYear: `${currentYear}年`,
        currentMonth: `${currentMonth}月`
      });

      // 生成前后2天的日期数组
      const dateList = [];
      const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      const dayNamesZh = ['日', '一', '二', '三', '四', '五', '六'];

      for (let i = -2; i <= 2; i++) {
        const targetDate = new Date(now);
        targetDate.setDate(now.getDate() + i);

        const date = targetDate.getDate();
        const dayIndex = targetDate.getDay();
        const dayName = dayNamesZh[dayIndex];
        const isToday = i === 0;
        const fullDate = `${targetDate.getFullYear()}-${String(targetDate.getMonth() + 1).padStart(2, '0')}-${String(date).padStart(2, '0')}`;

        dateList.push({
          date,
          dayName,
          isToday,
          fullDate
        });
      }

      this.setData({
        dateList
      });
    },

    onDateTap() {
      wx.navigateTo({
        url:'/pages/huangLi/index'
      })
    }
  }
});