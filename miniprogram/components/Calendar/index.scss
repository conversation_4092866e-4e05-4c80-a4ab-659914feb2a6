.calendar-container {
  position: relative;

  .calendar-container-bg{
    position: absolute;
    width: 100%;
    height: 416rpx;
    top: -140rpx;
  }
  .calendar-header {
    text-align: center;
    margin-bottom: 48rpx;
    margin-top: 80rpx;
    .year-month {
      font-size: 34rpx;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.9);
      letter-spacing: 1rpx;
      font-family: PingFang SC;
    }
  }

  .date-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8rpx;
    padding: 0 80rpx;
    .date-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8rpx;
      border-radius: 16rpx;
      transition: all 0.3s ease;
      cursor: pointer;
      min-height: 140rpx;
      justify-content: center;
      &.today {
        background: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/cal.png') no-repeat center;
        background-size: 100% 100%;
        color: white;
        transform: scale(1.05);
        width: 230rpx;
        height: 230rpx;
        .date-number {
          color: #1a1a1a;
          font-weight: 800;
          font-size: 72rpx;
        }

        .day-name {
          color: #1a1a1a;
          font-weight: 500;
          font-size: 28rpx;
        }
      }

      .date-number {
        font-size: 40rpx;
        font-weight: 500;
        color: rgba(40, 38, 36, 0.24);
        line-height: 1;
        margin-bottom: 12rpx;
      }

      .day-name {
        font-size: 26rpx;
        color: rgba(40, 38, 36, 0.24);
        line-height: 1;
        font-weight: 500;
      }
    }
  }
}