// 书本组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 默认封面
    defaultCover: {
      type: String,
      value: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/3841752820885_.pic.jpg',
    },
    bookCover:{
      type: String,
      value: '',
    },
    bookContent:{
      type: Array,
      value: [],
    },
    bookUpdateTime:{
      type: String,
      value: '',
    },
    bookTitle:{
      type: String,
      value: '',
    },
    totalCount:{
      type: String,
      value: '',
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 选中的图片
    customCoverUrl: '',
    // 最终显示的封面
    finalCover: '',
    nickName:''
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('BookCard attached, bookUpdateTime:', this.properties.bookUpdateTime);
      this.updateFinalCover();
      this.setData({ bookUpdateTime: this.properties.bookUpdateTime,bookTitle:this.properties.bookTitle,totalCount:this.properties.totalCount });
      this.setData({ nickName:wx.getStorageSync('userAvatarAndName').username });
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'bookUpdateTime': function(newVal) {
      console.log('bookUpdateTime observer triggered:', newVal);
      if (this.data.bookUpdateTime !== newVal) {
        this.setData({ bookUpdateTime: newVal });
      }
    },
    'bookCover, customCoverUrl': function() {
      this.updateFinalCover();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新最终显示的封面
     */
    updateFinalCover() {
      const finalCover = this.data.customCoverUrl || this.properties.bookCover || this.properties.defaultCover;
      this.setData({ finalCover });
    },

    /**
     * 从相机选择图片
     */
    onChooseFromCamera() {
      // 获取 session_id
      const sessionId = wx.getStorageSync('activeChatSession');
      if (!sessionId) {
        wx.showToast({
          title: '无法更换封面',
          icon: 'none',
        });
        return;
      }
      wx.showActionSheet({
        itemList: ['拍照', '从相册选择'],
        success:() => {
          wx.chooseMedia({
            itemList: ['拍照', '从相册选择'],
            count: 1,
            mediaType: ['image'],
            sourceType: ['album', 'camera'],
            camera: 'back',
            success: (res) => {
              const tempFilePath = res.tempFiles[0].tempFilePath;
              // 压缩图片
              wx.compressImage({
                src: tempFilePath,
                quality: 80,
                success: (compressRes) => {
                  // 转 base64
                  wx.getFileSystemManager().readFile({
                    filePath: compressRes.tempFilePath,
                    encoding: 'base64',
                    success: (readRes) => {
                      // 获取图片格式
                      const format = compressRes.tempFilePath.split('.').pop()?.toLowerCase() || 'jpg';
                      // 上传
                      const app = getApp();
                      app.API.book
                        .uploadBookCover({
                          session_id: sessionId,
                          image_file_base64: readRes.data,
                          image_format: format,
                        })
                        .then(() => {
                          wx.showToast({ title: '封面已上传', icon: 'success' });
                          // 本地更新封面
                          this.setData({ customCoverUrl: compressRes.tempFilePath });
                          wx.setStorageSync(`book_cover_${sessionId}`, compressRes.tempFilePath);
                          // 更新最终封面
                          this.updateFinalCover();
                        })
                        .catch(() => {
                          wx.showToast({ title: '上传失败', icon: 'none' });
                        });
                    },
                    fail: () => {
                      wx.showToast({ title: '图片读取失败', icon: 'none' });
                    },
                  });
                },
                fail: () => {
                  wx.showToast({ title: '图片压缩失败', icon: 'none' });
                },
              });
            },
            fail: () => {
              wx.showToast({ title: '选择图片失败', icon: 'none' });
            },
          });
        },
      });
    },
  },
});
