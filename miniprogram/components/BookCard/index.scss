/* 书本组件样式 */
.book-card {
  border-radius: 12rpx;
  padding: 20rpx;
  // box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  position: relative;
}

/* 封面容器 */
.book-cover-container {
  display: flex;
  position: relative;
  z-index: 2;
}

.book-cover-img {
  position: relative;
}

.book-cover {
  width: 212rpx;
  height: 290rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  margin-right: 20rpx;
}

.cover-overlay {
  position: absolute;
  top: 25rpx;
  left: -8rpx;
  right: 0;
  margin: auto;
  display: flex;
  justify-content: center;
  color: #fff;
  padding: 8rpx 12rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.overlay-text {
  color: #fff;
  font-size: 20rpx;
}

/* 更换封面按钮 */
.changeBtn {
  width: 160rpx;
  margin-left: 20rpx;
  position: relative;
  z-index: 2;

  image {
    width: 160rpx;
    height: 64rpx;
  }

}

/* 书本信息 */
.book-info {
  width: calc(100% - 50rpx);
  background-color: #EBE7E1;
  height: 184rpx;
  border-radius: 16rpx;
  position: absolute;
  bottom: 0rpx;
  right: -6rpx;
  padding-right: 60rpx;
}

.book-title {
  max-width:350rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin: 12rpx 0;
  line-height: 1.4;
}

.book-author {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  margin-left: 18rpx;
}

.author-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #999;
  margin-left: 8rpx;
}

.book-update {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  margin-left: 18rpx;
}

.update-text {
  font-size: 26rpx;
  color: #999;
  margin-left: 8rpx;
}

/* 统计数据 */
.book-stats {
  height: 100%;
  display: flex;
  width: 60%;
  float: right;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 40rpx;
  font-weight: 600;
  color: #28262499;
  line-height: 1.2;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 更换封面弹窗样式 */
.cover-popup {
  padding: 32rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  padding: 8rpx;
}

/* 图片裁剪区域 */
.crop-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.crop-area {
  position: relative;
  width: 100%;
  height: 400rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
}

.crop-image {
  width: 100%;
  height: 100%;
}

.crop-box {
  position: absolute;
  border: 2rpx solid #ff6b35;
  background: rgba(255, 107, 53, 0.1);
  min-width: 80rpx;
  min-height: 80rpx;
}

.crop-handle {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #ff6b35;
  border: 2rpx solid #fff;
  border-radius: 50%;
}

.crop-handle-tl {
  top: -10rpx;
  left: -10rpx;
}

.crop-handle-tr {
  top: -10rpx;
  right: -10rpx;
}

.crop-handle-bl {
  bottom: -10rpx;
  left: -10rpx;
}

.crop-handle-br {
  bottom: -10rpx;
  right: -10rpx;
}

.save-btn {
  margin-top: auto;
  background: #ff6b35 !important;
  border: none !important;
  border-radius: 8rpx !important;
  height: 80rpx !important;
  line-height: 80rpx !important;
  font-size: 28rpx !important;
}

/* 图片选择区域 */
.image-selector {
  flex: 1;
}

.camera-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  margin-bottom: 32rpx;
}

.camera-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8rpx;
  overflow: hidden;
  border: 2rpx solid transparent;
}

.image-item.selected {
  border-color: #ff6b35;
}

.preset-image {
  width: 100%;
  height: 100%;
}

.selected-mark {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 预览区域 */
.preview-container {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid #eee;
}

.preview-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.preview-box {
  width: 200rpx;
  height: 240rpx;
  border: 2rpx solid #ff6b35;
  border-radius: 8rpx;
  overflow: hidden;
  margin: 0 auto;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.book-card-dash {
  width: 100%;
  height: 2rpx;
  margin: 40rpx 0 40rpx 0;
  background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/dash_1.png');
  background-size: 100%;
}