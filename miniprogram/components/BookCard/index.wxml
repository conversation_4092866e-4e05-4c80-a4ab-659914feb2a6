<!-- 书本组件 -->
<view class="book-card">
  <!-- 书本封面 -->
  <view class="book-cover-container">
    <view class="book-cover-img">
      <image class="book-cover" src="{{finalCover}}" mode="aspectFill" bind:tap="onCoverTap" />
      <view class="cover-overlay">
        <text class="overlay-text">{{bookTitle || '我的回忆录'}}</text>
      </view>
    </view>
    <view>
      <!-- 书名 -->
      <view class="book-title">{{bookTitle|| '《我的回忆录》'}}</view>

      <!-- 作者信息 -->
      <view class="book-author">
        <van-icon name="user-o" size="14px" color="#999" />
        <text class="author-text">作者：{{bookData.author || nickName}}</text>
      </view>

      <!-- 更新时间 -->
      <view class="book-update">
        <van-icon name="clock-o" size="14px" color="#999" />
        <text class="update-text">{{bookUpdateTime || ''}}最近更新</text>
      </view>
    </view>
  </view>

  <!-- 更换封面按钮 -->

  <view bind:tap="onChangeCover" class="changeBtn" bind:tap="onChooseFromCamera">
    <image src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/btn.png" mode="" />
  </view>


  <!-- 书本信息 -->
  <view class="book-info">

    <!-- 统计数据 -->
    <view class="book-stats">
      <view class="stat-item">
        <text class="stat-number">{{bookContent.length || '0'}}</text>
        <text class="stat-label">章节</text>
      </view>
      <!-- <view class="stat-item">
        <text class="stat-number">{{bookData.pages || }}</text>
        <text class="stat-label">总页数</text>
      </view> -->
      <view class="stat-item">
        <text class="stat-number">{{totalCount || '0'}}</text>
        <text class="stat-label">累计字数</text>
      </view>
    </view>
  </view>
</view>

<view class="book-card-dash"></view>

