.book-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f6f6f6;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom); /* 适配iPhone底部安全区域 */
}

/* PDF容器 */
.pdf-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f0f0f0;
}

/* 悬浮按钮样式 */
.floating-btn, .floating-refresh-btn {
  position: fixed;
  z-index: 100;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #4a6bff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.floating-btn {
  bottom: 40rpx;
  left: 40rpx;
}

.floating-refresh-btn {
  bottom: 40rpx;
  right: 40rpx;
  background-color: #25b864;
}

.floating-btn:active, .floating-refresh-btn:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.floating-btn-icon {
  font-size: 26rpx;
  font-weight: bold;
}

/* Web-view样式 */
web-view {
  width: 100%;
  height: 100%;
}

/* 加载中样式 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f6f6f6;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid rgba(74, 107, 255, 0.2);
  border-top-color: #4a6bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 30rpx;
  color: #333;
}

/* 错误样式 */
.error-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f6f6f6;
  padding: 40rpx;
  box-sizing: border-box;
}

.error-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #ff4a6b;
  color: white;
  font-size: 70rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.error-subtext {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.book-content {
  width: 100%;
  min-height: 200rpx;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f9f6f2;
}

.loading {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx 0;
}

.book-header {
  margin-bottom: 40rpx;
  text-align: right;
}

.book-date {
  font-size: 24rpx;
  color: #888;
  font-style: italic;
}

.book-chapters {
  margin-bottom: 40rpx;
}

.chapter {
  margin-bottom: 50rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.chapter-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #eee;
  padding-bottom: 16rpx;
}

.chapter-intro {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.subchapters {
  margin-left: 20rpx;
}

.subchapter {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-left: 4rpx solid #e29c7a;
  border-radius: 0 8rpx 8rpx 0;
}

.subchapter-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #444;
  margin-bottom: 12rpx;
}

.subchapter-intro {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.back-btn-container {
  margin-top: 40rpx;
  text-align: center;
}

.back-btn {
  background-color: #e29c7a;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 60rpx;
  border-radius: 40rpx;
  display: inline-block;
  margin: 0 auto;
}

/* 书本翻页区域 */
.flip-book {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 翻页按钮 */
.nav-button {
  position: absolute;
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(226, 196, 152, 0.7);
  border-radius: 50%;
  z-index: 10;
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  top: 50%;
  transform: translateY(-50%);
}

.nav-button.prev {
  left: 20rpx;
}

.nav-button.next {
  right: 20rpx;
}

.nav-button.disabled {
  opacity: 0.3;
  pointer-events: none;
}

.arrow-left, .arrow-right {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #fff;
  border-right: 4rpx solid #fff;
}

.arrow-left {
  transform: rotate(-135deg);
}

.arrow-right {
  transform: rotate(45deg);
}

/* Swiper样式 */
.book-swiper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 页面共享样式 */
.page {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  border-radius: 2rpx;
  margin: 30rpx;
  overflow: hidden;
  position: relative;
  aspect-ratio: 1/1.414;
  max-height: 90%;
  width: calc(90% * 0.707);
  box-sizing: border-box;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  /* 添加古典书籍风格的纸张纹理 */
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23f0ebe2' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E"),
    linear-gradient(rgba(0, 0, 0, 0.01) 1px, transparent 1px);
  background-size: 300px 300px, 100% 32rpx;
  background-position: 0 0, 0 16rpx;
}

/* 封面页样式 */
.cover-page {
  background: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
  transform: perspective(1000px) rotateY(0deg);
}

/* 书籍封面边框 */
.cover-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 6rpx solid #a67c52;
  box-shadow: 0 0 0 2rpx rgba(255, 255, 255, 0.2) inset, 
              0 4rpx 10rpx rgba(0, 0, 0, 0.3),
              0 20rpx 40rpx -20rpx rgba(0, 0, 0, 0.4);
  pointer-events: none;
  z-index: 5;
  border-radius: 0;
  background: linear-gradient(to bottom, rgba(166, 124, 82, 0.1), rgba(166, 124, 82, 0.05));
}

.cover-border::after {
  content: "";
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  right: 10rpx;
  bottom: 10rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  z-index: 1;
}

.cover-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.cover-overlay {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));
  padding: 40rpx;
  box-sizing: border-box;
}

/* 封面标题容器 */
.cover-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  position: relative;
  width: 100%;
  padding: 0 20rpx;
}

/* 标题装饰 */
.title-decoration {
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to var(--direction, right), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.8));
  margin: 0 20rpx;
  position: relative;
}

.title-decoration::before, .title-decoration::after {
  content: "";
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
}

.title-decoration.left {
  --direction: right;
}

.title-decoration.right {
  --direction: left;
}

.title-decoration.left::before {
  left: 0;
}

.title-decoration.left::after {
  right: 0;
}

.title-decoration.right::before {
  left: 0;
}

.title-decoration.right::after {
  right: 0;
}

.cover-title {
  font-size: 60rpx;
  font-weight: bold;
  color: #fff;
  text-align: center;
  text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.6);
  z-index: 2;
  letter-spacing: 10rpx;
  position: relative;
  padding: 0 30rpx;
}

.cover-title::before {
  content: "";
  position: absolute;
  top: -20rpx;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.6), transparent);
}

.cover-title::after {
  content: "";
  position: absolute;
  bottom: -20rpx;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.6), transparent);
}

.cover-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.cover-page .page-number {
  color: rgba(255, 255, 255, 0.8);
  z-index: 2;
}

/* 目录页样式 */
.toc-page {
  background-color: #fff;
}

.toc-content {
  width: 100%;
  height: 100%;
  padding: 60rpx 40rpx;
  box-sizing: border-box;
  position: relative;
  background-color: #f8f5f0;
}

.toc-header {
  font-size: 48rpx; /* 增大字号 */
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx; /* 增加下边距 */
  font-family: serif; /* 使用衬线字体，更像书籍 */
  letter-spacing: 4rpx; /* 增加字母间距 */
  text-transform: uppercase; /* 使用大写，更正式 */
  position: relative;
}

.toc-header::after {
  content: "";
  position: absolute;
  bottom: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 3rpx;
  background: #a67c52;
}

.toc-divider {
  width: 60%;
  height: 2rpx; /* 减小高度使其更精致 */
  background-color: #e29c7a;
  margin: 0 auto 40rpx;
  position: relative;
}

.toc-divider::before,
.toc-divider::after {
  content: "❦"; /* 使用花纹符号 */
  position: absolute;
  top: -15rpx;
  color: #e29c7a;
  font-size: 30rpx;
}

.toc-divider::before {
  left: 0;
}

.toc-divider::after {
  right: 0;
}

.toc-scroll {
  height: calc(100% - 140rpx);
  padding: 0 20rpx;
}

.toc-chapter {
  margin-bottom: 40rpx; /* 增加间距 */
}

.toc-chapter-title {
  margin-bottom: 20rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  color: #222;
  font-weight: bold;
  font-family: serif; /* 使用衬线字体 */
  position: relative;
  padding-left: 20rpx; /* 左侧添加空间用于装饰 */
}

.toc-chapter-title::before {
  content: "◆"; /* 添加装饰符号 */
  position: absolute;
  left: 0;
  color: #a67c52;
  font-size: 24rpx;
}

.toc-subchapter {
  margin: 16rpx 0 16rpx 40rpx; /* 增加缩进和间距 */
  display: flex;
  align-items: center;
  color: #444;
  position: relative; /* 为装饰元素定位 */
  font-family: serif; /* 使用衬线字体 */
}

.toc-dot {
  width: 6rpx;
  height: 6rpx;
  background-color: #a67c52;
  border-radius: 50%;
  margin-right: 12rpx;
}

.toc-subchapter::after {
  content: '';
  position: absolute;
  right: 20rpx;
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #e29c7a;
  border-right: 2rpx solid #e29c7a;
  transform: rotate(45deg);
  opacity: 0.6;
  transition: all 0.2s;
}

.toc-subchapter:active {
  background-color: rgba(226, 156, 122, 0.2);
  color: #e29c7a;
}

.toc-subchapter:active::after {
  opacity: 1;
  transform: translateX(5rpx) rotate(45deg);
}

/* 内容页样式 */
.content-page {
  background-color: #fff;
  background-image: linear-gradient(rgba(0, 0, 0, 0.02) 1px, transparent 1px);
  background-size: 100% 32rpx;
  background-position: 0 16rpx;
  height: 100%;
  box-sizing: border-box;
}

.content-scroll {
  height: 100%;
  width: 100%;
}

.page-content {
  width: 100%;
  height: 100%;
  padding: 60rpx 50rpx;
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx; /* 增加下边距 */
  position: relative;
}

/* 页码信息通用样式 */
.current-page-info {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}

/* 封面页的页码特殊样式 */
.cover-page .current-page-info {
  color: rgba(255, 255, 255, 0.9);
  background-color: rgba(0, 0, 0, 0.2);
}

.page-number {
  position: absolute;
  bottom: 25rpx;
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  color: #888;
  font-style: italic;
  font-family: serif; /* 使用衬线字体 */
  position: relative;
}

.page-number::before,
.page-number::after {
  content: "—"; /* 添加装饰符号 */
  color: #ccc;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.page-number::before {
  left: 35%;
}

.page-number::after {
  right: 35%;
}

.chapter-info {
  text-align: center;
  color: #666;
  margin-bottom: 16rpx;
  font-family: serif; /* 使用衬线字体 */
  font-style: italic; /* 斜体样式，典型的书籍样式 */
  letter-spacing: 2rpx;
}

.page-title {
  text-align: center;
  margin: 24rpx 0 30rpx;
  font-weight: bold;
  color: #333;
  font-family: serif; /* 使用衬线字体 */
  letter-spacing: 2rpx; /* 增加字间距 */
  position: relative;
  padding: 0 30rpx;
}

.page-title::before,
.page-title::after {
  content: "~";
  position: absolute;
  color: #a67c52;
  font-size: 30rpx;
  top: 50%;
  transform: translateY(-50%);
}

.page-title::before {
  left: 0;
}

.page-title::after {
  right: 0;
}

.page-divider {
  width: 80%;
  height: 1rpx;
  background-color: #e0e0e0;
  margin: 10rpx auto 40rpx; /* 增加下边距 */
  position: relative;
}

.page-divider::before {
  content: "❧"; /* 使用装饰符号 */
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: -14rpx;
  color: #a67c52;
  font-size: 24rpx;
}

.page-text {
  line-height: 2.0; /* 增加行高 */
  color: #333;
  text-align: justify;
  column-gap: 60rpx;
  margin-top: 30rpx;
  flex: 1;
  overflow-y: auto;
  font-family: serif; /* 使用衬线字体 */
  text-indent: 2em; /* 首行缩进，典型书籍样式 */
  position: relative;
  padding: 0 10rpx;
}

.page-text::first-letter {
  font-size: 1.2em; /* 首字母放大 */
  font-weight: bold;
  color: #a67c52;
}

.page-intro {
  font-style: italic;
  color: #888;
}

/* 返回按钮 */
.back-button {
  position: fixed;
  top: 20rpx;
  left: 20rpx;
  padding: 12rpx 30rpx;
  background-color: rgba(226, 156, 122, 0.9);
  color: white;
  font-size: 28rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(150, 126, 102, 0.2);
  z-index: 25; /* 确保它显示在其他元素上方 */
  display: flex;
  align-items: center;
}

.back-button::before {
  content: "";
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-left: 3rpx solid #fff;
  border-bottom: 3rpx solid #fff;
  transform: rotate(45deg);
  margin-right: 10rpx;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .book-container {
    background-color: #2e2a25;
  }
  
  .page {
    background-color: #3c3730;
  }
  
  .toc-page {
    background-color: #3c3730;
  }
  
  .toc-chapter-title {
    color: #e9e1d8;
    border-bottom-color: #555;
  }
  
  .toc-subchapter {
    color: #bbb;
  }
  
  .toc-subchapter::after {
    border-color: #e29c7a;
    opacity: 0.6;
  }
  
  .toc-subchapter:active {
    background-color: rgba(226, 156, 122, 0.2);
    color: #e29c7a;
  }
  
  .page-title {
    color: #e9e1d8;
  }
  
  .page-text {
    color: #bbb;
  }
  
  .content-page {
    background-color: #3c3730;
  }
}

.generate-preview-container {
  margin-top: 40rpx;
  text-align: center;
}

.generate-preview-btn {
  display: inline-block;
  background-color: #e29c7a;
  color: white;
  padding: 12rpx 40rpx;
  border-radius: 40rpx;
  margin: 0 auto;
  box-shadow: 0 4rpx 12rpx rgba(150, 126, 102, 0.2);
  border: none;
  font-weight: normal;
}

.generate-preview-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.generate-preview-btn[disabled] {
  opacity: 0.5;
  background-color: #ccc;
}

/* 重新生成按钮样式 */
.regenerate-btn {
  display: inline-block;
  background-color: #6c9a7b;
  color: white;
  padding: 12rpx 40rpx;
  border-radius: 40rpx;
  margin: 0 auto;
  box-shadow: 0 4rpx 12rpx rgba(108, 154, 123, 0.2);
  border: none;
  font-weight: normal;
}

.regenerate-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.regenerate-btn[disabled] {
  opacity: 0.5;
  background-color: #ccc;
}

/* 生成中页面样式 */
.generating-page {
  background-color: #fffaf3;
}

.generating-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  text-align: center;
}

.generating-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.generating-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.generating-subtitle {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 50rpx;
}

.generating-progress {
  width: 80%;
  margin: 0 auto 40rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #f0e5da;
  border-radius: 10rpx;
  overflow: hidden;
  position: relative;
}

.progress-inner {
  position: absolute;
  height: 100%;
  background-color: #e29c7a;
  border-radius: 10rpx;
  width: 30%;
  animation: progress-animation 2s infinite ease-in-out;
}

@keyframes progress-animation {
  0% {
    left: -30%;
  }
  100% {
    left: 100%;
  }
}

.generating-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  line-height: 1.5;
  max-width: 80%;
}

/* 暗黑模式适配 - 生成中页面 */
@media (prefers-color-scheme: dark) {
  .generating-page {
    background-color: #3c3730;
  }
  
  .generating-title {
    color: #e9e1d8;
  }
  
  .generating-subtitle {
    color: #bbb;
  }
  
  .generating-tip {
    color: #999;
  }
  
  .progress-bar {
    background-color: #4a443e;
  }
}

.refresh-button {
  margin-top: 30rpx;
  padding: 16rpx 60rpx;
  background-color: #e29c7a;
  color: white;
  font-size: 28rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(150, 126, 102, 0.2);
  border: none;
  font-weight: normal;
}

.refresh-button:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 生成中的指示器 */
.generating-indicator {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
}

.generating-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(226, 156, 122, 0.3);
  border-radius: 50%;
  border-top-color: #e29c7a;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

.generating-text {
  font-size: 28rpx;
  color: #e29c7a;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.polish-btn {
  margin-top: 20rpx;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.polish-btn:active {
  background-color: #45a049;
  transform: translateY(1rpx);
}

.polish-btn[disabled] {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

/* 简洁模式下的样式 */
.simple-mode {
  padding: 60rpx 0;
}

/* 生成我的书按钮样式 */
.generate-book-btn {
  background-color: rgba(232, 216, 192, 0.9);
  color: #7a5a3a;
  border: 1rpx solid #d5c0a0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  border-radius: 6rpx;
  padding: 10rpx 40rpx;
  margin-top: 40rpx;
  z-index: 2;
}

/* 书脊效果 */
.book-spine {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 10rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.02) 70%, transparent);
  border-right: 1rpx solid rgba(0, 0, 0, 0.05);
  z-index: 3;
}

/* 书页右侧边缘效果 */
.book-edge-right {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 5rpx;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.03), transparent);
  z-index: 3;
}

/* 按钮激活样式 */
.nav-button:active {
  transform: translateY(-50%) scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 悬浮按钮 */
.floating-btn {
  position: fixed;
  right: 30rpx;
  bottom: 180rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: rgba(226, 156, 122, 0.9);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
}

/* 分享按钮 */
.floating-share-btn {
  position: fixed;
  right: 30rpx;
  bottom: 320rpx;
  width: 120rpx !important;
  height: 120rpx !important;
  min-width: 120rpx !important;
  min-height: 120rpx !important;
  max-width: 120rpx !important;
  max-height: 120rpx !important;
  background-color: rgba(65, 137, 230, 0.9) !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  color: #fff !important;
  font-size: 24rpx !important;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
  
  /* 重置按钮默认样式 */
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1 !important;
  border: none !important;
  background-size: cover !important;
  outline: none !important;
  overflow: visible !important;
  text-align: center !important;
  vertical-align: middle !important;
  box-sizing: border-box !important;
  font-weight: normal !important;
}

/* plain 属性的特殊样式 */
button.floating-share-btn[plain] {
  border: none !important;
  background-color: rgba(65, 137, 230, 0.9) !important;
  color: #fff !important;
}

/* 防止按钮点击时的默认样式变化 */
.floating-share-btn::after {
  border: none !important;
  outline: none !important;
  content: none !important;
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* 修复点击态样式 */
.floating-share-btn:not([disabled]):active {
  background-color: rgba(65, 137, 230, 0.8);
  opacity: 0.9;
}

.floating-share-btn .floating-btn-icon {
  color: #fff !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  text-align: center !important;
  width: 100% !important;
  height: auto !important;
  position: relative !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  line-height: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.floating-btn-icon {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
}

/* 暗黑模式适配 - 浮动按钮 */
@media (prefers-color-scheme: dark) {
  .floating-btn {
    background-color: #e29c7a;
  }
}

.cover-page .book-spine {
  width: 20rpx;
  background: linear-gradient(to right, #8c5d32, #a67c52);
  border-right: 1rpx solid rgba(0, 0, 0, 0.3);
  box-shadow: 2rpx 0 6rpx rgba(0, 0, 0, 0.2);
  z-index: 5;
}

/* 标题下方装饰 */
.title-ornament {
  width: 120rpx;
  height: 20rpx;
  margin: 10rpx auto 30rpx;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.9) 2rpx, transparent 3rpx);
  background-size: 10rpx 10rpx;
  position: relative;
  z-index: 2;
}

.title-ornament::before, .title-ornament::after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  height: 2rpx;
  width: 40rpx;
  background: linear-gradient(to var(--direction, right), rgba(255, 255, 255, 0.9), transparent);
}

.title-ornament::before {
  --direction: right;
  left: -45rpx;
}

.title-ornament::after {
  --direction: left;
  right: -45rpx;
}

/* 作者名称样式 */
.author-name {
  position: absolute;
  bottom: -50rpx;
  right: 40rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'STKaiti', 'KaiTi', serif;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.4);
  z-index: 3;
  padding: 5rpx 15rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.4);
}

.download-link {
  margin-top: 30rpx;
  color: #4a6bff;
  font-size: 28rpx;
  text-decoration: underline;
  padding: 16rpx 40rpx;
  background-color: rgba(74, 107, 255, 0.1);
  border-radius: 40rpx;
}

.download-link:active {
  opacity: 0.8;
}

.pdf-preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f8f8f8;
  padding: 40rpx;
  text-align: center;
}

.pdf-preview-info {
  margin-bottom: 60rpx;
}

.pdf-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.pdf-subtitle {
  font-size: 28rpx;
  color: #666;
}

.pdf-preview-btn {
  background-color: #4a6bff;
  color: white;
  padding: 24rpx 60rpx;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 16rpx rgba(74, 107, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.pdf-preview-btn:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(74, 107, 255, 0.2);
}

.preview-btn-text {
  font-size: 32rpx;
  font-weight: 500;
} 