<view class="book-container" style="height: {{containerHeight}}px;">

  <!-- 返回首页按钮 -->
  <view class="floating-btn" bindtap="navigateToHome">
    <view class="floating-btn-icon">返回首页</view>
  </view>
  
  <!-- 刷新PDF按钮 -->
  <view class="floating-refresh-btn" bindtap="refreshPdf">
    <view class="floating-btn-icon">刷新</view>
  </view>
  
  <!-- PDF查看区域 -->
  <view class="pdf-container" style="height: calc({{containerHeight}}px - {{safeAreaBottom > 0 ? safeAreaBottom : 0}}px);">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">PDF加载中...</view>
    </view>
    
    <!-- 加载错误 -->
    <view class="error-container" wx:elif="{{loadError}}">
      <view class="error-icon">!</view>
      <view class="error-text">PDF加载失败</view>
      <view class="error-subtext">请检查网络连接后点击刷新按钮重试</view>
      <view class="download-link" bindtap="openInBrowser">在浏览器中打开PDF</view>
    </view>

    <!-- PDF已下载成功 -->
    <view class="pdf-preview-container" wx:else>
      <view class="pdf-preview-info">
        <view class="pdf-title">PDF文件已下载完成</view>
        <view class="pdf-subtitle">点击下方按钮打开预览</view>
              </view>
      <view class="pdf-preview-btn" bindtap="openFilePreview">
        <view class="preview-btn-text">预览PDF文档</view>
            </view>
          </view>
  </view>
</view> 