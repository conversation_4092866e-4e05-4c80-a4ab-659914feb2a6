Component({
  properties: {
    bookData: {
      type: Object,
      value: null
    },
    pdfUrl: {
      type: String,
      value: ''
    }
  },

  data: {
    localPdfPath: '',
    isLoading: true,
    loadError: false,
    systemInfo: {},
    containerHeight: 0,
    safeAreaBottom: 0
  },

  lifetimes: {
    attached() {
      console.log('BookShareContent: 组件attached');
      this.getSystemInfo();
      // 开始下载PDF
      this.downloadPDF();
      
      // 添加自动超时处理，5秒后如果还在加载中，自动转为已加载状态
      this.loadTimer = setTimeout(() => {
        if (this.data.isLoading) {
          console.log('PDF加载超时，自动转为已加载状态');
          this.setData({
            isLoading: false
          });
        }
      }, 5000);
    },
    
    detached() {
      console.log('BookShareContent: 组件detached');
      // 清除定时器
      if (this.loadTimer) {
        clearTimeout(this.loadTimer);
      }
    }
  },
  
  methods: {
    getSystemInfo() {
      wx.getSystemInfo({
        success: (res) => {
          const windowHeight = res.windowHeight;
          const screenHeight = res.screenHeight;
          const safeBottom = res.safeArea ? (screenHeight - res.safeArea.bottom) : 0;
          this.setData({
            systemInfo: res,
            containerHeight: windowHeight,
            safeAreaBottom: safeBottom
          });
          wx.onWindowResize(() => {
            wx.getSystemInfo({
              success: (res) => {
                this.setData({
                  containerHeight: res.windowHeight,
                  safeAreaBottom: res.safeArea ? (res.screenHeight - res.safeArea.bottom) : 0
                });
              }
            });
          });
        }
      });
    },

    // 下载PDF到本地
    downloadPDF() {
      this.setData({ isLoading: true, loadError: false });
      
      // 使用传入的pdfUrl属性，如果没有则使用默认URL
      const pdfUrlToUse = this.properties.pdfUrl;
      console.log('开始下载PDF:', pdfUrlToUse);
      
      // 添加时间戳避免缓存问题
      const downloadUrl = `${pdfUrlToUse}?t=${new Date().getTime()}`;
      
      wx.downloadFile({
        url: downloadUrl,
        success: (res) => {
          console.log('PDF下载成功:', res);
          
          if (res.statusCode === 200) {
            // 检查文件大小，确保下载成功
            wx.getFileInfo({
              filePath: res.tempFilePath,
              success: (fileInfo) => {
                console.log('下载的PDF文件信息:', fileInfo);
                
                if (fileInfo.size > 0) {
      this.setData({
                    localPdfPath: res.tempFilePath,
                    isLoading: false
      });
                  console.log('PDF已下载并准备好预览:', res.tempFilePath);
                } else {
                  console.error('下载的PDF文件大小为0');
                  this.handleDownloadError(new Error('下载的PDF文件大小为0'));
      }
    },
              fail: (err) => {
                console.error('获取文件信息失败:', err);
                // 即使获取文件信息失败，也尝试使用下载的文件
        this.setData({
                  localPdfPath: res.tempFilePath,
                  isLoading: false
                });
              }
            });
          } else {
            console.error('下载PDF文件失败, 状态码:', res.statusCode);
            this.handleDownloadError(new Error(`下载PDF文件失败, 状态码: ${res.statusCode}`));
      }
    },
        fail: (err) => {
          console.error('下载PDF失败:', err);
          this.handleDownloadError(err);
        }
      });
    },
    
    // 处理下载错误
    handleDownloadError(err) {
      console.error('下载PDF出错:', err);
      this.setData({
        isLoading: false,
        loadError: true
      });
      wx.showToast({
        title: '加载PDF失败',
        icon: 'none',
        duration: 2000
      });
    },

    // 返回首页
    navigateToHome() {
      wx.reLaunch({
        url: '/pages/index/index',
        fail: (err) => {
          console.error('跳转到首页失败:', err);
          wx.showToast({
            title: '无法返回首页',
            icon: 'none'
          });
        }
      });
    },
    
    // 在浏览器中打开PDF
    openInBrowser() {
      // 使用传入的pdfUrl属性，如果没有则使用默认URL
      const pdfUrl = this.properties.pdfUrl;
      wx.setClipboardData({
        data: pdfUrl,
        success: () => {
          wx.showModal({
            title: '链接已复制',
            content: '由于小程序限制，无法直接打开浏览器。PDF链接已复制到剪贴板，您可以粘贴到浏览器中打开。',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      });
    },

    // 打开文件预览
    openFilePreview() {
      if (!this.data.localPdfPath) {
        wx.showToast({
          title: '文件未下载完成',
          icon: 'none'
        });
        return;
      }
      
      console.log('打开文件预览:', this.data.localPdfPath);
      
      wx.openDocument({
        filePath: this.data.localPdfPath,
        showMenu: true,
        success: () => {
          console.log('打开文档成功');
        },
        fail: (err) => {
          console.error('打开文档失败:', err);
          wx.showToast({
            title: '无法预览文档',
            icon: 'none'
          });
       }
      });
    },

    // 刷新PDF
    refreshPdf() {
      console.log('刷新PDF');
      // 重新下载PDF
      this.downloadPDF();
    }
  }
}) 