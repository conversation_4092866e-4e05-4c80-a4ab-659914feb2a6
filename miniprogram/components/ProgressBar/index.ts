Component({
  properties: {
    max: { type: Number, value: 100 },     // 进度条上限（总长度）
    min: { type: Number, value: 0 },       // 需要显示刻度的位置（0~max）
    current: { type: Number, value: 0 },   // 当前进度（0~max）
    showMinLabel: { type: Boolean, value: true } // 是否显示 min 数值标签
  },
  data: {
    percent: 0,       // current 对 max 的百分比
    minPercent: 0     // min 对 max 的百分比（刻度位置）
  },
  observers: {
    'max, min, current': function (max, min, current) {
      // 兜底与夹取
      if (typeof max !== 'number' || max <= 0) {
        this.setData({ percent: 0, minPercent: 0 });
        return;
      }
      const clamp = (v, lo, hi) => Math.max(lo, Math.min(v, hi));

      const percent = clamp((current / max) * 100, 0, 100);
      const minClamped = clamp(min, 0, max);
      const minPercent = (minClamped / max) * 100;

      this.setData({ percent, minPercent });
    }
  }
});
