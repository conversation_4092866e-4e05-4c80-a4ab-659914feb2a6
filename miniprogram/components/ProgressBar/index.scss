.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 顶部两端标签（0 和 max） */
.progress-labels {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 8rpx;
}
.progress-label {
  font-size: 24rpx;
  color: #666;
}

/* 条背景 */
.progress-bg {
  width: 550rpx;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  position: relative;
}

/* 填充 */
.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFB800 0%, #FF8A00 100%);
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

/* min 刻度 */
.min-tick {
  position: absolute;
  top: 0;
  height: 100%;
  transform: translateX(-50%); /* 让刻度线居中到准确位置 */
  display: flex;
  align-items: flex-start;
  pointer-events: none;
}
.min-tick-line {
  width: 8rpx;
  height: 100%;
  background-color: #ff6a00; /* 刻度线颜色 */
}
.min-tick-label {
  position: absolute;
  bottom: 100%;
  transform: translateX(-50%);
  font-size: 22rpx;
  color: #ff6a00;
  padding-bottom: 6rpx;
  white-space: nowrap;
}

/* 底部当前进度文字 */
.progress-text {
  margin-top: 10rpx;
  font-size: 26rpx;
  color: #333;
}
