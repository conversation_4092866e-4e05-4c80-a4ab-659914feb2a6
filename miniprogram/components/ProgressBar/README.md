# ProgressBar 进度条组件

一个功能丰富的微信小程序进度条组件，支持自定义颜色、动画效果、文字显示等多种配置。

## 功能特点

- ✅ 支持传入完成百分比 (0-100)
- ✅ 支持展示或隐藏当前进度数据
- ✅ 支持自定义颜色 (包括渐变色)
- ✅ 支持动画效果
- ✅ 支持进度条内文字显示
- ✅ 支持自定义高度和圆角
- ✅ 支持多种预设主题
- ✅ 支持进度完成事件回调

## 基础用法

### 1. 在页面 JSON 中注册组件

```json
{
  "usingComponents": {
    "ProgressBar": "/components/ProgressBar/index"
  }
}
```

### 2. 在 WXML 中使用

```xml
<!-- 基础用法 -->
<ProgressBar percentage="{{50}}" />

<!-- 隐藏百分比文字 -->
<ProgressBar 
  percentage="{{75}}" 
  showPercentage="{{false}}" 
/>

<!-- 自定义颜色 -->
<ProgressBar 
  percentage="{{60}}" 
  progressColor="linear-gradient(90deg, #FF6B6B 0%, #FF8E53 100%)"
  backgroundColor="#F0F0F0"
  textColor="#FF6B6B"
/>

<!-- 显示内部文字 -->
<ProgressBar 
  percentage="{{80}}" 
  showInnerText="{{true}}"
  innerText="80% 完成"
  height="{{32}}"
/>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| percentage | Number | 0 | 进度百分比 (0-100) |
| showPercentage | Boolean | true | 是否显示百分比文字 |
| showInnerText | Boolean | false | 是否显示进度条内的文字 |
| innerText | String | '' | 进度条内的自定义文字 |
| height | Number | 16 | 进度条高度 (rpx) |
| borderRadius | Number | 8 | 进度条圆角 (rpx) |
| progressColor | String | 'linear-gradient(90deg, #FFB800 0%, #FF8A00 100%)' | 进度条颜色 |
| backgroundColor | String | '#E5E5E5' | 背景颜色 |
| textColor | String | '#666666' | 百分比文字颜色 |
| innerTextColor | String | '#FFFFFF' | 进度条内文字颜色 |
| animated | Boolean | true | 是否启用动画 |
| animationDuration | Number | 800 | 动画持续时间 (ms) |
| customStyle | String | '' | 自定义样式 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| progressComplete | 进度动画完成时触发 | { percentage: Number } |

## 使用示例

### 基础进度条
```xml
<ProgressBar 
  percentage="{{24}}" 
  showPercentage="{{true}}"
  bind:progressComplete="onProgressComplete"
/>
```

### 自定义颜色进度条
```xml
<ProgressBar 
  percentage="{{65}}" 
  progressColor="linear-gradient(90deg, #FF6B6B 0%, #FF8E53 100%)"
  backgroundColor="#F0F0F0"
  textColor="#FF6B6B"
/>
```

### 隐藏百分比的进度条
```xml
<ProgressBar 
  percentage="{{80}}" 
  showPercentage="{{false}}"
  height="{{20}}"
  progressColor="#34C759"
/>
```

### 带内部文字的进度条
```xml
<ProgressBar 
  percentage="{{45}}" 
  showInnerText="{{true}}"
  innerText="{{45}}% 完成"
  height="{{32}}"
  showPercentage="{{false}}"
/>
```

### 预设主题样式
```xml
<!-- 主题样式通过 customStyle 属性添加 CSS 类 -->
<ProgressBar 
  percentage="{{70}}" 
  customStyle="theme-primary"
/>

<ProgressBar 
  percentage="{{85}}" 
  customStyle="theme-success"
/>

<ProgressBar 
  percentage="{{60}}" 
  customStyle="theme-warning"
/>

<ProgressBar 
  percentage="{{30}}" 
  customStyle="theme-danger"
/>
```

### 不同尺寸
```xml
<!-- 小尺寸 -->
<ProgressBar 
  percentage="{{50}}" 
  customStyle="size-small"
/>

<!-- 大尺寸 -->
<ProgressBar 
  percentage="{{75}}" 
  customStyle="size-large"
/>
```

## 方法调用

组件提供了 `setProgress` 方法来动态设置进度：

```javascript
// 获取组件实例
const progressBar = this.selectComponent('#my-progress-bar');

// 设置进度
progressBar.setProgress(80);
```

## 注意事项

1. `percentage` 值会自动限制在 0-100 范围内
2. 当 `animated` 为 true 时，进度变化会有平滑的动画效果
3. `progressColor` 支持纯色和渐变色
4. 组件会自动处理进度值的边界情况
5. 动画使用 `requestAnimationFrame` 实现，性能较好

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础进度条功能
- 支持自定义颜色和样式
- 支持动画效果
- 支持事件回调
