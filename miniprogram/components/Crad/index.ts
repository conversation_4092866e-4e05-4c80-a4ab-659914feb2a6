Component({
    options: {
        // 允许使用多个slot
        multipleSlots: true
    },
    properties: {
        // 支持字符串内容
        children: {
            type: null, // 使用null允许任何类型
            value: ''
        },
        // 自定义样式类
        customClass: {
            type: String,
            value: ''
        },
        // 自定义内联样式
        customStyle: {
            type: String,
            value: ''
        },
        // 是否显示边框
        bordered: {
            type: Boolean,
            value: true
        },
        // 是否有阴影
        shadow: {
            type: Boolean,
            value: false
        }
    },
    data: {
        // 组件内部数据
    },
    lifetimes: {
        attached() {
            // 组件实例进入页面节点树时执行
            this._updateClassAndStyle();
        }
    },
    observers: {
        'bordered, shadow': function() {
            this._updateClassAndStyle();
        }
    },
    methods: {
        // 更新样式类和内联样式
        _updateClassAndStyle() {
            let classNames = '';

            if (this.data.bordered) {
                classNames += ' card-bordered';
            }

            if (this.data.shadow) {
                classNames += ' card-shadow';
            }

            this.setData({
                computedClass: classNames
            });
        },

        // 检查是否有内容
        hasContent(): boolean {
            return !!(
                this.data.children ||
                this.selectComponent('.slot') ||
                this.selectComponent('.slot-content')
            );
        }
    }
})