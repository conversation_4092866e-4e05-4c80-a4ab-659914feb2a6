/* Card组件基础样式 */
.card-container {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 16rpx;
    min-height: 100rpx;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;

    /* 阴影效果 */
    &.card-shadow {
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    }

    /* 文本内容样式 */
    .card-text {
        font-size: 28rpx;
        line-height: 1.5;
        color: #333;
        word-wrap: break-word;
        word-break: break-all;
    }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
    .card-container {
        padding: 20rpx;
        border-radius: 12rpx;

        .card-text {
            font-size: 26rpx;
        }
    }
}