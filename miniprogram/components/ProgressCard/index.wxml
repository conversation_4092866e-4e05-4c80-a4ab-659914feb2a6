<view class="progress-card" style="{{customStyle}}" bind:tap="onCardTap">
  <!-- 进度条背景 -->
  <view class="progress-percentage" style="width: {{progressPercentage}}%; {{progressColor ? 'background: ' + progressColor : ''}};border-radius:{{progressPercentage ==100?'60rpx':'60rpx 0 0 60rpx'}}">
  </view>

  <!-- 内容层 -->
  <view class="progress-card-left">
    <!-- 头像容器 -->
    <view class="avatar-container">
      <view class="avatar">
        <text wx:if="{{!avatarUrl}}" class="avatar-emoji">👦</text>
        <image wx:else class="avatar-image" src="{{avatarUrl}}" mode="aspectFill"></image>
      </view>
    </view>

    <!-- 文字内容 -->
    <view class="text-content">
      <view class="main-title">{{mainTitle}}</view>
      <view class="sub-title">{{subTitle}}</view>
    </view>
  </view>
</view>