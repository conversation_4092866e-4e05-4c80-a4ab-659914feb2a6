.progress-card {
  background: #ffffff;
  border-radius: 60rpx;
  padding: 24rpx;
  position: relative;
  display: flex;
  height:64rpx;
  // flex-direction: column;
  // justify-content: space-between;
  background-color: #F7F7F7;

  .progress-card-left {
      display: flex;
      width: 550rpx;
      align-items: center;
      justify-content: space-between;
      position: relative; /* 确保内容在进度条之上 */
      z-index: 2; /* 提高层级 */
      // flex: 1;

      .avatar-container {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        // background: linear-gradient(135deg, #ffd89b 0%, #ff9a56 100%);
        padding: 3rpx;
        margin-right: 16rpx;

        .avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          background: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;

          .avatar-emoji {
            font-size: 24rpx;
          }

          .avatar-image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
      }
      .text-content{
        .main-title{
          font-size: 28rpx;
          font-weight: 400;
          color: #282624;
          margin-bottom: 8rpx;
        }
        .sub-title{
          font-size: 24rpx;
          color: #F5A630;
          font-weight: 400;
        }
      }
    }

 .progress-percentage {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-radius: 60rpx 0 0 60rpx; /* 与外层容器保持一致的圆角 */
      background: linear-gradient(135deg, #ffd89b 0%, #ff9a56 100%);
      font-weight: 500;
      transition: width 0.3s ease; /* 添加动画效果 */
      min-width: 0; /* 确保宽度为0时也能正确显示 */
      overflow: hidden; /* 防止内容溢出 */
      z-index: 1; /* 确保在背景层 */
    }
}
