# ProgressCard 进度卡片组件

一个美观的进度卡片组件，支持自定义头像、标题、进度。

## 功能特性

- ✅ 支持自定义头像（图片或emoji）
- ✅ 支持主标题和副标题
- ✅ 支持当前进度和总进度显示
- ✅ 支持点击事件
- ✅ 响应式设计，自适应不同屏幕

## 使用方法

### 1. 在页面json中引入组件

```json
{
  "usingComponents": {
    "progress-card": "/components/ProgressCard/index"
  }
}
```

### 2. 在页面wxml中使用

```xml
<!-- 基础用法 -->
<progress-card
  main-title="童年·家的温暖记忆"
  current-progress="{{3}}"
  total-progress="{{21}}"
  bind:cardtap="onCardTap"
></progress-card>

<!-- 带头像和副标题的用法 -->
<progress-card
  avatar-url="https://example.com/avatar.jpg"
  main-title="我的成长故事"
  sub-title="记录美好时光"
  current-progress="{{5}}"
  total-progress="{{15}}"
  theme="orange"
  bind:cardtap="onCardTap"
></progress-card>

```

### 3. 在页面js中处理事件

```javascript
Page({
  onCardTap(e) {
    const { currentProgress, totalProgress, progressPercentage } = e.detail;
    console.log('卡片被点击', { currentProgress, totalProgress, progressPercentage });
    
    // 跳转到详情页面
    wx.navigateTo({
      url: '/pages/detail/index'
    });
  }
});
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| avatarUrl | String | '' | 头像图片URL，为空时显示默认emoji |
| mainTitle | String | '童年·家的温暖记忆' | 主标题 |
| subTitle | String | '' | 副标题 |
| currentProgress | Number | 3 | 当前进度 |
| totalProgress | Number | 21 | 总进度 |
| progressColor | String | '#1890ff' | 进度条颜色（当theme为blue时生效） |
| customStyle | String | '' | 自定义样式 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| cardtap | 点击卡片时触发 | { currentProgress, totalProgress, progressPercentage } |

## 样式定制

组件支持通过 `customStyle` 属性传入自定义样式：

```xml
<progress-card
  custom-style="margin: 20rpx; box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);"
  main-title="自定义样式"
></progress-card>
```

## 注意事项

1. 头像支持网络图片和本地图片，建议使用正方形图片
2. 进度百分比会自动计算，无需手动传入
3. 组件宽度固定为 622rpx，高度为 112rpx
4. 点击整个卡片区域都会触发 cardtap 事件
