Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 头像URL
    avatarUrl: {
      type: String,
      value: ''
    },
    // 主标题
    mainTitle: {
      type: String,
      value: '童年·家的温暖记忆'
    },
    // 副标题
    subTitle: {
      type: String,
      value: ''
    },
    // 当前进度
    currentProgress: {
      type: Number,
      value: 0
    },
    // 总进度
    totalProgress: {
      type: Number,
      value: 21
    },
    // 进度条颜色
    progressColor: {
      type: String,
      value: 'linear-gradient(90deg, #F5A630 10%, #F7F7F7);'
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    progressPercentage: 0,
    themeClass: ''
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被创建好时执行
      this.updateProgressPercentage();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'currentProgress, totalProgress': function() {
      this.updateProgressPercentage();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新进度百分比
     */
    updateProgressPercentage() {
      const currentProgress = this.properties.currentProgress || 0;
      const totalProgress = this.properties.totalProgress || 21;
      const percentage = totalProgress === 0 ? 0 : Math.round((currentProgress / totalProgress) * 100);

      this.setData({
        progressPercentage: Math.min(100, Math.max(0, percentage)) // 确保百分比在0-100之间
      });
    },


    /**
     * 点击卡片事件
     */
    onCardTap() {
      this.triggerEvent('cardtap', {
        currentProgress: this.data.currentProgress,
        totalProgress: this.data.totalProgress,
        progressPercentage: this.data.progressPercentage
      });
    }
  },
});
