.question-container {
  position: absolute;
  top: 470rpx;
  right: 40rpx;
  background: #fff;
  border-radius: 20rpx;
  margin: 40rpx 20rpx 20rpx 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx #e0e0e0;
  width: 570rpx;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup-animate {
  transition: all 0.3s;
}
.triangle {
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-top: 20rpx solid #fff;
  margin: 0 auto;
  /* 让三角形紧贴对话框底部 */
  position: absolute;
  bottom: -16rpx;
  right: 0;
  left: 0;
}
.question {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.input-container {
  margin: 20rpx 0;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 15rpx;
  width: 540rpx;
  min-height: 100rpx;
}
.text-input {
  flex: 7;
  border: none;
  background: transparent;
  font-size: 28rpx;
  outline: none;
}
.voice-btn {
  margin-top: -12rpx;
  flex: 1;
}
.recording-tip {
  color: #007aff;
  text-align: center;
  margin-top: 20rpx;
  font-size: 26rpx;
}
.sendMsg{
  font-size: 28rpx;
  z-index: 2;
  color: #A9A8A7;
}
.question-title{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  .question-title-right{
    display: flex;
    align-items: center;
  }
  .question-title-icon{
    width: 8rpx;
    height: 32rpx;
  }
  .question-title-period{
    font-size: 32rpx;
    margin-left: 5rpx;
    margin-right: 20rpx;
  }
  .question-title-theme{
    font-size: 24rpx;
    color: #A9A8A7;
    width: 220rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .question-title-No{
    // height: 38rpx;
    background-color: #F5A63014;
    padding:4rpx 8rpx;
    color: #F5A631;
    border: 1rpx solid #F5A631;
    font-size: 20rpx;
    border-radius: 40rpx;
    margin-left: 8rpx;
  }
}
.addPic-container{
  display: flex;
  float: right;
}
.addPic{
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

