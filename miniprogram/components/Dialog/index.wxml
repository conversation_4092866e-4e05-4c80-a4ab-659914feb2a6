<view wx:if="{{visible}}">
  <view class="popup-animate" animation="{{animationData}}">
    <view class="question-container">
      <view wx:if="{{!isPreParing}}">
        <view class="question-title">
          <view class="question-title-right">
            <image class="question-title-icon" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Rectangle.png" mode="" />
            <view class="question-title-period">{{question.period.period_name || '童年'}}阶段</view>
          </view>
          <view class="question-title-right">
            <view class="question-title-theme">话题· {{question.chain_title}}</view>
            <view class="question-title-No">问题{{question.question_index}}</view>
          </view>

        </view>
        <text class="question">{{question.q}}</text>
        <view class="input-container">
          <view style="display: flex;">
            <view class="voice-btn" bindtouchstart="startVoice" bindtouchend="stopVoice">
              <image style="width: 40rpx;height: 40rpx;margin-top: 18rpx;" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/reply.png" mode="" />
            </view>
            <input class="text-input" maxlength="{{-1}}" placeholder="{{question.hint}}" bindblur="onBlur" bindinput="onInput" value="{{inputValue}}" type="text" confirm-type="send" bindconfirm="sendMsg" />
          </view>
          <!-- <van-uploader file-list="{{fileList}}" max-count="1" bind:after-read="onAfterRead" bind:delete="onDeleteImage" preview-size="40rpx" /> -->
          <view class="addPic-container">
            <image class="addPic" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/addPic.png" mode="" bindtap="gotoQuestionDetail" />
            <view class="sendMsg" bindtap="sendMsg">
              发送
            </view>
          </view>

        </view>
        <view wx:if="{{recording}}" class="recording-tip">正在识别语音，请说话...</view>
        <view class="triangle"></view>
      </view>
      <view wx:else>
        <van-loading type="spinner" size="24px" vertical color="#F5A631">正在为您准备下一个问题，请稍候...</van-loading>
        <view class="triangle"></view>
      </view>
     
    </view>

  </view>
</view>