const plugin = requirePlugin("WechatSI");
import chatApi from '../../api/modules/chat';
let manager:any = null;

Component({
  properties: {
    question: {
      type: Object
    },
    visible: {
      type: Boolean,
      value: false
    },
    isPreParing:{
      type: Boolean,
      value: false
    }
  },
  data: {
    inputValue: '',
    recording: false,
    isInputting: false,
    isRecognizing: false,
    animationData: null,
    fileList: [] as any[], // 明确声明fileList为any[]
  },
  lifetimes: {
    attached() {
      manager = plugin.getRecordRecognitionManager();
      manager.onRecognize = (res: { result: any; }) => {
        // 实时识别结果
        if (res.result) {
          this.setData({ inputValue: res.result });
        }
      };
      manager.onStart = () => {
        this.setData({ isRecognizing: true });
      };
      manager.onStop = (res: { result: any; }) => {
        this.setData({ recording: false, isRecognizing: false  });
        if (res.result) {
          this.setData({ inputValue: res.result });
          this.triggerEvent('voice', { text: res.result });
        } else {
          wx.showToast({ title: '未识别到语音', icon: 'none' });
        }
      };
      manager.onError = () => {
        this.setData({ recording: false, isRecognizing: false });
        wx.showToast({ title: '语音识别出错', icon: 'none' });
      };
    }
  },
  methods: {
    onInput(e: { detail: { value: any; }; }) {
      this.setData({
        inputValue: e.detail.value,
        isInputting: true
      });
      this.triggerEvent('input', { value: e.detail.value });
    },
    onBlur() {
      this.setData({ isInputting: false });
    },
    startVoice() {
      this.setData({ recording: true, isRecognizing: true });
      manager.start({ lang: "zh_CN" });
    },
    stopVoice() {
      manager.stop();
    },
    show() {
      this.setData({ visible: true });
    },
    hide() {
      this.setData({ visible: false });
    },
    isBusy() {
      return this.data.isInputting || this.data.isRecognizing;
    },
    gotoQuestionDetail() {
      wx.navigateTo({
        url: '/pages/questionDetail/index',
        success: (res) => {
          res.eventChannel.emit('acceptDataFromDialog', { question: this.properties.question });
        }
      });
    },
    // van-uploader 选择图片后回调
    onAfterRead(event: any) {
      const { file } = event.detail;
      let fileList = this.data.fileList || [];
      // 支持多选
      const files = Array.isArray(file) ? file : [file];
      files.forEach((item) => {
        // 读取本地图片为base64
        wx.getFileSystemManager().readFile({
          filePath: item.url, // 临时路径
          encoding: 'base64',
          success: (res) => {
            // res.data 就是base64字符串
            item.base64 = res.data; // 可根据实际类型调整
            // 你可以在这里直接上传base64，或存到fileList
            // 例如：console.log(item.base64);
            // 或者将base64存入fileList
            console.log('item.base64 ',item.base64 )
            fileList.push(item);
            this.setData({ fileList });
          },
          fail: (err) => {
            wx.showToast({ title: '图片读取失败', icon: 'none' });
          }
        });
      });
     
    },
    // 删除图片
    onDeleteImage(event: any) {
      const { index } = event.detail;
      const fileList = this.data.fileList.slice();
      fileList.splice(index, 1);
      this.setData({ fileList });
    },
    // 提交用户回复
    async sendMsg() {
      const question = this.properties.question;
      const params = {
        chain_id:question.chain_id,
        question:question.q,
        reply:this.data.inputValue,
        image_file_base64:this.data.fileList?.length ? this.data.fileList[0].base64 : ''
      }
      const {code} =await chatApi.sendReply(params, 'post')
      if(code === 2000){
        this.triggerEvent('refreshquestion');
        this.setData({
          inputValue:'',
          fileList:[]
        })
      }
    }
  }
});