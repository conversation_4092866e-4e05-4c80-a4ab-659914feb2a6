// utils/api-service.js

/**
 * API服务模拟 - 用于演示流式内容生成
 * 实际应用中应该替换为真实的API调用
 */

class APIService {
  constructor() {
    this.baseURL = 'https://your-api-domain.com';
    this.apiKey = 'your-api-key';
  }

  /**
   * 流式生成章节内容
   * @param {string} chapterId 章节ID
   * @param {Object} options 生成选项
   * @returns {Promise} 返回流式数据
   */
  async generateChapterStream(chapterId, options = {}) {
    const {
      prompt = '',
      maxLength = 2000,
      temperature = 0.7,
      onChunk = null,
      onComplete = null,
      onError = null
    } = options;

    try {
      // 模拟流式响应
      return await this.simulateStreamResponse(chapterId, {
        maxLength,
        onChunk,
        onComplete,
        onError
      });
    } catch (error) {
      console.error('Generate chapter stream error:', error);
      if (onError) onError(error);
      throw error;
    }
  }

  /**
   * 模拟流式响应
   */
  async simulateStreamResponse(chapterId, options) {
    const { maxLength, onChunk, onComplete, onError } = options;
    
    // 模拟内容片段
    const contentChunks = this.generateContentChunks(chapterId, maxLength);
    
    let generatedContent = '';
    
    for (let i = 0; i < contentChunks.length; i++) {
      try {
        // 模拟网络延迟
        await this.delay(300 + Math.random() * 500);
        
        const chunk = contentChunks[i];
        generatedContent += chunk;
        
        // 调用回调函数
        if (onChunk) {
          onChunk({
            chunk,
            totalContent: generatedContent,
            progress: (i + 1) / contentChunks.length,
            isComplete: i === contentChunks.length - 1
          });
        }
        
        // 模拟可能的错误
        if (Math.random() < 0.05) { // 5% 概率出错
          throw new Error('模拟网络错误');
        }
        
      } catch (error) {
        if (onError) onError(error);
        throw error;
      }
    }
    
    if (onComplete) {
      onComplete({
        content: generatedContent,
        chapterId,
        wordCount: generatedContent.length
      });
    }
    
    return generatedContent;
  }

  /**
   * 生成内容片段
   */
  generateContentChunks(chapterId, maxLength) {
    const chapterNum = this.extractChapterNumber(chapterId);
    const storyTemplates = this.getStoryTemplates(chapterNum);
    
    const chunks = [];
    let currentLength = 0;
    let templateIndex = 0;
    
    while (currentLength < maxLength && templateIndex < storyTemplates.length) {
      const template = storyTemplates[templateIndex];
      const chunkSize = Math.min(150 + Math.random() * 100, maxLength - currentLength);
      
      let chunk = this.generateChunkFromTemplate(template, chunkSize);
      
      // 确保在合适的地方断句
      if (currentLength + chunk.length < maxLength) {
        chunk = this.ensureProperEnding(chunk);
      }
      
      chunks.push(chunk);
      currentLength += chunk.length;
      templateIndex++;
      
      // 如果模板用完了，重新开始
      if (templateIndex >= storyTemplates.length) {
        templateIndex = 0;
      }
    }
    
    return chunks;
  }

  /**
   * 提取章节号
   */
  extractChapterNumber(chapterId) {
    const match = chapterId.match(/(\d+)/);
    return match ? parseInt(match[1]) : 1;
  }

  /**
   * 获取故事模板
   */
  getStoryTemplates(chapterNum) {
    const baseTemplates = [
      {
        type: 'scene_setting',
        content: '在一个{adjective}的{time}，{character}来到了{location}。这里{description}，让人感到{emotion}。'
      },
      {
        type: 'character_action',
        content: '{character}{action}，心中想着{thought}。{he_she}知道{situation}，必须{decision}。'
      },
      {
        type: 'dialogue',
        content: '"{dialogue1}，"{character1}说道。\n\n"{dialogue2}，"{character2}回答。\n\n两人{interaction}。'
      },
      {
        type: 'conflict',
        content: '突然，{unexpected_event}。{character}感到{emotion}，{he_she}{reaction}。这{consequence}。'
      },
      {
        type: 'reflection',
        content: '{character}停下脚步，回想起{memory}。{he_she}明白了{realization}，这让{he_she}{feeling}。'
      }
    ];

    // 根据章节号调整模板内容
    return baseTemplates.map(template => ({
      ...template,
      chapterContext: this.getChapterContext(chapterNum)
    }));
  }

  /**
   * 获取章节上下文
   */
  getChapterContext(chapterNum) {
    const contexts = {
      1: {
        character: '小明',
        location: '神秘的森林',
        situation: '开始冒险',
        theme: '勇气与探索'
      },
      2: {
        character: '小明',
        location: '古老的城堡',
        situation: '寻找线索',
        theme: '智慧与谜题'
      },
      3: {
        character: '小明',
        location: '地下洞穴',
        situation: '面对挑战',
        theme: '坚持与成长'
      }
    };

    return contexts[chapterNum] || contexts[1];
  }

  /**
   * 从模板生成内容块
   */
  generateChunkFromTemplate(template, targetLength) {
    const context = template.chapterContext;
    const variables = {
      character: context.character,
      location: context.location,
      adjective: this.getRandomItem(['美丽', '神秘', '危险', '宁静', '古老']),
      time: this.getRandomItem(['清晨', '黄昏', '深夜', '午后', '黎明']),
      description: this.getRandomItem([
        '充满了未知的气息',
        '散发着古老的魔力',
        '隐藏着无数秘密',
        '让人心生敬畏',
        '仿佛在诉说着古老的传说'
      ]),
      emotion: this.getRandomItem(['好奇', '紧张', '兴奋', '敬畏', '期待']),
      action: this.getRandomItem(['缓缓前行', '仔细观察', '深深思考', '小心探索', '勇敢前进']),
      thought: this.getRandomItem([
        '这次冒险的意义',
        '前方等待的挑战',
        '家人的期望',
        '内心的使命',
        '未来的可能'
      ]),
      he_she: context.character.includes('明') ? '他' : '她',
      situation: context.situation,
      decision: this.getRandomItem(['勇敢面对', '仔细计划', '寻求帮助', '相信自己', '坚持到底'])
    };

    let content = template.content;
    
    // 替换变量
    Object.keys(variables).forEach(key => {
      const regex = new RegExp(`{${key}}`, 'g');
      content = content.replace(regex, variables[key]);
    });

    // 扩展内容到目标长度
    while (content.length < targetLength) {
      const extension = this.generateExtension(template.type, context);
      content += extension;
    }

    return content.substring(0, targetLength);
  }

  /**
   * 生成扩展内容
   */
  generateExtension(type, context) {
    const extensions = {
      scene_setting: [
        '\n\n远处传来了鸟儿的歌声，微风轻抚过树叶，发出沙沙的响声。',
        '\n\n阳光透过云层洒下，在地面上形成斑驳的光影。',
        '\n\n空气中弥漫着花草的香气，让人心旷神怡。'
      ],
      character_action: [
        '\n\n每一步都走得很坚定，因为心中有着明确的目标。',
        '\n\n虽然前路未卜，但内心充满了勇气和决心。',
        '\n\n这是一个重要的决定，将会改变一切。'
      ],
      dialogue: [
        '\n\n话语中透露出深深的关切和理解。',
        '\n\n这番对话让彼此的心更加贴近。',
        '\n\n言语虽简，但意义深远。'
      ],
      conflict: [
        '\n\n这个转折让所有人都始料未及。',
        '\n\n局面变得更加复杂和充满挑战。',
        '\n\n但这也是成长路上必经的考验。'
      ],
      reflection: [
        '\n\n过往的经历如电影般在脑海中闪过。',
        '\n\n每一个细节都有着特殊的意义。',
        '\n\n这些回忆将成为前进的动力。'
      ]
    };

    const typeExtensions = extensions[type] || extensions.scene_setting;
    return this.getRandomItem(typeExtensions);
  }

  /**
   * 确保内容以合适的方式结束
   */
  ensureProperEnding(content) {
    const endings = ['。', '！', '？', '…'];
    const lastChar = content[content.length - 1];
    
    if (!endings.includes(lastChar)) {
      // 找到最后一个完整句子的结尾
      let lastSentenceEnd = -1;
      for (let i = content.length - 1; i >= 0; i--) {
        if (endings.includes(content[i])) {
          lastSentenceEnd = i;
          break;
        }
      }
      
      if (lastSentenceEnd > content.length * 0.8) {
        // 如果最后一个句子结尾位置合理，截取到那里
        content = content.substring(0, lastSentenceEnd + 1);
      } else {
        // 否则添加省略号
        content += '…';
      }
    }
    
    return content;
  }

  /**
   * 获取随机项目
   */
  getRandomItem(array) {
    return array[Math.floor(Math.random() * array.length)];
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取章节列表
   */
  async getChapterList() {
    // 模拟API调用
    await this.delay(500);
    
    return [
      { id: 'chapter_1', title: '第一章 神秘的开始', status: 'available' },
      { id: 'chapter_2', title: '第二章 未知的旅程', status: 'locked' },
      { id: 'chapter_3', title: '第三章 重要的发现', status: 'locked' },
      { id: 'chapter_4', title: '第四章 转折点', status: 'locked' },
      { id: 'chapter_5', title: '第五章 最终的选择', status: 'locked' }
    ];
  }

  /**
   * 保存阅读进度到服务器
   */
  async saveReadingProgress(progress) {
    try {
      // 模拟API调用
      await this.delay(200);
      
      console.log('保存阅读进度:', progress);
      return { success: true };
    } catch (error) {
      console.error('保存进度失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 从服务器加载阅读进度
   */
  async loadReadingProgress(userId) {
    try {
      // 模拟API调用
      await this.delay(300);
      
      // 返回模拟的进度数据
      return {
        success: true,
        data: {
          currentChapter: 'chapter_1',
          currentPage: 0,
          totalReadTime: 1800, // 秒
          lastReadTime: Date.now() - 3600000 // 1小时前
        }
      };
    } catch (error) {
      console.error('加载进度失败:', error);
      return { success: false, error };
    }
  }
}

// 创建单例实例
const apiService = new APIService();

export default apiService;

