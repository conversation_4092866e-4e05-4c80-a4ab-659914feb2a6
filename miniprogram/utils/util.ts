import Toast from '@vant/weapp/toast/toast';
import { Home } from '~/utils/router';

/**
 * @method getItemSync 缓存读取
 * @param {*} key
 */
export const getItemSync = (key: string) => {
  try {
    return wx.getStorageSync(key);
  } catch (err) {
    console.error('wx.getStorageSync(key)', err);
  }
};

/**
 * @method setItemSync 缓存存储
 * @param {*} key
 * @param {*} value
 */
export const setItemSync = (key: string, value: any) => {
  try {
    wx.setStorageSync(key, value);
  } catch (err) {
    console.error('wx.setStorageSync(key, value)', err);
  }
};

/**
 * @method deleteItemSync 缓存删除
 * @param {*} key
 */
export const deleteItemSync = (key: string) => {
  try {
    return wx.removeStorageSync(key);
  } catch (err) {
    console.error('wx.removeStorageSync(key)', err);
  }
};

/**
 * @method navigateTo 封装navigateTo请求
 * @param {*} { url, events }
 */
export const navigateTo = ({ url, events = {} }: { url: string; events?: any }) => {
  return new Promise((resolve) => {
    const { globalData } = getApp();
    const { isConnected } = globalData;
    // 有网络
    if (isConnected) {
      wx.navigateTo({
        url,
        events,
        success: resolve,
        fail: () => {
          redirectTo({ url, events });
        },
      });

      // 无网络
    } else {
      Toast('似乎已经断开了与互联网的连接');
    }
  });
};

/**
 * @method redirectTo 封装redirectTo请求
 * @param {*} { url, events }
 */
export const redirectTo = ({ url, events = {} }: { url: string; events?: any }) => {
  return new Promise((resolve, reject) => {
    const { globalData } = getApp();
    const { isConnected } = globalData;
    // 有网络
    if (isConnected) {
      wx.redirectTo({
        url,
        events,
        success: resolve,
        fail: reject,
      });

      // 无网络
    } else {
      Toast('似乎已经断开了与互联网的连接');
    }
  });
};

/**
 * @method navigateBack 封装navigateBack请求
 * @param {*} delta
 */
export const navigateBack = (delta: number = 1) => {
  return new Promise((resolve, reject) => {
    const { globalData } = getApp();
    const { isConnected } = globalData;
    // 有网络
    if (isConnected) {
      wx.navigateBack({
        delta,
        success: resolve,
        fail: reject,
      });

      // 无网络
    } else {
      Toast('似乎已经断开了与互联网的连接');
    }
  });
};

/**
 * @method switchTab 封装switchTab请求
 * @param {*} { url }
 */
export const switchTab = ({ url }: { url: string }) => {
  return new Promise((resolve, reject) => {
    const { globalData } = getApp();
    const { isConnected } = globalData;
    // 有网络
    if (isConnected) {
      wx.switchTab({
        url,
        success: resolve,
        fail: reject,
      });

      // 无网络
    } else {
      Toast('似乎已经断开了与互联网的连接');
    }
  });
};

/**
 * @method reLaunch 封装reLaunch请求
 * @param {*} { url }
 */
export const reLaunch = ({ url }: { url: string }) => {
  return new Promise((resolve, reject) => {
    const { globalData } = getApp();
    const { isConnected } = globalData;
    // 有网络
    if (isConnected) {
      wx.reLaunch({
        url,
        success: resolve,
        fail: reject,
      });

      // 无网络
    } else {
      Toast('似乎已经断开了与互联网的连接');
    }
  });
};

/**
 * @method getCurrentPageInfo 获取当前页面栈中指定路径的页面信息
 * @param {*} path app.json中定义的完整路径
 */
export const getCurrentPageInfo = (path: string) => {
  // 存在指定路径， 返回指定路径页面详情
  if (path) {
    // 反转数组，返回最后一次出现路由
    return getCurrentPages()
      .reverse()
      .find((item) => {
        return `/${item.route}` === path;
      });

    // 反转数组,返回当前页面详情
  } else {
    return getCurrentPages().reverse()[0];
  }
};

/**
 * @method getCurrentPageIndex 获取当前页面栈中指定路径的下标
 * @param {*} path app.json中定义的完整路径
 */
export const getCurrentPageIndex = (path: string) => {
  return getCurrentPages()
    .reverse()
    .findIndex((item) => {
      return `/${item.route}` === path;
    });
};

/**
 * @method checkNetwork 检查网络
 */
export const checkNetwork = () => {
  return new Promise<void>((resolve, reject) => {
    const { globalData } = getApp();
    const { isConnected } = globalData;
    if (isConnected) {
      resolve();
    } else {
      Toast('似乎已经断开了与互联网的连接');
      reject('似乎已经断开了与互联网的连接');
    }
  });
};

/**
 * @method getNetworkType 获取网络类型
 */
export const getNetworkType = () => {
  return new Promise((resolve) => {
    // 获取网络类型
    wx.getNetworkType({
      success: (value) => {
        const { networkType } = value;
        resolve(networkType);
      },
    });
  });
};

/**
 * @method logout 退出登录
 */
export const logout = () => {
  const { globalData } = getApp();
  globalData.userInfo = null;
  deleteItemSync('userInfo');
  reLaunch({ url: Home.path });
};

/**
 * @method shareImageFormat 图片格式处理
 * @param {*} url
 * @returns
 */
export const shareImageFormat = (url: string) => {
  const { systemInfo } = getApp();
  const { system } = systemInfo;
  const systemDetail = system.split(' ');
  const type = systemDetail[0];
  const version = systemDetail[1];
  // ios系统14版本以下，小程序分享的图片不支持webp格式，进行转换
  if (type.toLowerCase() === 'ios' && parseInt(version) <= 14) {
    return url.replace('format,webp', 'format,jpg');
  } else {
    return url;
  }
};

export const getSystemInfoSync = () => {
  const systemSetting = wx.getSystemSetting();
  const appAuthorizeSetting = wx.getAppAuthorizeSetting();
  const deviceInfo = wx.getDeviceInfo();
  const windowInfo = wx.getWindowInfo();
  const appBaseInfo = wx.getAppBaseInfo();
  return {
    ...systemSetting,
    ...appAuthorizeSetting,
    ...deviceInfo,
    ...windowInfo,
    ...appBaseInfo,
  };
};

/**
 * 将数组元素循环右移一位（最后一位移到第一位，其他元素依次后移）
 * @param {Array} arr - 待处理的数组
 * @param {boolean} [inPlace=true] - 是否原地修改数组（默认为 true）
 * @returns {Array} - 返回修改后的数组（若 inPlace 为 true，则返回原数组引用）
 */
export const rotateArrayRight = (arr: Array<any>, inPlace: boolean = true): Array<any> => {
  if (!Array.isArray(arr) || arr.length <= 1) {
    return arr; // 空数组或单元素数组无需处理
  }

  if (inPlace) {
    // 原地修改：移除最后一位并添加到开头
    arr.unshift(arr.pop());
    return arr;
  } else {
    // 非原地修改：创建新数组
    return [arr[arr.length - 1], ...arr.slice(0, arr.length - 1)];
  }
}

/**
 * 将 0 到 100 的整数转换为中文读法。
 *
 * @param num 要转换的数字 (0-100)。
 * @returns 对应的中文读法字符串，如果输入超出范围或无效则返回空字符串。
 */
export const  convertNumberToChineseWithin100 = (str: string): string  =>{
  const num = parseInt(str);
  const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];

  if (num === 0) {
    return '零';
  } else if (num === 100) {
    return '一百';
  } else if (num < 10) {
    return chineseNums[num];
  } else if (num < 20) {
    // 10 到 19 的数字，例如 10 (十), 11 (十一)
    return `十${num % 10 === 0 ? '' : chineseNums[num % 10]}`;
  } else {
    // 20 到 99 的数字，例如 20 (二十), 21 (二十一)
    const tens = Math.floor(num / 10);
    const ones = num % 10;
    return `${chineseNums[tens]}十${ones === 0 ? '' : chineseNums[ones]}`;
  }
}

// 定义人生阶段数据结构接口
export interface PeriodInfo {
  period_id: string;
  name: string;
  description: string;
  topics: any[];
  bgUrl?: string;
}

/**
 * 将人生阶段对象转换为数组形式
 * @param {Object} periodObj - 包含各人生阶段的原始对象（如basic_info、childhood等）
 * @returns {Array} 转换后的人生阶段数组，每个元素包含对应阶段的完整信息
 */
export const convertPeriodsToArray = (periodObj: any): PeriodInfo[] => {
  // 校验输入是否为有效对象
  if (typeof periodObj !== 'object' || periodObj === null) {
    console.error('输入必须是有效的对象');
    return [];
  }



  // 提取对象的所有键（即各阶段的period_id），并转换为数组
  return Object.keys(periodObj).map(periodKey => {
    console.log('periodKey',periodKey)
    // 获取对应阶段的完整信息（包含period_id、name、topics等）
    const periodInfo = periodObj[periodKey];
    // 返回阶段信息（确保结构完整）
    return {
      period_id: periodInfo.period_id,
      name: periodInfo.name,
      description: periodInfo.description,
      topics: periodInfo.topics || [], // 确保topics为数组，避免undefined
    };
  });
}

    // 去重处理：根据title去重
export const removeDuplicateContent = (contentData: any[]): any[] => {
    const seen = new Set();
    const uniqueContent = contentData.filter(item => {
      const title = item || '';
      if (seen.has(title)) {
        return false;
      }
      seen.add(title);
      return true;
    });

  
    return uniqueContent;
  }

/**
 * 统计每个topic_id和title的出现次数
 * @param {Array} storyList - 包含story对象的数组，每个story包含content数组
 * @returns {Object} 返回包含topic_id统计和title统计的对象
 */
export const countTopicIdOccurrences = (storyList: any[]): {
  topicIdCounts: Array<{[key: string]: number}>;
  titleCounts: Array<{[key: string]: number}>;
} => {
  const topicIdCounts: {[key: string]: number} = {};
  const titleCounts: {[key: string]: number} = {};
  
  // 遍历所有story的content
  storyList.forEach((story: { content: any[] }) => {
    story.content.forEach((item: { topic_id: string; title: string }) => {
      const topicId = item.topic_id;
      const title = item.title;
      
      // 统计topic_id
      if (topicId) {
        topicIdCounts[topicId] = (topicIdCounts[topicId] || 0) + 1;
      }
      
      // 统计title
      if (title) {
        titleCounts[title] = (titleCounts[title] || 0) + 1;
      }
    });
  });
  
  // 转换为数组格式
  const topicIdResult = Object.keys(topicIdCounts).map(key => {
    const obj: {[key: string]: number} = {};
    obj[key] = topicIdCounts[key];
    return obj;
  });
  
  const titleResult = Object.keys(titleCounts).map(key => {
    const obj: {[key: string]: number} = {};
    obj[key] = titleCounts[key];
    return obj;
  });
  
  return {
    topicIdCounts: topicIdResult,
    titleCounts: titleResult
  };
}

