/**
 * App initialization utilities
 * Handles post-login data fetching and initialization
 */

import { storage } from '../config/index';
import API from '../api/index';

/**
 * Initialize user data after successful login
 * Fetches user information and session data
 * 
 * @returns {Promise} - Promise that resolves to {success: true} when finished
 */
export const initUserData = async () => {
  try {
    console.log('Initializing user data...');
    
    // Verify we have a token before proceeding
    const token = wx.getStorageSync(storage.accessToken);
    if (!token) {
      console.error('无法初始化用户数据: 未找到访问令牌');
      return { success: false, error: '未找到访问令牌' };
    }
    
    // Get user profile data
    try {
      const userProfileRes = await API.user.getProfile();
      console.log('原始用户信息响应:', userProfileRes);
      
      // 尝试从不同的可能的响应结构中提取用户数据
      let userData = null;
      
      // 检查各种可能的响应格式
      if (userProfileRes && userProfileRes.data) {
        // 如果响应格式是 { data: { user info } }
        userData = userProfileRes.data;
      } else if (userProfileRes && typeof userProfileRes === 'object' && userProfileRes.uuid) {
        // 如果响应本身就是用户数据对象
        userData = userProfileRes;
      } else if (userProfileRes && userProfileRes.user) {
        // 如果响应格式是 { user: { user info } }
        userData = userProfileRes.user;
      } else if (userProfileRes && userProfileRes.userInfo) {
        // 如果响应格式是 { userInfo: { user info } }
        userData = userProfileRes.userInfo;
      }
      
      if (userData) {
        console.log('用户信息获取成功');
        
        // 处理用户标签权重信息
        if (userData.interest_tags) {
          console.log('用户兴趣标签:', Object.keys(userData.interest_tags));
        }
        
        // 存储用户信息
        wx.setStorageSync(storage.userInfo, userData);
        
        // Update global data in App instance
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.userInfo = userData;
          app.globalData.isLoggedIn = true;
          
          // 特别记录链数量等关键信息
          if (userData.total_chain_count) {
            console.log('用户已完成问题链数量:', userData.total_chain_count);
          }
          if (userData.total_asked_chain_count) {
            console.log('用户已提问链数量:', userData.total_asked_chain_count);
          }
        }
      } else {
        console.warn('无法从响应中提取有效的用户数据:', userProfileRes);
      }
    } catch (profileError) {
      console.error('获取用户信息失败:', profileError);
    }
    
    // Get all sessions
    try {
      const sessionsRes = await API.content.getAllSessions();
      console.log('获取到的会话列表原始响应:', sessionsRes);
      
      // 尝试从不同的可能的响应结构中提取会话数据
      let sessionsList = null;
      
      if (sessionsRes && Array.isArray(sessionsRes.sessions)) {
        // 如果响应格式是 { sessions: [] }
        sessionsList = sessionsRes.sessions;
      } else if (sessionsRes && Array.isArray(sessionsRes.data)) {
        // 如果响应格式是 { data: [] }
        sessionsList = sessionsRes.data;
      } else if (sessionsRes && sessionsRes.sessions && sessionsRes.sessions.sessions) {
        // 处理嵌套的情况 { sessions: { sessions: [] } }
        sessionsList = sessionsRes.sessions.sessions;
      } else if (sessionsRes && Array.isArray(sessionsRes)) {
        // 如果响应本身就是数组
        sessionsList = sessionsRes;
      }
      
      if (sessionsList && Array.isArray(sessionsList)) {
        console.log('会话列表获取成功:', sessionsList.length + '个会话');
        
        // 确保会话有正确的结构
        const validSessions = sessionsList.filter(session => session && session.session_id);
        
        if (validSessions.length > 0) {
          // 保存会话列表数据
          wx.setStorageSync('sessions', validSessions);
          
          // 确保有一个活跃会话ID
          const latestSession = validSessions[0];
          if (latestSession && latestSession.session_id) {
            wx.setStorageSync('activeChatSession', latestSession.session_id);
            console.log('已设置活跃会话ID:', latestSession.session_id);
          }
          
          // Emit global event to notify components that sessions are loaded
          const app = getApp();
          if (app && app.globalEvent) {
            app.globalEvent.emit('sessionsLoaded', validSessions);
          }
        } else {
          console.warn('提取的会话列表中没有有效的会话');
        }
      } else {
        console.warn('无法从响应中提取有效的会话列表数据');
      }
    } catch (sessionsError) {
      console.error('获取会话列表失败:', sessionsError);
    }
    
    console.log('用户数据初始化完成');
    return { success: true };
  } catch (error) {
    console.error('初始化用户数据时出错:', error);
    return { 
      success: false, 
      error: error.message || '初始化用户数据失败'
    };
  }
};

/**
 * Check login status and initialize data if logged in
 * 
 * @returns {Promise<boolean>} - Promise that resolves to login status
 */
export const checkLoginAndInitialize = async () => {
  try {
    // 使用API的统一认证方法检查是否已登录
    if (!API.isAuthenticated()) {
      console.log('未登录状态，需要登录');
      return false;
    }
    
    // 用户已登录，初始化用户数据
    await initUserData();
    return true;
  } catch (error) {
    console.error('检查登录状态时出错:', error);
    return false;
  }
};

/**
 * Clear all user data from storage
 * Use during logout or when resetting the app
 */
export const clearUserData = () => {
  wx.removeStorageSync(storage.accessToken);
  wx.removeStorageSync(storage.refreshToken);
  wx.removeStorageSync(storage.userInfo);
  wx.removeStorageSync('sessions');
  
  // Update global data in App instance
  const app = getApp();
  if (app && app.globalData) {
    app.globalData.userInfo = null;
    app.globalData.isLoggedIn = false;
  }
  
  console.log('用户数据已清除');
};

export default {
  initUserData,
  checkLoginAndInitialize,
  clearUserData
}; 