var computed = {
containerStyle: function (params) {
  var showToolTip = params.showToolTip;
  var showCopyBtn = params.showCopyBtn;
  var activeBgColor = params.activeBgColor || '#DEDEDE';
  var style = '';
  if (showToolTip) {
    style += 'background:' + activeBgColor + ';';
  }
  return style;
},
isSelected: function (idx, start, end) {
  if (start === null || start === undefined) return false;
  if (end === null || end === undefined) return false;
  var s = start < end ? start : end;
  var e = start < end ? end : start;
  return idx >= s && idx <= e;
}
}
module.exports = computed