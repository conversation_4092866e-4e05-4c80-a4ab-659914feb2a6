/**
* Image caching utility for WeChat Mini Program
* - Persist downloaded images with wx.saveFile
* - Map remote URL -> local saved path in storage
* - TTL based expiration and validation
*/

const STORAGE_KEY_PREFIX = 'IMG_CACHE_V1:';
const DEFAULT_TTL_MS = 7 * 24 * 60 * 60 * 1000; // 7 days

interface ImageCacheRecord {
 path: string;
 expireAt: number; // epoch ms
}

function buildStorageKey(url: string): string {
  // Keep the key reasonably short
  return STORAGE_KEY_PREFIX + encodeURIComponent(url);
}

function now(): number {
  return Date.now();
}

function readCache(url: string): ImageCacheRecord | null {
  try {
    const key = buildStorageKey(url);
    const rec = wx.getStorageSync(key) as ImageCacheRecord | undefined;
    if (!rec || !rec.path || !rec.expireAt) return null;
    if (rec.expireAt < now()) return null;
    return rec;
  } catch (_) {
    return null;
  }
}

function writeCache(url: string, path: string, ttlMs: number): void {
  const key = buildStorageKey(url);
  const record: ImageCacheRecord = {
    path,
    expireAt: now() + (ttlMs || DEFAULT_TTL_MS),
  };
 try {
   wx.setStorageSync(key, record);
 } catch (_) {
   // ignore storage errors
 }
}

function removeCache(url: string): void {
 try {
   const key = buildStorageKey(url);
   wx.removeStorageSync(key);
 } catch (_) {}
}

function fileExists(path: string): boolean {
 try {
   const fs = wx.getFileSystemManager();
   fs.statSync(path);
   return true;
 } catch (_) {
   return false;
 }
}

function downloadFile(url: string): Promise<string> {
 return new Promise((resolve, reject) => {
   wx.downloadFile({
     url,
     success(res) {
       if (res.statusCode === 200 && res.tempFilePath) {
         resolve(res.tempFilePath);
       } else {
         reject(new Error('download failed: ' + res.statusCode));
       }
     },
     fail(err) {
       reject(err);
     },
   });
 });
}

function saveFile(tempPath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      wx.saveFile({
        tempFilePath: tempPath,
        success(res) {
          if (res.savedFilePath) resolve(res.savedFilePath);
          else reject(new Error('saveFile: no savedFilePath'));
        },
        fail(err) {
          reject(err);
        },
      });
    } catch (err) {
      reject(err as any);
    }
  });
}

/**
 * Get a locally cached image path for a remote URL.
 * - If valid cache exists, returns saved local path
 * - Otherwise downloads and persists, caches, and returns saved path
 */
export async function getCachedImagePath(url: string, options?: { ttlMs?: number }): Promise<string> {
  if (!url) throw new Error('url is required');

  // 1) Try storage cache
  const cached = readCache(url);
  if (cached && fileExists(cached.path)) {
    return cached.path;
  }

  // 2) Download and persist
  const tempPath = await downloadFile(url);
  let savedPath = '';
  try {
    savedPath = await saveFile(tempPath);
  } catch (err) {
    // Fallback: use temp path if saveFile fails
    savedPath = tempPath;
  }

  // 3) Write cache record
  writeCache(url, savedPath, options?.ttlMs ?? DEFAULT_TTL_MS);
  return savedPath;
}

/**
 * Clear the cached record (and optionally remove saved file).
 */
export function evictImageCache(url: string, removeFile: boolean = false): void {
  const cached = readCache(url);
  removeCache(url);
  if (removeFile && cached && cached.path) {
    try {
      const fs = wx.getFileSystemManager();
      fs.unlinkSync(cached.path);
    } catch (_) {}
  }
}

/**
 * Clear all expired image cache records. Optionally remove files.
 */
export function clearExpiredImageCache(removeFiles: boolean = false): void {
  try {
    const info = wx.getStorageInfoSync();
    const keys = info.keys || [];
    const n = now();
    const fs = removeFiles ? wx.getFileSystemManager() : null;
    keys.forEach((key: string) => {
      if (!key.startsWith(STORAGE_KEY_PREFIX)) return;
      try {
        const rec = wx.getStorageSync(key) as ImageCacheRecord | undefined;
        if (!rec || !rec.expireAt || rec.expireAt < n) {
          wx.removeStorageSync(key);
          if (removeFiles && rec && rec.path) {
            try { fs?.unlinkSync(rec.path); } catch (_) {}
          }
        }
      } catch (_) {}
    });
  } catch (_) {}
}

/**
 * Convenience helper: resolve a displayable src for <image>
 * Returns local cached path if possible, otherwise the original url
 */
export async function resolveImageSrc(url: string, options?: { ttlMs?: number }): Promise<string> {
  try {
    const local = await getCachedImagePath(url, options);
    return local || url;
  } catch (_) {
    return url;
  }
}