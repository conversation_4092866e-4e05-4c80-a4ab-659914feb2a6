/**
 * 设备检测工具类
 * 用于检测设备类型和选择合适的预览方式
 */

interface DeviceInfo {
  platform: string;
  system: string;
  isIOS: boolean;
  isAndroid: boolean;
  isHuawei: boolean;
  supportWebViewPDF: boolean;
  recommendedPreviewMethod: 'webview' | 'download' | 'external';
}

class DeviceHelper {
  private static instance: DeviceHelper;
  private deviceInfo: DeviceInfo | null = null;

  private constructor() {
    this.initDeviceInfo();
  }

  public static getInstance(): DeviceHelper {
    if (!DeviceHelper.instance) {
      DeviceHelper.instance = new DeviceHelper();
    }
    return DeviceHelper.instance;
  }

  /**
   * 初始化设备信息
   */
  private initDeviceInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const platform = systemInfo.platform?.toLowerCase() || '';
      const system = systemInfo.system?.toLowerCase() || '';
      const brand = systemInfo.brand?.toLowerCase() || '';
      const model = systemInfo.model?.toLowerCase() || '';

      const isIOS = platform === 'ios' || system.includes('ios');
      const isAndroid = platform === 'android' || system.includes('android');
      const isHuawei = brand.includes('huawei') || model.includes('huawei') || brand.includes('honor');

      // 判断是否支持 web-view PDF 预览
      const supportWebViewPDF = isIOS; // 主要是iOS支持web-view PDF预览

      // 推荐的预览方式
      let recommendedPreviewMethod: 'webview' | 'download' | 'external' = 'download';
      if (isIOS) {
        recommendedPreviewMethod = 'webview';
      } else if (isAndroid || isHuawei) {
        // Android设备和华为设备都使用下载预览
        recommendedPreviewMethod = 'download';
      }

      this.deviceInfo = {
        platform,
        system,
        isIOS,
        isAndroid,
        isHuawei,
        supportWebViewPDF,
        recommendedPreviewMethod
      };

      console.log('设备信息初始化完成:', this.deviceInfo);
    } catch (error) {
      console.error('获取设备信息失败:', error);
      // 默认设备信息
      this.deviceInfo = {
        platform: 'unknown',
        system: 'unknown',
        isIOS: false,
        isAndroid: false,
        isHuawei: false,
        supportWebViewPDF: false,
        recommendedPreviewMethod: 'download'
      };
    }
  }

  /**
   * 获取设备信息
   */
  public getDeviceInfo(): DeviceInfo {
    if (!this.deviceInfo) {
      this.initDeviceInfo();
    }
    return this.deviceInfo!;
  }

  /**
   * 是否为iOS设备
   */
  public isIOS(): boolean {
    return this.getDeviceInfo().isIOS;
  }

  /**
   * 是否为Android设备
   */
  public isAndroid(): boolean {
    return this.getDeviceInfo().isAndroid;
  }

  /**
   * 是否为华为设备
   */
  public isHuawei(): boolean {
    return this.getDeviceInfo().isHuawei;
  }

  /**
   * 是否支持web-view PDF预览
   */
  public supportWebViewPDF(): boolean {
    return this.getDeviceInfo().supportWebViewPDF;
  }

  /**
   * 获取推荐的预览方式
   */
  public getRecommendedPreviewMethod(): 'webview' | 'download' | 'external' {
    return this.getDeviceInfo().recommendedPreviewMethod;
  }

  /**
   * 获取设备特定的错误提示
   */
  public getDeviceSpecificErrorMessage(_error?: any): string {
    const deviceInfo = this.getDeviceInfo();

    if (deviceInfo.isHuawei) {
      return '华为设备预览失败，请尝试重新预览或复制链接';
    }

    if (deviceInfo.isAndroid) {
      return 'Android设备预览失败，请检查网络连接或重试';
    }

    if (deviceInfo.isIOS) {
      return 'iOS设备预览失败，请检查网络连接或尝试系统预览';
    }

    return '预览失败，请尝试其他预览方式';
  }

  /**
   * 处理PDF URL（根据设备类型优化）
   */
  public processPDFUrl(originalUrl: string): string {
    let processedUrl = originalUrl;
    
    // 确保使用HTTPS
    if (processedUrl.startsWith('http://')) {
      processedUrl = processedUrl.replace('http://', 'https://');
    }
    
    const deviceInfo = this.getDeviceInfo();
    
    // 华为设备特殊处理
    if (deviceInfo.isHuawei) {
      const separator = processedUrl.includes('?') ? '&' : '?';
      processedUrl = `${processedUrl}${separator}t=${Date.now()}&device=huawei`;
    }
    
    // iOS设备优化
    if (deviceInfo.isIOS) {
      const separator = processedUrl.includes('?') ? '&' : '?';
      processedUrl = `${processedUrl}${separator}platform=ios`;
    }
    
    return processedUrl;
  }

  /**
   * 获取设备信息摘要（用于调试）
   */
  public getDeviceSummary(): string {
    const info = this.getDeviceInfo();
    return `${info.platform} | ${info.isIOS ? 'iOS' : info.isAndroid ? 'Android' : 'Other'} | ${info.isHuawei ? 'Huawei' : 'Other'} | WebView PDF: ${info.supportWebViewPDF ? 'Yes' : 'No'}`;
  }
}

export default DeviceHelper;
