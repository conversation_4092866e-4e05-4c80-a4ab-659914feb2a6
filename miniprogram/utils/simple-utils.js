// utils/simple-utils.js

/**
 * 精简版阅读器工具函数
 */

/**
 * 内容分页管理器
 */
class ContentPaginator {
  constructor() {
    this.content = '';
    this.pages = [];
    this.wordsPerPage = 500;
    this.firstPageWordsPerPage = 300; // 第一页字数更少，为标题预留空间
    this.isComplete = false;
  }

  /**
   * 设置内容
   */
  setContent(content, isComplete = false) {
    this.content = content;
    this.isComplete = isComplete;
    this.repaginate();
  }

  /**
   * 追加内容
   */
  appendContent(newContent, isComplete = false) {
    this.content += newContent;
    this.isComplete = isComplete;
    this.repaginate();
  }

  /**
   * 重新分页
   */
  repaginate() {
    this.pages = this.smartPagination(this.content, this.wordsPerPage);
  }

  /**
   * 智能分页算法
   */
  smartPagination(content, wordsPerPage) {
    if (!content) return [];

    const pages = [];
    let currentPos = 0;

    while (currentPos < content.length) {
      // 第一页使用更少的字数，为标题预留空间
      const currentPageWordsPerPage = pages.length === 0 ? this.firstPageWordsPerPage : wordsPerPage;
      let endPos = Math.min(currentPos + currentPageWordsPerPage, content.length);
      
      // 如果不是最后一页，尝试在合适的位置断页
      if (endPos < content.length) {
        // 优先在段落结束处断页
        let paragraphEnd = content.lastIndexOf('\n\n', endPos);
        if (paragraphEnd > currentPos && endPos - paragraphEnd < currentPageWordsPerPage * 0.3) {
          endPos = paragraphEnd + 2;
        } else {
          // 其次在句子结束处断页
          const sentenceEnds = ['。', '！', '？', '…'];
          let sentenceEnd = -1;

          for (let i = endPos - 1; i > currentPos && i > endPos - currentPageWordsPerPage * 0.2; i--) {
            if (sentenceEnds.includes(content[i])) {
              sentenceEnd = i + 1;
              break;
            }
          }
          
          if (sentenceEnd > currentPos) {
            endPos = sentenceEnd;
          }
        }
      }
      
      const pageContent = content.substring(currentPos, endPos).trim();
      if (pageContent) {
        pages.push({
          content: pageContent,
          startPos: currentPos,
          endPos: endPos,
          pageNumber: pages.length + 1
        });
      }
      
      currentPos = endPos;
    }
    
    return pages;
  }

  /**
   * 计算每页字数
   * @param {number} fontSize - 字体大小（px），如果不传则使用默认值32rpx
   */
  calculateWordsPerPage(fontSize = null) {
    try {
      const systemInfo = wx.getSystemInfoSync();
      // 如果传入了fontSize（px），则转换为rpx；否则使用默认的32rpx
      const fontSizeRpx = fontSize ? fontSize * 2 : 32; // px转rpx：px * 2
      const lineHeight = 1.6; // 固定行高
      const padding = 40; // 增加内边距到40rpx，与SCSS中的padding保持一致

      // 转换rpx到px
      const scale = systemInfo.windowWidth / 750;
      const fontSizePx = fontSizeRpx * scale;
      const paddingPx = padding * scale;

      // 计算可用宽度
      const availableWidth = systemInfo.windowWidth - paddingPx * 2;

      // 计算可用高度，考虑更多的安全区域
      let availableHeight = systemInfo.windowHeight;

      // 减去状态栏高度
      if (systemInfo.statusBarHeight) {
        availableHeight -= systemInfo.statusBarHeight;
      }

      // 减去安全区域
      if (systemInfo.safeArea) {
        const safeAreaTop = systemInfo.safeArea.top || 0;
        const safeAreaBottom = systemInfo.windowHeight - (systemInfo.safeArea.bottom || systemInfo.windowHeight);
        availableHeight -= safeAreaBottom;
      }

      // 减去内边距
      availableHeight -= paddingPx * 2;

      // 预留额外的缓冲区域，防止内容溢出
      const bufferHeight = 60; // 预留60px的缓冲区域
      availableHeight -= bufferHeight;

      // 计算每行字符数（考虑中文字符宽度）
      const charsPerLine = Math.floor(availableWidth / fontSizePx);
      const lineHeightPx = fontSizePx * lineHeight;
      const linesPerPage = Math.floor(availableHeight / lineHeightPx);

      // 为了保险起见，减少10%的字数
      const safetyFactor = 0.9;
      this.wordsPerPage = Math.max(Math.floor(charsPerLine * linesPerPage * safetyFactor), 200);

      // 第一页预留标题空间
      const titleHeight = 120; // 增加标题高度预留
      const firstPageAvailableHeight = availableHeight - titleHeight;
      const firstPageLinesPerPage = Math.floor(firstPageAvailableHeight / lineHeightPx);
      this.firstPageWordsPerPage = Math.max(Math.floor(charsPerLine * firstPageLinesPerPage * safetyFactor), 150);

      console.log(`分页计算详情:`, {
        fontSize: `${fontSize}px -> ${fontSizeRpx}rpx`,
        screenSize: `${systemInfo.windowWidth}x${systemInfo.windowHeight}`,
        availableSize: `${Math.round(availableWidth)}x${Math.round(availableHeight)}`,
        charsPerLine,
        linesPerPage,
        wordsPerPage: this.wordsPerPage,
        firstPageWordsPerPage: this.firstPageWordsPerPage
      });
    } catch (error) {
      console.error('计算每页字数失败:', error);
      this.wordsPerPage = 400; // 更保守的默认值
      this.firstPageWordsPerPage = 250; // 更保守的默认值
    }
  }

  /**
   * 获取页面数据
   */
  getPages() {
    return this.pages;
  }

  /**
   * 获取总页数
   */
  getTotalPages() {
    return this.pages.length;
  }

  /**
   * 是否内容完整
   */
  isContentComplete() {
    return this.isComplete;
  }

  /**
   * 设置第一页标题高度，重新计算第一页字数
   * @param {number} titleHeight - 标题占用的高度（px）
   * @param {number} fontSize - 字体大小（px），如果不传则使用当前设置
   */
  setTitleHeight(titleHeight = 120, fontSize = null) {
    try {
      const systemInfo = wx.getSystemInfoSync();
      // 如果传入了fontSize（px），则转换为rpx；否则使用默认的32rpx
      const fontSizeRpx = fontSize ? fontSize * 2 : 32;
      const lineHeight = 1.6;
      const padding = 40; // 与calculateWordsPerPage保持一致

      const scale = systemInfo.windowWidth / 750;
      const fontSizePx = fontSizeRpx * scale;
      const paddingPx = padding * scale;

      // 计算可用宽度
      const availableWidth = systemInfo.windowWidth - paddingPx * 2;

      // 计算可用高度，使用与calculateWordsPerPage相同的逻辑
      let availableHeight = systemInfo.windowHeight;

      // 减去状态栏高度
      if (systemInfo.statusBarHeight) {
        availableHeight -= systemInfo.statusBarHeight;
      }

      // 减去安全区域
      if (systemInfo.safeArea) {
        const safeAreaBottom = systemInfo.windowHeight - (systemInfo.safeArea.bottom || systemInfo.windowHeight);
        availableHeight -= safeAreaBottom;
      }

      // 减去内边距和缓冲区域
      availableHeight -= paddingPx * 2;
      const bufferHeight = 60;
      availableHeight -= bufferHeight;

      const charsPerLine = Math.floor(availableWidth / fontSizePx);
      const lineHeightPx = fontSizePx * lineHeight;

      // 重新计算第一页字数
      const firstPageAvailableHeight = availableHeight - titleHeight;
      const firstPageLinesPerPage = Math.floor(firstPageAvailableHeight / lineHeightPx);
      const safetyFactor = 0.9;
      this.firstPageWordsPerPage = Math.max(Math.floor(charsPerLine * firstPageLinesPerPage * safetyFactor), 150);

      console.log(`标题高度设置: ${titleHeight}px，字体大小: ${fontSize}px，第一页字数: ${this.firstPageWordsPerPage}`);

      // 重新分页
      this.repaginate();
    } catch (error) {
      console.error('设置标题高度失败:', error);
    }
  }
}

/**
 * 流式数据接收器
 */
class StreamReceiver {
  constructor() {
    this.onChunk = null;
    this.onComplete = null;
    this.onError = null;
    this.isReceiving = false;
  }

  /**
   * 开始接收流式数据
   */
  startReceiving(options = {}) {
    const {
      onChunk = null,
      onComplete = null,
      onError = null
    } = options;

    this.onChunk = onChunk;
    this.onComplete = onComplete;
    this.onError = onError;
    this.isReceiving = true;

    // 模拟流式数据接收
    this.simulateStreamData();
  }

  /**
   * 停止接收
   */
  stopReceiving() {
    this.isReceiving = false;
  }

  /**
   * 模拟流式数据接收
   */
  async simulateStreamData() {
    const chunks = this.generateContentChunks();
    let totalContent = '';
    
    for (let i = 0; i < chunks.length && this.isReceiving; i++) {
      try {
        // 模拟网络延迟
        await this.delay(500 + Math.random() * 500);
        
        const chunk = chunks[i];
        totalContent += chunk;
        
        if (this.onChunk) {
          this.onChunk({
            chunk,
            totalContent,
            progress: Math.round((i + 1) / chunks.length * 100),
            isComplete: i === chunks.length - 1
          });
        }
        
        // 模拟可能的错误
        if (Math.random() < 0.03) { // 3% 概率出错
          throw new Error('网络连接异常');
        }
        
      } catch (error) {
        if (this.onError) {
          this.onError(error);
        }
        break;
      }
    }
    
    if (this.isReceiving && this.onComplete) {
      this.onComplete({
        content: totalContent,
        totalChunks: chunks.length
      });
    }
  }

  /**
   * 生成内容片段
   */
  generateContentChunks() {
    const storyParts = [
      '在一个阳光明媚的早晨，小明踏出了家门，开始了他的冒险之旅。',
      '街道上人来人往，每个人都有着自己的故事和目标。',
      '他走过熟悉的街角，心中充满了对未知世界的好奇和期待。',
      '远处的山峦在晨雾中若隐若现，仿佛在召唤着勇敢的探险者。',
      '小明深深地吸了一口新鲜的空气，感受着这个美好世界的魅力。',
      '他知道，这次旅程将会改变他的一生，让他成为一个更好的人。',
      '沿途的风景如画，鸟儿在枝头歌唱，花朵在微风中摇摆。',
      '每一步都充满了意义，每一个瞬间都值得珍惜和回味。',
      '当太阳升到最高点时，小明已经走了很远的路程。',
      '他停下脚步，回望来时的路，心中涌起一阵温暖的感动。',
      '这个世界如此美好，值得用一生去探索和发现。',
      '小明微笑着继续前行，因为他知道最好的还在后面等着他。'
    ];

    const chunks = [];
    for (let i = 0; i < storyParts.length; i++) {
      let chunk = storyParts[i];
      
      // 添加一些随机的扩展内容
      if (Math.random() > 0.5) {
        const extensions = [
          '\n\n微风轻抚过脸颊，带来了远方的花香。',
          '\n\n阳光透过云层洒下，在地面上形成美丽的光影。',
          '\n\n鸟儿的歌声在空中回荡，为这个美好的日子增添了生机。',
          '\n\n路边的野花开得正艳，仿佛在为旅行者送行。'
        ];
        chunk += extensions[Math.floor(Math.random() * extensions.length)];
      }
      
      chunks.push(chunk);
    }

    return chunks;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 阅读进度管理器
 */
class ProgressManager {
  constructor() {
    this.currentPage = 0;
    this.totalPages = 0;
    this.readingProgress = 0;
  }

  /**
   * 更新进度
   */
  updateProgress(currentPage, totalPages) {
    this.currentPage = currentPage;
    this.totalPages = totalPages;
    this.readingProgress = totalPages > 0 ? Math.round((currentPage + 1) / totalPages * 100) : 0;
  }

  /**
   * 获取阅读进度
   */
  getProgress() {
    return {
      currentPage: this.currentPage,
      totalPages: this.totalPages,
      readingProgress: this.readingProgress
    };
  }

  /**
   * 保存进度到本地
   */
  saveProgress() {
    try {
      wx.setStorageSync('readingProgress', {
        currentPage: this.currentPage,
        totalPages: this.totalPages,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('保存进度失败:', error);
    }
  }

  /**
   * 从本地加载进度
   */
  loadProgress() {
    try {
      const progress = wx.getStorageSync('readingProgress');
      if (progress) {
        this.currentPage = progress.currentPage || 0;
        this.totalPages = progress.totalPages || 0;
        this.updateProgress(this.currentPage, this.totalPages);
        return progress;
      }
    } catch (error) {
      console.error('加载进度失败:', error);
    }
    return null;
  }
}

/**
 * 工具函数
 */
const utils = {
  /**
   * 防抖函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * 节流函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  }
};

export {
  ContentPaginator,
  StreamReceiver,
  ProgressManager,
  utils
};

