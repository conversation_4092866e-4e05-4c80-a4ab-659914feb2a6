/**
 * App configuration constants
 */

// 检测当前环境
const env = {
  isDev: function() {
    if (typeof __wxConfig !== 'undefined') {
      return __wxConfig.envVersion === 'develop';
    }
    return false;
  },
  isProd: function() {
    if (typeof __wxConfig !== 'undefined') {
      return __wxConfig.envVersion === 'release' || __wxConfig.envVersion === 'trial';
    }
    return true; // 默认为生产环境
  }
};

const config = {
  // App info
  appName: 'Memoir',
  version: '1.0.0',
  appId: '1002',
  
  // UI settings
  ui: {
    mainContentHeight: '90%',
    controlsHeight: '10%'
  },
  
  // WeChat API settings
  api: {
    // Base URL - 根据环境选择不同的API地址
    baseUrl: env.isDev() 
      ? 'https://dev.api.memoiris.zhijiucity.com:51012' // 开发环境
      : 'https://api.memoiris.zhijiucity.com:51012',     // 生产环境
    
    // Auth endpoints
    loginUrl: '/v1/auth/login',
    wechatLoginUrl: '/v1/auth/wechat-login',
    refreshTokenUrl: '/v1/auth/refresh',
    logoutUrl: '/v1/auth/logout',
    checkStatusUrl: '/v1/auth/verify-token',
    
    // User endpoints
    user: {
      profileUrl: '/v1/data/profile_data',
      updateProfileUrl: '/v1/user/profile',
      avatarUrl: '/v1/users/avatar',
      userInitUrl: '/v1/user/user_init'
    },
    
    data: {
      bookUrl: '/v1/data/get_book',
      sharedBookUrl: '/v1/data/get_shared_book',
      bookStatusUrl: '/v1/data/book_status',
      generateTextByIdUrl: '/v1/book/generate_text_by_id',
      generateChapterUrl: '/v1/book/generate_text_stream',
      generateOutlineUrl: '/v1/book/generate_outline',
      polishTextStreamUrl: '/v1/book/polish_text_stream',
      formatSubchapterUrl: '/v1/format/format_subchapter',
      chatUrl: '/v1/data/get_chat',
      chatOverView:'/v1/data/chat_overview',
      chatOverViewV2:'/v2/data/chat_overview',
      allSessionsUrl: '/v1/data/get_all_sessions',
      latestSessionWithBookUrl: '/v1/data/get_latest_session_with_book',
      resetUrl: '/v1/data/reset',
      resetUserUrl: '/v2/data/reset_user_data',
      generateTimelineUrl: '/v1/book/generate_timeline',
      getTimelineUrl: '/v1/book/get_timeline',
      getPdfUrl: '/v1/book/get_pdf_url',
      progressUrl: '/v1/data/get_progress',
      getChatOverViewV2:'/v2/data/chat_overview',
      purchaseUrl: '/v2/data/purchase_intent',
      updateOutlineUrl: '/v1/book/update_outline',
      updateOutlineByVersion:'/v1/book/update_outline_by_version',
      getBookVersion:'/v1/data/get_book/versions',
      getBookByVersion:'/v1/data/get_book/version',
      bookEdit:'/v2/book/edit',
    },

    chat: {
      reConnectUrl: '/v1/chat/reconnect',
      messageUrl: '/v1/chat/update_message',
      updateMessageMediaUrl: '/v1/chat/update_message_media',
      audioUrl: '/v1/chat/audio',
      replyUrl: '/v1/chat/reply',
      replyUrlV2:'/v2/chat/reply',
      nextChainUrl: '/v1/chat/next_chain',
      askAIUrl: '/v1/chat/ask_ai',
      getQuestionUrl: '/v1/chat/get_question',
      getQuestionUrlV2: '/v2/chat/get_question',
      delImage:'/v2/chat/del-image',
      topicsUrl:'/v2/chat/topics',
      progressUrl:'/v2/chat/progress',
      skipUrl:'/v2/chat/topics/{topic_id}/skip',
    },
    feedback:{
      submitFeedbackUrl: '/v1/feedback/submit',
    }
  },
  
  // Storage keys
  storage: {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
    userInfo: 'userInfo',
    uuid: 'uuid'
  },
  
  // Debug settings
  debug: {
    logRequests: env.isDev(), // 只在开发环境下记录请求
    logResponses: env.isDev() // 只在开发环境下记录响应
  },
  
  // 微信相关配置
  wechat: {
    // 可以放一些微信相关的配置，如appId等
    scope: {
      userInfo: 'scope.userInfo',
      userLocation: 'scope.userLocation'
    }
  },
  
  // 当前环境
  env: {
    isDev: env.isDev(),
    isProd: env.isProd()
  }
}

module.exports = config 