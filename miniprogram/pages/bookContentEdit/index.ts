
import { convertNumberToChineseWithin100 } from '../../utils/util';
import bookApi from '../../api/modules/book';

interface IPageData {
  bookContent: string;
  chapterId: null | string;
  chapterIdWithChinese:string;
  subchapterId: null | string;
  referenceId: string;
  subtitle: string;
  chapterTitle:string;
  isGeneratingOutline: boolean;
  pages: any[];
  currentPageIndex: number;
  totalPages: number;
  isLoading: boolean;
  loadingProgress: number;
  isContentComplete: boolean;
  readingProgress: number;
  showProgress: boolean;
  progressTimer: null | NodeJS.Timeout;
  fontSize: number;
  isUserInteracting: boolean;
  isPolishing?: boolean;
  show?: boolean;
  polishValue?: string;
  versionId?: number;
  selectedText?: string;
}

interface IPageMethods {
  onEditorReady: () => void;
  saveContent: () => void;
}
let editorCtx1: WechatMiniprogram.EditorContext
let editorCtx2: WechatMiniprogram.EditorContext

Page<IPageData, IPageMethods>({
  /**
   * 页面的初始数据
   */
  data: {
    bookContent: '',
    chapterId: null,
    chapterIdWithChinese: '一',
    subchapterId: null,
    referenceId: '',
    subtitle: '',
    chapterTitle:'',
    isGeneratingOutline: true,
     // 内容数据
    pages: [],
    currentPageIndex: 0,
    totalPages: 0,

    // 加载状态
    isLoading: false,
    loadingProgress: 0,
    isContentComplete: false,

    // 阅读进度
    readingProgress: 0,

    // UI状态
    showProgress: false,
    progressTimer: null,

    // 字体大小
    fontSize: 16,

    // 用户交互状态
    isUserInteracting: false,
    isPolishing: false,
    show: false,
    polishValue:'',
    versionId: 1,
    selectedText:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    console.log('页面加载，接收参数:', options);

    // 获取路由传递的参数
    const {text, chapterId, subchapterId, referenceId, subtitle, chapterTitle,versionId} = options;

    // 从 storage 中获取字体大小
    const fontSize = wx.getStorageSync('fontSize') || 16;

    // 保存参数到页面数据中
    this.setData({
      chapterId: chapterId || '',
      chapterIdWithChinese: convertNumberToChineseWithin100(chapterId || '') || '一',
      subchapterId: subchapterId || '',
      referenceId: referenceId || '',
      subtitle: subtitle || '',
      chapterTitle:chapterTitle || '',
      fontSize: fontSize,
      versionId,
      bookContent:text || ''
    });

  },
  onEditorReady() {
    // 正文编辑器的内容
    wx.createSelectorQuery().select('#editorBookContent').context((res) => {
      editorCtx1 = res.context
      // 回显后端数据（这里用 HTML）
      editorCtx1.setContents({
        html: this.data.bookContent
      })
    }).exec()
    // 副标题编辑器的内容
     wx.createSelectorQuery().select('#editorBookSubTitle').context((res) => {
      editorCtx2 = res.context
      // 回显后端数据（这里用 HTML）
      editorCtx2.setContents({
        html: this.data.subtitle
      })
    }).exec()
  },

  saveContent() {
      editorCtx1.getContents({
      success: (res1) => {
        console.log("编辑器1内容:", res1.html)

        // 再取第二个
        editorCtx2.getContents({
          success:async (res2) => {
            console.log("编辑器2内容:", res2.html)
            const subchapter_title = res2.html.replace(/<\/?p>/g, ""); 
            const subchapter_content = res1.html.replace(/<\/?p>/g, "");

            const params = {
              chapter_id: this.data.chapterId,
              chapter_title:this.data.chapterTitle,
              subchapter_content,
              subchapter_id: this.data.subchapterId,
              subchapter_title,
              version_number:Number(this.data.versionId)
            }

            console.log('parasm',params)
            const res = await bookApi.bookEdit(params);
            if(!res) return;
            wx.showToast({
              title:'保存成功'
            })
          
            wx.redirectTo({
              url: '/pages/bookCatalog/index',
              fail: (err) => {
                console.error("跳转失败:", err)
              }
            })
            
          }
        })
      }
    })
    },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '我在「时光笔迹」写回忆录…',
      path: '/pages/interview/index',
      imageUrl: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/share.png' // 可以设置分享图片
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '我在「时光笔迹」写回忆录…',
      query: '/pages/interview/index',
      imageUrl: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/share.png' // 可以设置分享图片
    };
  },








});
