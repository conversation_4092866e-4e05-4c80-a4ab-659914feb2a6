/* pages/reader/reader.wxss */

.reader-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F7F7F7;
  background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/catalog.png');
  background-size: 110% 110%;
  background-position: center;
  position: relative;
}
.reader-header{
  
  padding:40rpx;
  &-title{
    font-size: 24rpx;
    color:#2826243D;
    display: flex;
    flex-direction: column;
  }
  &-text{
    font-size: 48rpx;
    color:#282624;
    margin-top: 40rpx;
    font-weight: 600;;
  }
  &-subtitle{
    font-size: 40rpx;
    color:#E59C2D;
    font-weight: 600;
    display: flex;
    align-items: baseline;
    margin-top: 10rpx;
    .icon{
      
      width: 6rpx;
      height:32rpx;
      background-color: #E59C2D;
      margin-right:10rpx;
    }

  }
}
/* 内容区域 */
.content-swiper {
  flex: 1;
  width: 100%;
  background-color: #F7F7F7;
  background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/catalog.png');
  background-size: 100% 100%;
  background-position: center;
  /* 优化滑动性能 */
  will-change: transform;
  transform: translateZ(0);
  -webkit-overflow-scrolling: touch;
}

.page-item {
  width: 100%;
  height: 100%;
  /* 优化渲染性能 */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.page-content {
  width: 100%;
  height: 100%;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

.text-content {
  width: 100%;
  min-height: 100%;
  // display: flex;
  align-items: flex-start;
  flex-direction: column;
}
.page-turn{
  float:right;
  margin-top: 60rpx;
  color:#2826243D;
  font-size: 24rpx;;
  background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/AR.png');
  background-repeat: no-repeat;
  background-size: 24rpx 24rpx;
  background-position: right center;
  padding-right: 30rpx;
  
}

.content-text {
  /* font-size 通过内联样式动态设置 */
  line-height: 1.6;
  color: #333333;
  letter-spacing: 1rpx;
  word-break: break-all;
  white-space: pre-wrap;
  width: 100%;
  text-indent: 2em;
}

/* 加载页面 */
.loading-page {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
  padding: 60rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

.loading-progress {
  margin-top: 20rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #007aff;
  font-weight: 500;
}

/* 内容结束页面 */
.end-page {
  display: flex;
  align-items: center;
  justify-content: center;
}

.end-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  padding: 60rpx;
  text-align: center;
}

.end-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.end-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.end-subtitle {
  font-size: 26rpx;
  color: #666666;
}

/* 底部加载提示 */
.bottom-loading {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 15rpx;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 30rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  z-index: 200;
  backdrop-filter: blur(10rpx);
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: white;
  animation: dotPulse 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-tip {
  margin-left: 10rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .content-text {
    /* font-size 通过内联样式动态设置 */
    line-height: 1.5;
  }

  .page-content {
    padding: 30rpx 25rpx;
  }
}

/* 动画效果 */
.page-item {
  transition: transform 0.3s ease;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

/* 安全区域适配 */
.reader-container {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 流式内容指示器 */
.content-streaming {
  position: relative;
}

.content-streaming::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 2rpx;
  height: 30rpx;
  background-color: #007aff;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 页面切换动画 */
.swiper-item {
  transition: opacity 0.3s ease;
}

/* 加载状态样式 */
.loading-state .content-text {
  opacity: 0.8;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .reader-container {
  background-color: #F7F7F7;
  }
  
  .content-text {
    color: #333;
  }
  
  .progress-bar {
    background-color: rgba(26, 26, 26, 0.95);
    border-bottom-color: #333333;
  }
  
  .page-info {
    color: #cccccc;
  }
  
  .progress-track {
    background-color: #333333;
  }
  
  .loading-text,
  .end-text {
    color: #e0e0e0;
  }
  
  .end-subtitle {
    color: #cccccc;
  }
}



/* 性能优化样式 */
.content-swiper {
  will-change: transform;
}

.page-item {
  will-change: transform;
  contain: layout style paint;
}

.content-text {
  contain: layout style;
  text-rendering: optimizeSpeed;
}

/* 触摸优化 */
.page-content {
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
}

/* 加载状态优化 */
.loading-content {
  will-change: opacity;
}

.loading-spinner {
  will-change: transform;
}

/* 进度条优化 */
.progress-fill {
  will-change: width;
}

/* 底部加载提示优化 */
.bottom-loading {
  will-change: transform, opacity;
}

/* 减少重绘 */
.reader-container,
.reader-container view,
.reader-container text,
.reader-container image {
  box-sizing: border-box;
}

/* 字体渲染优化 */
.content-text {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-size-adjust: 100%;
}

/* 滚动优化 */
.content-swiper,
.page-content {
  -webkit-overflow-scrolling: touch;
}



/* 动画性能优化 */
@media (prefers-reduced-motion: reduce) {
  view,
  text,
  image,
  .reader-container,
  .page-item,
  .content-text,
  .progress-fill {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .content-text {
    -webkit-font-smoothing: subpixel-antialiased;
  }
}

/* 横屏适配 */
@media (orientation: landscape) {
  .page-content {
    padding: 30rpx 60rpx;
  }

  .content-text {
    /* font-size 通过内联样式动态设置 */
    line-height: 1.5;
  }
}

/* 小屏幕优化 */
@media (max-height: 600px) {
  .page-content {
    padding: 25rpx 20rpx;
  }
  
  .loading-content {
    padding: 40rpx;
    gap: 20rpx;
  }
  
  .end-content {
    padding: 40rpx;
    gap: 15rpx;
  }
}

/* 大屏幕优化 */
@media (min-width: 1024rpx) {
  .page-content {
    max-width: 800rpx;
    margin: 0 auto;
  }

  .content-text {
    /* font-size 通过内联样式动态设置 */
    line-height: 1.7;
  }
}

.bubble-menu {
 position: absolute;
 background: #000;
 color: #fff;
 border-radius: 8rpx;
 padding: 8rpx 12rpx;
 display: flex;
 align-items: center;
 box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.2);
 z-index: 9999;
}
.bubble-item {
 font-size: 24rpx;
 padding: 8rpx 10rpx;
}
.bubble-divider {
 width: 2rpx;
 height: 28rpx;
 background: rgba(255,255,255,.4);
 margin: 0 6rpx;
}
.polish-wrapper{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.block {
  width: 600rpx;
  min-height: 120px;
  border-radius: 20rpx;
  padding: 40rpx;
  background-color: #fff;
  textarea{
    width: 520rpx;
  }

}
.polish-actions{
  display: flex;
  justify-content: space-around;
  .cancel{
    width: 180rpx;
    border: 1px solid #ccc;
    border-radius: 30rpx;
    text-align: center;
    padding: 10rpx 20rpx;
  }
  .confirm{
    width: 180rpx;
    background-color: #E59C2D;
    color: #fff;
    border-radius: 30rpx;
    text-align: center;
    padding: 10rpx 20rpx;
  }
}
.editorBookContent {
  height: 960rpx;
  padding:0 40rpx 40rpx;
  text-indent: 2em;
}
.editorBookSubTitle{
  min-height: 80rpx;
  height: 80rpx;
}
.save-btn{
  border:2rpx solid #E59C2D;
  color: #E59C2D;
  border-radius: 30rpx;
}




