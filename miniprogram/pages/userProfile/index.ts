
// pages/userProfile/index.ts
import { storage, api } from '../../config/index';
Page({
  data: {
    avatarUrl: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/mine.png',
    nickName: '未登录用户',
  },

  onLoad() {
    // 加载已保存的用户信息
    this.loadUserInfo();
  },

  // 选择头像
  onChooseAvatar(e: any) {
    const { avatarUrl } = e.detail;
    console.log('选择的头像:', avatarUrl, e.detail);
    this.setData({
      avatarUrl,
    });
  },

  // 输入昵称
  onNickNameInput(e: any) {
    const nickName = e.detail.value;
    this.setData({
      nickName,
    });
  },

  // 保存用户信息
  async saveUserInfo() {
    const { avatarUrl, nickName } = this.data;

    if (!nickName.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none',
      });
      return;
    }

    const userInfo = {
      avatar: avatarUrl,
      username: nickName,
    };

    try {
      // 保存到本地存储
      wx.setStorageSync('userAvatarAndName', userInfo);

      // 保存到全局数据
      const app = getApp();
      if (app.globalData) {
        app.globalData.userAvatarAndName = userInfo;
      }
      console.log('nickName:', nickName);
       
      await new Promise<void>((resolve, reject) => {
        const token = wx.getStorageSync(storage.accessToken)
        wx.uploadFile({
          url: `${api.baseUrl}${api.user.updateProfileUrl}`,
          filePath: avatarUrl, // 本地图片路径
          name: 'avatar', // 与后端 form-data 字段名对应
          formData: {
            username: nickName,
          },
          header: {
            // 如有鉴权需求
            // Authorization: 'Bearer your_token_here',
            Authorization:token.startsWith('Bearer ') ? token : `Bearer ${token}`
          },
          success(res) {
            if (res.statusCode === 200) {
              resolve();
            } else {
              reject(new Error('上传失败，状态码：' + res.statusCode));
            }
          },
          fail(err) {
            reject(err);
          },
        });
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success',
        success: () => {
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        },
      });
    } catch (error) {
      console.error('保存用户信息失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error',
      });
    }
  },

  // 加载用户信息
  loadUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userAvatarAndName');
      if (userInfo) {
        this.setData({
          avatarUrl: userInfo.avatar || this.data.avatarUrl,
          nickName: userInfo.username || this.data.nickName,
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },
});
