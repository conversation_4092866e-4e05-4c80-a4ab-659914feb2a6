/* pages/userProfile/index.wxss */
.container {
  padding: 40rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.form {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 60rpx;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  width: 120rpx;
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}

.avatar-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
}

.avatar-button::after {
  border: none;
}

.avatar-wrapper {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 2rpx solid #e0e0e0;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  
  background-color: #fff;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat
}
.avatar-text{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  color: #999;
  text-align: center;
  font-size: 24rpx;
}


.nickname-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 32rpx;
  background-color: #fafafa;
}

.nickname-input:focus {
  border-color: #F5A630;
  background-color: #fff;
}

.button-container {
  padding: 0 40rpx;
}

.save-button {
  width: 100%;
  height: 88rpx;
  background-color: #F5A630;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-button:active {
  background-color: #e6951f;
}
