import contentApi from '../../api/modules/data';
Page({
  data: {
    isShow: false,
    activeNames: ['1'],
    storyList: [],
  },
  onLoad() {
    this.getStoryList();
  },
  onShow() {
    // 每次页面显示时强制刷新数据，忽略缓存
    this.getStoryList(true);
  },
  onChange(event: any) {
    this.setData({
      activeNames: event.detail,
    });
  },
  clickShowAn() {
    this.setData({
      isShow: !this.data.isShow,
    });
  },
  backToClick() {
    wx.navigateBack({
      delta: 1,
    });
  },
  previewImage(e: { currentTarget: { dataset: { url: any } } }) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: [url], // 需要预览的图片http链接列表
    });
  },
  gotoQuestionDetail(e: { currentTarget: { dataset: { item: any; idx: any; length: any } } }) {
    const item = e.currentTarget.dataset.item;
    const idx = e.currentTarget.dataset.idx + 1;
    const length = e.currentTarget.dataset.length;
    const chatOverView = wx.getStorageSync('chatOverView');
    const index = chatOverView.titleCodeList.findIndex((title: string) => title === item.topic_id);
    const title = chatOverView.titleList[index];
    console.log(' e.currentTarget.dataset', item, item.title, chatOverView.titleList, index + 1, title);
    wx.navigateTo({
      url: '/pages/questionDetail/index',
      success: (res) => {
        res.eventChannel.emit('acceptDataFromDialog', { question: item, idx, length, titleid: index + 1, title });
      },
    });
  },
  // 切换单个回答的显示状态
  toggleAnswer(e: { currentTarget: { dataset: { storyIndex: any; itemIndex: any } } }) {
    const { storyIndex, itemIndex } = e.currentTarget.dataset;
    const storyList = this.data.storyList;

    // 切换显示状态
    (storyList[storyIndex].content[itemIndex] as any).showAnswer = !(storyList[storyIndex].content[itemIndex] as any)
      .showAnswer;

    this.setData({
      storyList: storyList,
    });
  },
  // 获取用户进度
  async getStoryList(forceRefresh = false) {
    const res = await contentApi.getChatOverViewV2(undefined, forceRefresh);
    if (!res) return;
    const { storyList } = res;
    // 新增：对每个 story 的 content 进行分组和排序
    storyList.forEach((story: { content: any[] }) => {
      // 1. 按 topic_id 分组
      const groupMap: { [topicId: string]: any[] } = {};
      story.content.forEach((item) => {
        if (!groupMap[item.topic_id]) {
          groupMap[item.topic_id] = [];
        }
        groupMap[item.topic_id].push(item);
      });

      // 2. 每组内按 qa_record_id 排序，并处理 title
      const newContent: any[] = [];
      Object.values(groupMap).forEach((group: any[]) => {
        group.sort((a, b) => a.qa_record_id - b.qa_record_id);
        group.forEach((item, idx) => {
          const newItem = { ...item }; // 复制原始对象，避免直接修改
          if (idx !== 0) {
            newItem.title = ''; // 仅修改 title
          }
          newContent.push(newItem); // 保留所有字段
        });
      });
      story.content = newContent;

      // 4. 下面原有的逻辑不变
      let lastAnsweredIndex = -1;
      const seenTitles = new Set<string>();
      story.content.forEach((item, index) => {
        if (seenTitles.has(item.title)) {
          item.title = '';
        } else {
          seenTitles.add(item.title);
        }
        if (item.answer) {
          lastAnsweredIndex = index;
        }
      });

      // 标记下一个需要回答的位置
      story.content.forEach((item, index) => {
        item.isCurrentAnswer = !item.answer && index === lastAnsweredIndex + 1;
        item.showAnswer = true; // 默认展开
      });
    });
    this.setData({
      storyList,
    });
  },
});
