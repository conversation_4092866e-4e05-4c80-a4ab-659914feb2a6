<view class="interview-container">
  <view class="interview-list">
    <van-collapse accordion class="my-collapse" value="{{ activeNames }}" bind:change="onChange" wx:for="{{storyList}}" wx:for-item="story" wx:key="index">
      <van-collapse-item title="{{story.title}}" name="{{story.name}}">
        <view slot="value">{{story.count}}</view>
        <view class="listdashline">
          <view class="content" wx:for="{{story.content}}" wx:for-index="idx">
           <view class="dashed-line">
            <image src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/dot.png" wx:if="{{item.title}}" class="listdot" />
            <view class="content-title">{{item.title}}</view>
          </view>
           
            <view class="content-item">
              <view class="content-item-No">第{{idx + 1}}题</view>
              <view class="content-item-main">{{item.question}}</view>
              <view class="content-item-No" bind:tap="toggleAnswer" data-story-index="{{index}}" data-item-index="{{idx}}">
                {{item.answer || item.image_url ? (item.showAnswer ? '收起回答' : '展开您的回答记录') : '您还暂未回答'}}
              </view>
              
              <view class="content-item-main" bindtap="gotoQuestionDetail" data-item="{{item}}"  data-idx="{{item.qa_record_id}}" data-length="{{story.content.length}}" wx:if="{{item.showAnswer && item.answer}}">
              <van-icon name="edit"  />
              {{item.answer}}
              </view>
              <van-icon name="edit" size="32rpx" color="#282624" wx:if="{{item.showAnswer && item.image_url && !item.answer}}" bindtap="gotoQuestionDetail" data-item="{{item}}"  data-idx="{{item.qa_record_id}}" data-length="{{story.content.length}}"  />
              <image src="{{item.image_url}}" class="image-box" mode="widthFix" bindtap="previewImage" data-url="{{item.image_url}}" wx:if="{{item.showAnswer && item.image_url}}"/>
             
            </view>
            <view class="current-an" wx:if="{{item.isCurrentAnswer}}">
              <image src="/assets/images/awr.png" mode="" class="conent-awr"/>
              当前采访回答到此处
            </view>
          </view>
        </view>
      </van-collapse-item>
    </van-collapse>
  </view>

  <view class="backTo" bind:tap="backToClick">
    返回
  </view>
</view>