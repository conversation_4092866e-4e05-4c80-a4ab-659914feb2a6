// pages/bookContent/index.ts
import bookApi from '../../api/modules/book'
// pages/reader/reader.js
import { ContentPaginator, StreamReceiver, ProgressManager, utils } from '../../utils/simple-utils.js';
import { convertNumberToChineseWithin100 } from '../../utils/util';

interface IPageData {
  bookContent: string;
  chapterId: null | string;
  chapterIdWithChinese:string;
  subchapterId: null | string;
  referenceId: string;
  subtitle: string;
  chapterTitle:string;
  isGeneratingOutline: boolean;
  pages: any[];
  currentPageIndex: number;
  totalPages: number;
  isLoading: boolean;
  loadingProgress: number;
  isContentComplete: boolean;
  readingProgress: number;
  showProgress: boolean;
  progressTimer: null | NodeJS.Timeout;
  fontSize: number;
  isUserInteracting: boolean;
  isPolishing?: boolean;
  show?: boolean;
  polishValue?: string;
  versionId?: number;
  selectedText?: string;
}

interface IPageMethods {
  paginator?: ContentPaginator;
  streamReceiver?: StreamReceiver;
  progressManager?: ProgressManager;
  debouncedSaveProgress?: () => void;
  debouncedUpdatePages?: () => void;
  pendingPagesUpdate?: any;
  lastUpdateTime?: number;
  initManagers: () => void;
  getGenerateTextStream: (chapterId: string | number, subchapterId: string | number, referenceId: string) => Promise<void>;
  registerWebSocketListener: () => void;
  handleWebSocketMessage: (message: any) => void;
  startStreamReceiving: () => void;
  onReceiveChunk: (data: any) => void;
  onReceiveComplete: (data: any) => void;
  onReceiveError: (error: any) => void;
  updateReadingProgress: (currentPageIndex: number, totalPages: number) => void;
  retryReceiving: () => void;
  onPageChange: (e: any) => void;
  onPageTap: () => void;
  onTouchStart: () => void;
  onTouchEnd: () => void;
  showProgressBar: () => void;
  toggleProgressBar: () => void;
  loadProgress: () => void;
  refreshContent: () => void;
  onShow: () => void;
  onHide: () => void;
  onPullDownRefresh: () => void;
  onShareTimeline: () => any;
  adjustTitleHeight: (height: number) => void;
  performPagesUpdate: (updateData: any) => void;
  onEnhanceSelected:(e:any) => void;
  onPolishCancel: () => void;
  onPolishConfirm: () => void;
  onInput: (e: any) => void;
  handleEdit:() => void;
}

Page<IPageData, IPageMethods>({
  /**
   * 页面的初始数据
   */
  data: {
    bookContent: '',
    chapterId: null,
    chapterIdWithChinese: '一',
    subchapterId: null,
    referenceId: '',
    subtitle: '',
    chapterTitle:'',
    isGeneratingOutline: true,
     // 内容数据
    pages: [],
    currentPageIndex: 0,
    totalPages: 0,

    // 加载状态
    isLoading: false,
    loadingProgress: 0,
    isContentComplete: false,

    // 阅读进度
    readingProgress: 0,

    // UI状态
    showProgress: false,
    progressTimer: null,

    // 字体大小
    fontSize: 16,

    // 用户交互状态
    isUserInteracting: false,
    isPolishing: false,
    show: false,
    polishValue:'',
    versionId: 1,
    selectedText:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    console.log('页面加载，接收参数:', options);

    // 获取路由传递的参数
    const {text, chapterId, subchapterId, referenceId, subtitle, chapterTitle,versionId} = options;

    // 从 storage 中获取字体大小
    const fontSize = wx.getStorageSync('fontSize') || 16;

    // 保存参数到页面数据中
    this.setData({
      chapterId: chapterId || '',
      chapterIdWithChinese: convertNumberToChineseWithin100(chapterId || '') || '一',
      subchapterId: subchapterId || '',
      referenceId: referenceId || '',
      subtitle: subtitle || '',
      chapterTitle:chapterTitle || '',
      fontSize: fontSize,
      versionId
    });

    this.registerWebSocketListener();
    // 初始化管理器
    this.initManagers();

    // 检查是否有现有的 text 内容
    if (text && text.trim()) {
      console.log('检测到现有内容，直接使用:', text.length, '字符');
      
      // 直接设置内容，不调用生成方法
      this.setData({
        bookContent: text,
        isContentComplete: true,
        isLoading: false
      });
      
      // 使用分页器处理现有内容
      if (this.paginator) {
        this.paginator.setContent(text, true);
        this.performPagesUpdate({
          pages: this.paginator.getPages(),
          totalPages: this.paginator.getTotalPages(),
          isComplete: true
        });
      }
      
      console.log('现有内容已加载完成');
    } else if (chapterId && subchapterId && referenceId) {
      // 没有现有内容，需要生成
      console.log('没有现有内容，开始生成');
      this.getGenerateTextStream(chapterId, subchapterId, referenceId);
    } else {
      wx.showToast({
        title: '缺少必要参数',
        icon: 'none'
      });
    }
  },

    /**
   * 初始化管理器
   */
  initManagers: function() {
    try {
      // 内容分页管理器
      this.paginator = new ContentPaginator();
      // 使用当前页面的字体大小计算每页字数
      this.paginator.calculateWordsPerPage(this.data.fontSize);

      // 设置第一页标题高度（根据实际标题高度调整）
      this.paginator.setTitleHeight(150, this.data.fontSize); // 增加标题高度预留，确保不会溢出

      console.log('分页器初始化成功, 字体大小:', this.data.fontSize, '每页字数:', this.paginator.wordsPerPage, '第一页字数:', this.paginator.firstPageWordsPerPage);

      // 流式数据接收器
      this.streamReceiver = new StreamReceiver();
      console.log('流式接收器初始化成功');

      // 进度管理器
      this.progressManager = new ProgressManager();
      console.log('进度管理器初始化成功');

      // 防抖函数
      this.debouncedSaveProgress = utils.debounce(() => {
        this.progressManager?.saveProgress();
      }, 1000);

      // 防抖的页面更新函数
      this.debouncedUpdatePages = utils.debounce(() => {
        if (this.pendingPagesUpdate) {
          this.performPagesUpdate(this.pendingPagesUpdate);
          this.pendingPagesUpdate = null;
        }
      }, 300); // 300ms 防抖，在生成过程中减少更新频率

      this.lastUpdateTime = 0;

      console.log('所有管理器初始化完成');
    } catch (error) {
      console.error('管理器初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'none'
      });
    }
  },

    /**
   * 开始接收流式内容
   */
  startStreamReceiving: function() {
    this.setData({
      isLoading: true,
      loadingProgress: 0
    });

    this.streamReceiver?.startReceiving({
      onChunk: (data: any) => {
        this.onReceiveChunk(data);
      },
      onComplete: (data: any) => {
        this.onReceiveComplete(data);
      },
      onError: (error: any) => {
        this.onReceiveError(error);
      }
    });
  },
    /**
   * 接收到内容片段
   */
  onReceiveChunk: function(data) {
    const { chunk, totalContent, progress, isComplete } = data;
    
    // 更新分页器内容
    this.paginator?.setContent(totalContent, isComplete);

    // 获取新的页面数据
    const pages = this.paginator?.getPages() || [];
    const totalPages = this.paginator?.getTotalPages() || 0;
    
    // 保持当前阅读位置
    const currentPageIndex = Math.min(this.data.currentPageIndex, totalPages - 1);
    
    // 更新页面数据
    this.setData({
      pages: pages,
      totalPages: totalPages,
      currentPageIndex: currentPageIndex,
      loadingProgress: progress,
      isContentComplete: isComplete
    });
    
    // 更新阅读进度
    this.updateReadingProgress(currentPageIndex, totalPages);
    
    console.log(`接收内容片段: ${chunk.length}字, 总进度: ${progress}%`);
  },


  /**
   * 内容接收完成
   */
  onReceiveComplete: function(data) {
    console.log('内容接收完成:', data);
    
    this.setData({
      isLoading: false,
      loadingProgress: 100,
      isContentComplete: true
    });
    
    // 显示完成提示
    wx.showToast({
      title: '内容加载完成',
      icon: 'success',
      duration: 2000
    });
  },

  /**
   * 接收错误处理
   */
  onReceiveError: function(error) {
    console.error('接收内容错误:', error);
    
    this.setData({
      isLoading: false
    });
    
    // 显示错误提示
    wx.showModal({
      title: '加载失败',
      content: '内容加载失败，是否重试？',
      success: (res) => {
        if (res.confirm) {
          this.retryReceiving();
        }
      }
    });
  },
  
   /**
   * 重试接收内容
   */
  retryReceiving: function() {
    // 重置状态
    this.paginator?.setContent('', false);
    this.setData({
      pages: [],
      totalPages: 0,
      currentPageIndex: 0,
      loadingProgress: 0,
      isContentComplete: false
    });

    // 重新开始接收
    this.startStreamReceiving();
  },

  /**
   * 页面切换事件
   */
  onPageChange: function(e) {
    const currentPageIndex = e.detail.current;
    const totalPages = this.data.totalPages;

    // 标记用户正在交互
    this.setData({
      currentPageIndex: currentPageIndex,
      isUserInteracting: true
    });

    // 更新阅读进度
    this.updateReadingProgress(currentPageIndex, totalPages);

    // 保存进度（防抖）
    this.debouncedSaveProgress?.();

    // 显示进度条
    this.showProgressBar();

    // 1秒后重置交互状态
    setTimeout(() => {
      this.setData({
        isUserInteracting: false
      });
    }, 1000);

    console.log(`切换到第 ${currentPageIndex + 1} 页`);
  },

  /**
   * 更新阅读进度
   */
  updateReadingProgress: function(currentPage: number, totalPages: number) {
    this.progressManager?.updateProgress(currentPage, totalPages);
    const progress = this.progressManager?.getProgress();

    if (progress) {
      this.setData({
        readingProgress: progress.readingProgress
      });
    }
  },

  /**
   * 页面点击事件
   */
  onPageTap: function() {
    // 点击页面显示/隐藏进度条
    this.toggleProgressBar();
  },


  /**
   * 触摸开始事件
   */
  onTouchStart: function() {
    this.setData({
      isUserInteracting: true
    });
  },

  /**
   * 触摸结束事件
   */
  onTouchEnd: function() {
    // 延迟重置交互状态，给滑动动画时间
    setTimeout(() => {
      this.setData({
        isUserInteracting: false
      });
    }, 500);
  },

  /**
   * 显示进度条
   */
  showProgressBar: function() {
    this.setData({
      showProgress: true
    });
    
    // 清除之前的定时器
    if (this.data.progressTimer) {
      clearTimeout(this.data.progressTimer);
    }
    
    // 3秒后自动隐藏
    const timer = setTimeout(() => {
      this.setData({
        showProgress: false,
        progressTimer: null
      });
    }, 3000);
    
    this.setData({
      progressTimer: timer
    });
  },

  /**
   * 切换进度条显示状态
   */
  toggleProgressBar: function() {
    const showProgress = !this.data.showProgress;
    
    this.setData({
      showProgress: showProgress
    });
    
    if (showProgress) {
      // 如果显示，3秒后自动隐藏
      if (this.data.progressTimer) {
        clearTimeout(this.data.progressTimer);
      }
      
      const timer = setTimeout(() => {
        this.setData({
          showProgress: false,
          progressTimer: null
        });
      }, 3000);
      
      this.setData({
        progressTimer: timer
      });
    } else {
      // 如果隐藏，清除定时器
      if (this.data.progressTimer) {
        clearTimeout(this.data.progressTimer);
        this.setData({
          progressTimer: null
        });
      }
    }
  },

  /**
   * 加载阅读进度
   */
  loadProgress: function() {
    const progress = this.progressManager?.loadProgress();
    if (progress && progress.currentPage >= 0) {
      this.setData({
        currentPageIndex: progress.currentPage
      });
      console.log('加载阅读进度:', progress);
    }
  },

  /**
   * 手动刷新内容
   */
  refreshContent: function() {
    // 停止当前接收
    this.streamReceiver?.stopReceiving();

    // 重新开始
    setTimeout(() => {
      this.retryReceiving();
    }, 500);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时恢复接收（如果需要）
    console.log('页面显示');

    // 检查字体大小是否有变化
    const currentFontSize = wx.getStorageSync('fontSize') || 16;
    if (currentFontSize !== this.data.fontSize) {
      console.log(`字体大小变化: ${this.data.fontSize}px -> ${currentFontSize}px`);

      // 更新字体大小
      this.setData({
        fontSize: currentFontSize
      });

      // 重新计算分页
      if (this.paginator) {
        this.paginator.calculateWordsPerPage(currentFontSize);
        this.paginator.setTitleHeight(150, currentFontSize);

        // 如果有内容，重新分页
        if (this.data.bookContent) {
          this.paginator.setContent(this.data.bookContent, this.data.isContentComplete);
          const pages = this.paginator.getPages();
          const totalPages = this.paginator.getTotalPages();

          // 保持当前阅读位置，但不超过总页数
          const currentPageIndex = Math.min(this.data.currentPageIndex, Math.max(0, totalPages - 1));

          this.setData({
            pages: pages,
            totalPages: totalPages,
            currentPageIndex: currentPageIndex
          });

          // 更新阅读进度
          this.updateReadingProgress(currentPageIndex, totalPages);

          console.log(`重新分页完成: 总页数=${totalPages}, 当前页=${currentPageIndex + 1}`);
        }
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    // 保存当前进度
    this.progressManager?.saveProgress();

    // 清除进度条定时器
    if (this.data.progressTimer) {
      clearTimeout(this.data.progressTimer);
    }

    console.log('页面隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 停止流式接收
    this.streamReceiver?.stopReceiving();

    // 保存进度
    this.progressManager?.saveProgress();

    // 清除定时器
    if (this.data.progressTimer) {
      clearTimeout(this.data.progressTimer);
    }

    console.log('页面卸载');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 下拉刷新重新加载内容
    this.refreshContent();
    
    // 停止下拉刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '我在「时光笔迹」写回忆录…',
      path: '/pages/interview/index',
      imageUrl: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/share.png' // 可以设置分享图片
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '我在「时光笔迹」写回忆录…',
      query: '/pages/interview/index',
      imageUrl: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/share.png' // 可以设置分享图片
    };
  },


  async getGenerateTextStream(chapterId: string | number, subchapterId: string | number,) {
    // 设置加载状态
    this.setData({
      isLoading: true,
      loadingProgress: 0,
      isContentComplete: false,
      pages: [],
      totalPages: 0
    });
    const style = wx.getStorageSync('styleSetting') || '';

    const params = {
      chapter_id: typeof chapterId === 'string' ? parseInt(chapterId) : chapterId,
      subchapter_id: typeof subchapterId === 'string' ? parseInt(subchapterId) : subchapterId,
      version_number:Number(this.data.versionId),
      style
    };
    try {
      const res: any = await bookApi.generateChapter(params);
      if (res && res.code === 2000) {
        wx.showToast({
          title: "请稍等，内容生成中...",
          icon: 'none'
        });
      } else {
        // 如果请求失败，重置加载状态
        this.setData({
          isLoading: false
        });
        wx.showToast({
          title: "内容生成失败，请重试",
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('生成内容失败:', error);
      this.setData({
        isLoading: false
      });
      wx.showToast({
        title: "网络错误，请重试",
        icon: 'none'
      });
    }
  },

  registerWebSocketListener() {
    try {
      const app = getApp();
      if (!app || !app.API || !app.API.websocket) {
        console.error('WebSocket API未初始化');
        wx.showToast({
          title: 'WebSocket未初始化',
          icon: 'none'
        });
        return;
      }

      // 注册消息监听器
      app.API.websocket.registerMessageListener('book-content-listener', (message: any) => {
        this.handleWebSocketMessage(message);
      });

      // console.log('WebSocket监听器注册成功');
    } catch (error) {
      console.error('注册WebSocket监听器失败:', error);
      wx.showToast({
        title: 'WebSocket注册失败',
        icon: 'none'
      });
    }
  },
  /**
   * 处理WebSocket消息
   * @param {Object} message - 收到的消息对象
   */
  handleWebSocketMessage(message: any) {
    try {
      // console.log('收到WebSocket消息:', message);
      const { event, full_text, progress, is_complete } = message;

      if (event === 'generate_text_stream') {
        console.log(`处理流式内容: 长度=${full_text?.length || 0}, 进度=${progress || 0}%, 完成=${is_complete || false}`);

        // 更新原始内容
        this.setData({
          bookContent: full_text || '',
        });

        // 使用分页器处理内容
        if (this.paginator && full_text) {
          // 设置内容到分页器
          this.paginator.setContent(full_text, is_complete || false);

          // 获取分页数据
          const pages = this.paginator.getPages();
          const totalPages = this.paginator.getTotalPages();

          console.log(`分页结果: 总页数=${totalPages}, 页面数据长度=${pages.length}`);

          // 保持当前阅读位置，但不超过总页数
          const currentPageIndex = Math.min(this.data.currentPageIndex, Math.max(0, totalPages - 1));

          // 准备更新数据
          const updateData = {
            pages: pages,
            totalPages: totalPages,
            currentPageIndex: currentPageIndex,
            loadingProgress: progress || 0,
            isContentComplete: is_complete || false
          };

          // 如果内容生成完成，立即更新；否则使用防抖更新
          if (is_complete) {
            // 内容完成时立即更新
            this.setData({
              ...updateData,
              isLoading: false
            });
            this.updateReadingProgress(currentPageIndex, totalPages);
            console.log(`内容生成完成，立即更新: 当前页=${currentPageIndex + 1}/${totalPages}`);
          } else {
            // 生成过程中使用防抖更新，减少卡顿
            const currentTime = Date.now();
            if (currentTime - (this.lastUpdateTime || 0) > 500) {
              // 如果距离上次更新超过500ms，立即更新一次
              this.setData({
                ...updateData,
                isLoading: true
              });
              this.updateReadingProgress(currentPageIndex, totalPages);
              this.lastUpdateTime = currentTime;
              console.log(`立即更新: 当前页=${currentPageIndex + 1}/${totalPages}`);
            } else {
              // 否则使用防抖更新
              this.pendingPagesUpdate = {
                ...updateData,
                isLoading: true
              };
              this.debouncedUpdatePages?.();
            }
          }
        } else {
          console.warn('分页器未初始化或内容为空', {
            paginatorExists: !!this.paginator,
            contentLength: full_text?.length || 0
          });

          // 即使没有分页器，也要更新基本状态
          this.setData({
            loadingProgress: progress || 0,
            isContentComplete: is_complete || false,
            isLoading: !(is_complete || false)
          });
        }
      } 
    } catch (error) {
      console.error('处理WebSocket消息失败:', error);
      wx.showToast({
        title: '消息处理失败',
        icon: 'none'
      });
    }
  },

  /**
   * 动态调整标题高度
   * @param height 标题高度（px）
   */
  adjustTitleHeight: function(height: number) {
    if (this.paginator) {
      this.paginator.setTitleHeight(height, this.data.fontSize);
      console.log(`标题高度已调整为 ${height}px，字体大小 ${this.data.fontSize}px`);
    }
  },

  /**
   * 调试方法：显示分页信息
   */
  /**
   * 执行页面更新
   */
  performPagesUpdate: function(updateData: any) {
    const { pages, totalPages, currentPageIndex, loadingProgress, isContentComplete } = updateData;

    // 只有在用户没有交互时才更新页面
    if (!this.data.isUserInteracting) {
      this.setData({
        pages: pages,
        totalPages: totalPages,
        currentPageIndex: currentPageIndex,
        loadingProgress: loadingProgress,
        isContentComplete: isContentComplete
      });

      // 更新阅读进度
      this.updateReadingProgress(currentPageIndex, totalPages);
    }
  },

  onPolishCancel() {
    this.setData({ show: false });
  },
  onEnhanceSelected: async function(e: any) {
    const selectedText = e?.detail?.text || '';
    this.setData({
      selectedText
    })
    if (!selectedText) { wx.showToast({ title: '未选中文本', icon: 'none' }); return; }
    const subchapterId = this.data.subchapterId;
    if (!subchapterId) { wx.showToast({ title: '缺少小节ID', icon: 'none' }); return; }
    if (this.data.isPolishing) return;
    try {
      this.setData({ isPolishing: true });
      // 显示错误提示
      this.setData({ show: true });
      // await bookApi.polishTextStream({ subchapter_id: String(subchapterId), hint_text: selectedText });
      
    } catch (err) {
      wx.hideLoading();
      wx.showToast({ title: '润色失败', icon: 'none' });
    } finally {
      this.setData({ isPolishing: false });
    }
  },
  onInput(e){
    console.log(e.detail.value)
    this.setData({
      polishValue:e.detail.value,
    })
  },
  async onPolishConfirm(){
    const style = wx.getStorageSync('style')
    const content =  `${this.data.selectedText}---按照：${this.data.polishValue}意思修改`
    const params = {
      hint_text:content,
      style,
      subchapter_id: Number(this.data.subchapterId),
      version_number:Number(this.data.versionId)
    }
    console.log('params',params)
    const res = await bookApi.polishTextStream(params);
    if(!res) return;
    wx.showToast({
      title:'提交成功'
    })
    this.setData({ show:false, polishValue:''})
  },
  handleEdit(){
    wx.navigateTo({
      url: `/pages/bookContentEdit/index?text=${this.data.bookContent}&chapterId=${this.data.chapterId}&subchapterId=${this.data.subchapterId}&referenceId=${this.data.referenceId}&subtitle=${this.data.subtitle}&chapterTitle=${this.data.chapterTitle}&versionId=${this.data.versionId}`,
    });
  }
});
