<!--pages/reader/reader.wxml-->
<view class="reader-container" data-show-progress="{{showProgress}}">
  <view class="reader-header" wx:if="{{currentPageIndex === 0}}">
    <view class="reader-header-title">
        <text>第{{chapterIdWithChinese}}章</text>
        <text>Chapter {{chapterId}}</text>
    </view>
    <view class="reader-header-text">{{chapterTitle}}</view>

    <view class="reader-header-subtitle">
      <text class="icon"></text>
      <text>{{subtitle}}</text>
    </view>
  </view>

  <!-- 阅读内容区域 -->
  <swiper class="content-swiper" current="{{currentPageIndex}}" bindchange="onPageChange" circular="{{false}}" duration="300" easing-function="easeInOutCubic" bindtouchstart="onTouchStart" bindtouchend="onTouchEnd">

    <!-- 内容页面 -->
    <swiper-item wx:for="{{pages}}" wx:key="index" class="page-item">
      <scroll-view class="page-content" scroll-y="{{false}}" bindtap="onPageTap">
        <view class="text-content">
          <SelectTextPlus class="content-text" text-style="font-size: {{fontSize}}px;display:block;" value="{{item.content}}" bind:enhance="onEnhanceSelected" bind:edit="handleEdit"/>
          <view class="page-turn" wx:if="{{index !== pages.length - 1}}">左滑下一页</view>
        </view>
      </scroll-view>
    </swiper-item>

    <!-- 加载页面 -->
    <swiper-item wx:if="{{isLoading && pages.length === 0}}" class="page-item loading-page">
      <view class="loading-content">
        <van-loading type="spinner" size="24px" vertical color="#F5A631">正在加载内容...</van-loading>
        <view class="loading-progress" wx:if="{{loadingProgress > 0}}">
          <text class="progress-text">{{loadingProgress}}%</text>
        </view>
      </view>
    </swiper-item>
  </swiper>
  <van-overlay show="{{ show }}">
    <view class="polish-wrapper">
      <view class="block">
        <textarea placeholder="请输入你需要的润色的内容，例如：“将XXXX中的内容修改为XXXX”" placeholder-style="color:#28262466" value="{{polishValue}}" bindinput="onInput" />
        <view class="polish-actions">
          <view class="cancel" bindtap="onPolishCancel">取消</view>
          <view class="confirm" bindtap="onPolishConfirm">确定</view>
        </view>
      </view>
    </view>
  </van-overlay>
</view>