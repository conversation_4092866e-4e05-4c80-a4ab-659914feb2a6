import contentApi from '../../api/modules/data';
Page({
  data:{
    contactValue:''
  },
  onContactInput(e: { detail: { value: any } }) {
    this.setData({ contactValue: e.detail.value });
  },
  async submitFeedback(){
    const regx= /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
    const bool=regx.test(this.data.contactValue);
    if(!bool){
      wx.showToast({
        title: '请输入正确手机号',
        icon:'none'
      })
      return
    }
    const params = {
      phone_number:this.data.contactValue,

    }
    const res = await contentApi.submitPurchase(params);
    if(res.success){
      wx.showToast({
        title: '提交成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 清空表单
          this.setData({
            contactValue: '',
          });
  
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
            }, 1500);
        },
      });
    }
  }
})