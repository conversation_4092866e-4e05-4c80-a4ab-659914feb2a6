import contentApi from '../../api/modules/data';
Page({
  data: {
    styleValue: '',
  },
  onLoad(){
    const styleSetting = wx.getStorageSync('styleSetting') || '';
    this.setData({ styleValue: styleSetting });
  },
  onHide() {
    //页面隐藏，重置设置
    this.setData({ styleValue: '' });

  },
  onInput(e: { detail: { value: any } }) {
    this.setData({ styleValue: e.detail.value });
  },
  async resetSystem() {
    wx.showModal({
      title: '您确定要重置吗？',
      content: '重置后，您将无法恢复数据，是否继续？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const params = {
              confirm: true,
              confirmation_text: '确认',
            };
            const res = await contentApi.resetUserData(params);
            if(!res.success) return;
            wx.setStorageSync('fontSize', 16);
            wx.showToast({
              title: '重置成功',
              icon: 'success',
              duration: 1500,
              success: () => {
                // 延迟重启小程序到首页
                setTimeout(() => {
                  wx.reLaunch({
                    url: '/pages/interview/index' // 重新启动到首页
                  });
                }, 1500);
              }
            });
          } catch (error) {
            wx.showToast({
              title: '重置失败',
              icon: 'error',
            });
          }
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      },
    });
  },
  submitStyleSetting() {
    wx.setStorageSync('styleSetting', this.data.styleValue);
    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      },
    });
  },
});
