.book-preview-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f6f6f6;
}

/* 内容区域 */
.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 书本内容容器 */
.book-content-container {
  flex: 1;
  position: relative;
  height: 100%;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  height: 80rpx;
  position: relative;
}

.back-button {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  z-index: 1;
}

.back-icon {
  font-size: 40rpx;
  margin-right: 8rpx;
}

.title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 提示条 */
.tip-container {
  position: fixed;
  top: 120rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 30rpx;
  padding: 15rpx 25rpx;
  z-index: 100;
  animation: fadeInOut 3s;
}

.tip-text {
  color: #fff;
  font-size: 26rpx;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(74, 107, 255, 0.2);
  border-top: 6rpx solid #4a6bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 30rpx;
  color: #333;
}

/* 错误提示 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40rpx;
  text-align: center;
}

.error-message {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.error-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;
  max-width: 400rpx;
}

.error-buttons button {
  margin: 0;
  min-width: 200rpx;
  font-size: 28rpx;
}

/* Android预览信息 */
.android-preview-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40rpx;
  text-align: center;
}

.preview-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.preview-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.preview-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.preview-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;
  max-width: 400rpx;
}

.preview-actions button {
  margin: 0;
  font-size: 28rpx;
}

.debug-info {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;

  text {
    display: block;
    margin-bottom: 8rpx;
  }
}

/* Web-view 容器 */
web-view {
  width: 100%;
  height: 100%;
  flex: 1;
}

/* 无内容提示 */
.no-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40rpx;
}

.no-content-text {
  font-size: 32rpx;
  color: #999;
}

/* PDF 未生成提示 */
.pdf-not-generated-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40rpx;
}

.pdf-notice {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}
