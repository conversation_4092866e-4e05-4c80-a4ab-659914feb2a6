
import DeviceHelper from '../../utils/device-helper';

Page({
  data: {
    // PDF数据
    bookData: {},
    // 是否正在加载
    isLoading: true,
    sessionId: null, // 用于存储从分享参数中获取的 session_id
    pdfUrl: '', // 存储PDF文件URL
    pdfNotGenerated: false, // 新增，PDF未生成时显示按钮
    isGeneratingPdf: false, // 新增，生成PDF时按钮loading
    isIOS: false, // 是否为iOS设备
    isAndroid: false, // 是否为Android设备
    isHuawei: false, // 是否为华为设备
    showError: false, // 是否显示错误
    errorMessage: '', // 错误信息
    deviceHelper: null as any // 设备助手实例
  },

  onLoad(options) {
    console.log('分享页面 onLoad, options:', options);

    // 初始化设备助手
    const deviceHelper = DeviceHelper.getInstance();
    const deviceInfo = deviceHelper.getDeviceInfo();

    this.setData({
      deviceHelper,
      isIOS: deviceInfo.isIOS,
      isAndroid: deviceInfo.isAndroid && !deviceInfo.isHuawei, // 华为设备单独处理
      isHuawei: deviceInfo.isHuawei
    });

    console.log('设备信息:', deviceHelper.getDeviceSummary());
    console.log('详细设备信息:', {
      isIOS: deviceInfo.isIOS,
      isAndroid: deviceInfo.isAndroid,
      isHuawei: deviceInfo.isHuawei,
      platform: deviceInfo.platform,
      recommendedMethod: deviceInfo.recommendedPreviewMethod
    });

    const pdfUrl = options.pdfUrl ? decodeURIComponent(options.pdfUrl) : '';
    console.log('原始 pdfUrl:', pdfUrl);

    if (pdfUrl) {
      // 处理PDF URL
      const processedUrl = deviceHelper.processPDFUrl(pdfUrl);
      this.setData({ pdfUrl: processedUrl });
      this.handlePdfPreview(processedUrl);
    } else {
      this.setData({
        isLoading: false,
        pdfNotGenerated: true
      });
    }
  },

  // 处理PDF预览
  handlePdfPreview(pdfUrl: string) {
    const deviceHelper = this.data.deviceHelper;
    const recommendedMethod = deviceHelper.getRecommendedPreviewMethod();

    console.log('推荐预览方式:', recommendedMethod);
    console.log('设备类型:', {
      isIOS: this.data.isIOS,
      isAndroid: this.data.isAndroid,
      isHuawei: this.data.isHuawei
    });

    if (this.data.isIOS) {
      // iOS设备使用web-view
      this.setData({ isLoading: false });
    } else if (this.data.isAndroid || this.data.isHuawei) {
      // Android设备或华为设备使用下载预览
      console.log('Android/华为设备，使用下载预览');
      this.downloadAndPreview(pdfUrl);
    } else {
      // 其他情况，默认尝试下载预览
      console.log('未知设备，默认使用下载预览');
      this.downloadAndPreview(pdfUrl);
    }
  },

  // 下载并预览文档（Android/华为设备使用）
  downloadAndPreview(pdfUrl: string) {
    console.log('开始下载PDF文件:', pdfUrl);
    console.log('当前设备类型:', {
      isIOS: this.data.isIOS,
      isAndroid: this.data.isAndroid,
      isHuawei: this.data.isHuawei
    });

    wx.showLoading({
      title: '下载中...'
    });

    wx.downloadFile({
      url: pdfUrl,
      success: (res) => {
        wx.hideLoading();

        if (res.statusCode === 200) {
          console.log('文件下载成功:', res.tempFilePath);

          // 使用系统预览
          wx.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: () => {
              console.log('文档预览成功');
              this.setData({ isLoading: false });
            },
            fail: (err) => {
              console.error('文档预览失败:', err);
              this.handleError('无法预览文档，请检查文件格式', err);
            }
          });
        } else {
          console.error('文件下载失败，状态码:', res.statusCode);
          this.handleError('文件下载失败');
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('文件下载失败:', err);
        this.handleError('网络异常，下载失败');
      }
    });
  },

  // 错误处理
  handleError(message: string, error?: any) {
    console.error('预览错误:', message, error);

    // 获取设备特定的错误信息
    const deviceHelper = this.data.deviceHelper;
    const deviceSpecificMessage = deviceHelper ?
      deviceHelper.getDeviceSpecificErrorMessage(error) : message;

    this.setData({
      isLoading: false,
      showError: true,
      errorMessage: deviceSpecificMessage
    });

    wx.showToast({
      title: deviceSpecificMessage,
      icon: 'none',
      duration: 2000
    });
  },

  // 重试加载
  retryLoad() {
    if (this.data.pdfUrl) {
      this.setData({
        isLoading: true,
        showError: false,
        errorMessage: ''
      });
      this.handlePdfPreview(this.data.pdfUrl);
    }
  },

  // Web-view加载成功（iOS设备）
  onWebViewLoad(e: any) {
    console.log('Web-view加载成功:', e);
    this.setData({
      isLoading: false,
      showError: false
    });
  },

  // Web-view加载失败（iOS设备）
  onWebViewError(e: any) {
    console.error('Web-view加载失败:', e);
    this.handleError('页面加载失败，请检查网络连接', e);
  },

  // 复制链接
  copyLink() {
    if (this.data.pdfUrl) {
      wx.setClipboardData({
        data: this.data.pdfUrl,
        success: () => {
          wx.showToast({
            title: '链接已复制',
            icon: 'success'
          });
        },
        fail: () => {
          wx.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 手动打开文件预览（备用方案）
  openFilePreview() {
    console.log('手动触发预览，PDF URL:', this.data.pdfUrl);

    if (this.data.pdfUrl) {
      // 重置错误状态
      this.setData({
        showError: false,
        errorMessage: '',
        isLoading: true
      });

      // 重新尝试预览
      this.downloadAndPreview(this.data.pdfUrl);
    } else {
      wx.showToast({
        title: '无可预览的文件',
        icon: 'none'
      });
    }
  }
  
});
