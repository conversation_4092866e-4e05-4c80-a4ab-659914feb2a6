<view class="book-preview-container">
  <!-- 内容展示区域 -->
  <view class="content-wrapper">
    <!-- 加载中 -->
    <block wx:if="{{isLoading}}">
      <view class="loading-container">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>
    </block>
    
    <!-- PDF未生成提示 -->
    <block wx:elif="{{!isLoading && pdfNotGenerated}}">
      <view class="book-content-container">
        <view class="pdf-not-generated-info">
          <view class="pdf-notice">该书籍的PDF尚未生成</view>
          <button type="primary" loading="{{isGeneratingPdf}}" disabled="{{isGeneratingPdf}}" bindtap="onGeneratePdf">{{isGeneratingPdf ? '正在生成...' : '生成PDF'}}</button>
        </view>
      </view>
    </block>

    <!-- 错误提示 -->
    <block wx:elif="{{showError}}">
      <view class="error-container">
        <view class="error-message">{{errorMessage}}</view>
        <view class="error-buttons">
          <button type="default" bindtap="retryLoad">重新加载</button>
          <button type="primary" bindtap="openFilePreview">系统预览</button>
          <button type="warn" bindtap="copyLink">复制链接</button>
        </view>
      </view>
    </block>

    <!-- iOS设备使用web-view -->
    <block wx:elif="{{!isLoading && isIOS && pdfUrl}}">
      <web-view
        src="{{pdfUrl}}"
        bindload="onWebViewLoad"
        binderror="onWebViewError"
        bindmessage="">
      </web-view>
    </block>

    <!-- 华为设备提示（已自动下载预览） -->
    <block wx:elif="{{!isLoading && isHuawei && pdfUrl}}">
      <view class="android-preview-info">
        <view class="preview-icon">📱</view>
        <view class="preview-title">华为设备预览</view>
        <view class="preview-desc">文档已自动下载并使用系统预览打开</view>
        <view class="debug-info">
          <text>设备类型: 华为设备</text>
          <text>预览方式: 系统下载预览</text>
        </view>
        <view class="preview-actions">
          <button type="primary" bindtap="openFilePreview">重新预览</button>
          <button type="default" bindtap="copyLink">复制链接</button>
        </view>
      </view>
    </block>

    <!-- Android设备提示（已自动下载预览） -->
    <block wx:elif="{{!isLoading && isAndroid && pdfUrl}}">
      <view class="android-preview-info">
        <view class="preview-icon">📱</view>
        <view class="preview-title">Android设备预览</view>
        <view class="preview-desc">文档已自动下载并使用系统预览打开</view>
        <view class="preview-actions">
          <button type="primary" bindtap="openFilePreview">重新预览</button>
          <button type="default" bindtap="copyLink">复制链接</button>
        </view>
      </view>
    </block>

    <!-- 无内容提示 -->
    <block wx:else>
      <view class="no-content">
        <view class="no-content-text">暂无内容</view>
      </view>
    </block>
      
   
  </view>
</view>
