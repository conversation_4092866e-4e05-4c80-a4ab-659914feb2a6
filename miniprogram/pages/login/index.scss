/* login.wxss */
page {
  height: 100%;
  background-color: #fff;
}

.container {
  height: calc(100vh - 60rpx);
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: center;
  padding: 30rpx;
  position: relative;
  overflow: hidden;
  opacity: 0;
  // transform: translateY(20rpx);
  transition: opacity 0.6s ease, transform 0.6s ease;
  background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/loginBG.png');
  background-size:100% 100%;
}

.container.active {
  opacity: 1;
  transform: translateY(0);
}

.login-card {
  width: 100%;
  height:100%;
  max-width: 650rpx;
  border-radius: 24rpx;
  // padding: 60rpx 40rpx;
  z-index: 10;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Logo和标题样式 */
.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
  position: absolute;
  top:300rpx;
  left:20rpx;
}

.text-logo-wrapper {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  background: linear-gradient(135deg, #0288d1, #007aff);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 122, 255, 0.2);
}

.text-logo {
  font-size: 80rpx;
  line-height: 1;
  color: white;
}

.logo-image {
  height:152rpx;
  width: 432rpx;
  margin-bottom: 24rpx;
}

.app-title {
  font-size: 48rpx;
  font-weight: bold;
  background: linear-gradient(90deg, #0288d1, #007aff);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 16rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

/* 按钮区域样式 */
.buttons-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
  position: absolute;
  bottom:  180rpx;
}

.login-btn {
  height: 90rpx;
  width: 614rpx !important;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.05);
  border: none;
  margin: 0;
  transition: all 0.3s ease;
  overflow: hidden;
  
}

.login-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.login-btn:active::after {
  transform: translateX(0);
}

.primary-btn {
  background:#F5E2C4;
  color: #E59C2D;
  position: relative;
  font-size: 32rpx;
  width: 614rpx;
}

.primary-btn[disabled] {
  background: linear-gradient(135deg, #ccc, #999);
  color: rgba(255, 255, 255, 0.8);
  box-shadow: none;
}

.primary-btn::after {
  border: none;
}

.auth-btn {
  background-color: #07c160;
  color: white;
}

.auth-btn::after {
  border: none;
}

.avatar-btn {
  background-color: #5B8FF9;
  color: white;
}

.avatar-btn::after {
  border: none;
}

.secondary-btn {
  background-color: #f0f0f0;
  color: #333;
}

.secondary-btn::after {
  border: none;
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.btn-icon-right {
  margin-left: 10rpx;
  font-size: 32rpx;
  transition: transform 0.3s ease;
}

.primary-btn:hover .btn-icon-right {
  transform: translateX(5rpx);
}

.btn-text {
  font-weight: 500;
}

/* 底部提示文字 */
.login-tips {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.link {
  color: var(--primary-color, #F5A630);
  display: inline;
}



@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

/* 加载动画 */
.loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loader {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid var(--primary-color, #007aff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 隐私协议弹窗样式 */
.privacy-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.privacy-container {
  width: 80%;
  max-width: 650rpx;
  background-color: white;
  border-radius: 24rpx;
  padding: 40rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { 
    transform: translateY(50rpx);
    opacity: 0;
  }
  to { 
    transform: translateY(0);
    opacity: 1;
  }
}

.privacy-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.privacy-title::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -1rpx;
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #0288d1, #007aff);
  transform: translateX(-50%);
  border-radius: 4rpx;
}

.privacy-content {
  flex: 1;
  overflow-y: auto;
}

.privacy-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 40rpx;
}

.privacy-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.privacy-btn {
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
  margin: 0;
  transition: all 0.3s ease;
}

.view-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1rpx solid #e0e0e0;
}

.view-btn:active {
  background-color: #e0e0e0;
}

.view-btn::after {
  border: none;
}

.agree-btn {
  background: linear-gradient(135deg, #0288d1, #007aff);
  color: white;
  box-shadow: 0 6rpx 12rpx rgba(0, 122, 255, 0.2);
}

.agree-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.agree-btn::after {
  border: none;
}

/* 隐私协议勾选框样式 */
.privacy-checkbox-container {
  position: absolute;
  bottom: 80rpx;
  left: 0;
  margin-top: 20rpx;
  width: 100%;
  padding: 10rpx 0;
  display: flex;
  justify-content: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  position: relative;
  justify-content: center;
}

.custom-checkbox {
  width: 36rpx;
  height: 36rpx;
  border-radius: 6rpx;
  border: 2rpx solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  transition: all 0.2s ease;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.custom-checkbox.checked {
  background-color: var(--primary-color, #007aff);
  border-color: var(--primary-color, #007aff);
}

.check-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.checkbox-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.checkbox-text {
  margin-right: 4rpx;
  color:#1D1D1B;
  font-size: 24rpx;
}

.link {
  font-size: 24rpx;
  color: var(--primary-color, #F5A630);
  display: inline;
  position: relative;
  padding-bottom: 2rpx;
  border-bottom: 1rpx solid var(--primary-color, #F5A630);
}

/* Ripple animation for checkbox */
@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
} 