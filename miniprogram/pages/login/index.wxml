<!--login.wxml-->
<view class="container {{animationActive ? 'active' : ''}}">
  <!-- 隐私协议弹窗 -->
  <view class="privacy-overlay" wx:if="{{showPrivacy}}">
    <view class="privacy-container">
      <view class="privacy-title">隐私保护指引</view>
      <view class="privacy-content">
        <view class="privacy-text">
          为了更好地保护您的个人信息，在使用本小程序前，请您认真阅读并同意{{privacyContractName}}。我们将严格遵守相关法律法规，保护您的个人信息安全。
        </view>
        <view class="privacy-actions">
          <button 
            class="privacy-btn view-btn" 
            bindtap="handleOpenPrivacyContract"
          >查看隐私协议</button>
          <button 
            class="privacy-btn agree-btn" 
            open-type="agreePrivacyAuthorization" 
            bindagreeprivacyauthorization="handleAgreePrivacyAuthorization"
          >同意并继续</button>
        </view>
      </view>
    </view>
  </view>


  <view class="login-card">
    <!-- Logo和标题 -->
    <view class="logo-container">
      <image class="logo-image" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/H1.png" mode="" />
      
    </view>
    
    <!-- 登录按钮区域 -->
    <view class="buttons-container">
      <!-- 初始登录按钮 -->
      <button 
        wx:if="{{!pendingLoginCode}}" 
        class="login-btn primary-btn {{!agreedToPrivacy ? 'disabled' : ''}}"
        bindtap="handleLoginButtonTap"
        disabled="{{isLoginInProgress || !agreedToPrivacy}}"
      >
        微信授权登录
      </button>
      
      <!-- 授权选项，当需要用户授权时显示 -->
      <!-- <block wx:if="{{pendingLoginCode}}"> -->
        <!-- 微信头像授权按钮 -->
        <!-- <button 
          class="login-btn avatar-btn"
          open-type="chooseAvatar" 
          bindchooseavatar="onChooseAvatar"
          disabled="{{!agreedToPrivacy}}"
        >
          <text class="btn-icon">🖼️</text>
          <text>授权使用头像</text>
        </button>
        
        <button 
          class="login-btn auth-btn"
          bindtap="getUserProfileByTap"
          disabled="{{!agreedToPrivacy}}"
        >
          <text class="btn-icon">👤</text>
          <text>微信账号登录</text>
        </button>
        
        <button 
          class="login-btn secondary-btn"
          bindtap="completeLoginWithoutUserInfo"
          disabled="{{!agreedToPrivacy}}"
        >
          <text class="btn-icon">🔑</text>
          <text>快速登录(不授权)</text>
        </button> -->
      <!-- </block> -->
    </view>
    
    <!-- 用户隐私协议勾选 -->
    <view class="privacy-checkbox-container">
       <van-checkbox
            value="{{ agreedToPrivacy }}"
            checked-color="#1D1D1B"
            bind:change="onPrivacyCheckboxChange"
            icon-size="16px"
          >
             <text class="checkbox-text">我已阅读并同意</text>
            <text class="link" bindtap="handleOpenPrivacyContract">{{privacyContractName}}</text>
          </van-checkbox>
    </view>
  </view>
  
  
  <!-- 加载中遮罩 -->
  <view class="loader-overlay" wx:if="{{loaderVisible}}">
    <view class="loader"></view>
  </view>
</view> 