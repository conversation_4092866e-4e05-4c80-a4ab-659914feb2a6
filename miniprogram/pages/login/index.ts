// login.js
import { authService } from '../../service/auth/index';
import userService from '../../service/user/index';
import { initUserData } from '../../utils/init';
import API from '../../api/index';

Page({
  data: {
    // 登录相关
    isLoginInProgress: false,
    pendingLoginCode: '', // 存储等待用户授权的登录凭证
    hasUserInfo: false,
    isPageLoading: false,
    loadingText: '登录中...',
    loaderVisible: false,
    // 动画元素
    animationActive: false,
    // 头像相关
    avatarUrl: '',
    hasChosenAvatar: false,
    // 隐私协议相关
    showPrivacy: false,
    privacyContractName: '《隐私政策》',
    hasAgreedPrivacy: false,
    // 隐私协议勾选状态
    agreedToPrivacy: false
  },

  onLoad() {
    // 先检查隐私协议状态
    this.checkPrivacyAuthorization();
    
    // 加载动画
    setTimeout(() => {
      this.setData({
        animationActive: true
      });
    }, 300);
  },

  // 检查隐私授权状态
  checkPrivacyAuthorization() {
    wx.getPrivacySetting({
      success: res => {
        console.log('隐私设置状态:', res);
        if (res.needAuthorization) {
          // 需要弹出隐私协议
          this.setData({
            showPrivacy: true,
            privacyContractName: res.privacyContractName || '《隐私政策》'
          });
        } else {
          // 用户已经同意过隐私协议
          this.setData({
            hasAgreedPrivacy: true
          });
          // 检查是否已登录
          this.checkLoginStatusAndRedirect();
        }
      },
      fail: (err) => {
        console.error('获取隐私设置失败', err);
        // 获取失败时默认检查登录状态
        this.checkLoginStatusAndRedirect();
      }
    });
  },

  // 用户同意隐私协议
  handleAgreePrivacyAuthorization() {
    this.setData({
      showPrivacy: false,
      hasAgreedPrivacy: true
    });
    // 同意隐私协议后，检查登录状态
    this.checkLoginStatusAndRedirect();
  },

  // 打开隐私协议
  handleOpenPrivacyContract() {
    wx.openPrivacyContract({
      success: () => {
        console.log('隐私协议打开成功');
      },
      fail: (err) => {
        console.error('隐私协议打开失败', err);
        wx.showToast({
          title: '打开隐私协议失败',
          icon: 'none'
        });
      }
    });
  },

  // 检查登录状态并重定向
  async checkLoginStatusAndRedirect() {
    try {
      // 优先使用API的统一认证方法检查是否已登录
      if (API.isAuthenticated()) {
        console.log('API检测到用户已登录');
        // 已登录，重定向到回忆录页面
        wx.reLaunch({
          url: '/pages/interview/index'
        });
        return;
      }
      
      // 如果API方法检查失败，进行备用检查
      try {
        const isLoggedIn = await authService.checkLoginStatus();
        if (isLoggedIn) {
          console.log('authService检测到用户已登录');
          // 已登录，重定向到回忆录页面
          wx.reLaunch({
            url: '/pages/interview/index'
          });
        }
      } catch (authErr) {
        console.error('authService检查登录状态失败', authErr);
      }
    } catch (err) {
      console.error('检查登录状态失败', err);
    }
  },

  // 处理头像选择
  onChooseAvatar(e:any) {
    const { avatarUrl } = e.detail;
    
    if (avatarUrl) {
      this.setData({
        avatarUrl,
        hasChosenAvatar: true
      });
      
      wx.showToast({
        title: '头像已选择',
        icon: 'success'
      });
      
      // 保存头像到本地存储，以便登录时使用
      wx.setStorageSync('pendingAvatarUrl', avatarUrl);
    }
  },

  // 处理隐私协议勾选
  onPrivacyCheckboxChange(e:any) {
    this.setData({
      agreedToPrivacy: e.detail
    });
  },

  // 点击登录按钮
  async handleLoginButtonTap() {
    // 检查是否已勾选隐私协议
    if (!this.data.agreedToPrivacy) {
      wx.showToast({
        title: '请先同意隐私协议',
        icon: 'none'
      });
      return;
    }

    // 检查是否已同意隐私协议
    if (!this.data.hasAgreedPrivacy) {
      wx.showToast({
        title: '请先同意隐私协议',
        icon: 'none'
      });
      this.setData({
        showPrivacy: true
      });
      return;
    }
    
    if (this.data.isLoginInProgress) {
      return;
    }
    
    this.setData({ 
      isLoginInProgress: true,
      loaderVisible: true,
      loadingText: '登录中...'
    });
    
    try {
      const loginResult = await authService.startLogin();
      
      if (loginResult.success) {
        this.setData({
          hasUserInfo: true,
          pendingLoginCode: '',
          loadingText: '获取数据中...'
        });
        
        // 检查是否有待上传的头像
        await this.updateAvatarIfNeeded();
        
        // 初始化用户数据（获取用户信息和会话列表）
        try {
          const initResult = await initUserData();
          if (!initResult.success) {
            console.warn('初始化用户数据失败:', initResult.error);
            wx.showToast({
              title: '数据初始化失败，部分功能可能不可用',
              icon: 'none',
              duration: 2000
            });
          } else {
            console.log('用户数据初始化成功');
          }
        } catch (initError) {
          console.error('初始化用户数据出错:', initError);
        }
        
        // 登录成功，重定向到回忆录页面
        wx.reLaunch({
          url: '/pages/interview/index'
        });
      } else if (loginResult.needUserAuth && loginResult.code) {
        this.setData({
          pendingLoginCode: loginResult.code
        });
        // 页面展示登录按钮，等待用户点击授权
      } else {
        // 其他登录失败
        if (loginResult.error) {
          wx.showToast({
            title: loginResult.error,
            icon: 'none'
          });
        }
      }
    } catch (err) {
      console.error('登录失败', err);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ 
        isLoginInProgress: false,
        loaderVisible: false
      });
    }
  },
  
  // 上传头像（如果已选择）
  async updateAvatarIfNeeded() {
    const pendingAvatarUrl = wx.getStorageSync('pendingAvatarUrl');
    if (pendingAvatarUrl) {
      try {
        // 使用项目中的userService.updateAvatar方法
        const result = await userService.updateAvatar(pendingAvatarUrl);
        
        if (result && result.success) {
          console.log('头像更新成功', result);
          
          // 获取当前用户信息
          const userInfo = authService.getCachedUserInfo() || {};
          
          // 更新用户信息中的头像
          userInfo.avatarUrl = pendingAvatarUrl;
          
          // 保存更新后的用户信息
          authService.setUserInfo(userInfo);
          
          // 清除待上传的头像
          wx.removeStorageSync('pendingAvatarUrl');
        }
      } catch (err) {
        console.error('上传头像失败', err);
      }
    }
  },
  
  // 获取用户信息（由用户直接点击授权按钮触发）
  async getUserProfileByTap() {
    // 检查是否已勾选隐私协议
    if (!this.data.agreedToPrivacy) {
      wx.showToast({
        title: '请先同意隐私协议',
        icon: 'none'
      });
      return;
    }

    // 检查是否已同意隐私协议
    if (!this.data.hasAgreedPrivacy) {
      wx.showToast({
        title: '请先同意隐私协议',
        icon: 'none'
      });
      this.setData({
        showPrivacy: true
      });
      return;
    }
    
    if (!this.data.pendingLoginCode) {
      wx.showToast({
        title: '登录凭证失效，请重新点击登录',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      loaderVisible: true
    });
    
    try {
      // 获取用户信息
      const userInfo = await authService.getUserProfile();
      console.log('Login Page: wx.getUserProfile result:', JSON.stringify(userInfo));
      
      // 检查是否有已选择的头像，如果有，替换userInfo中的头像
      const pendingAvatarUrl = wx.getStorageSync('pendingAvatarUrl');
      if (pendingAvatarUrl && userInfo && userInfo.userInfo) {
        userInfo.userInfo.avatarUrl = pendingAvatarUrl;
      }
      
      // 使用code和用户信息完成登录
      const authResult = await authService.handleUserInfoAuth(
        this.data.pendingLoginCode, 
        userInfo
      );
      
      if (authResult.success) {
        this.setData({
          hasUserInfo: true,
          pendingLoginCode: '',
          loadingText: '获取数据中...',
          loaderVisible: true
        });
        
        // 清除待上传的头像，因为已经在登录时使用了
        wx.removeStorageSync('pendingAvatarUrl');

        // 初始化用户数据
        try {
          const initResult = await initUserData();
          if (!initResult.success) {
            console.warn('初始化用户数据失败:', initResult.error);
          } else {
            console.log('初始化用户数据成功');
          }
        } catch (initError) {
          console.error('初始化用户数据失败:', initError);
        }
        
        this.setData({
          loaderVisible: false
        });
        
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500,
          success: () => {
            setTimeout(() => {
              // 登录成功，重定向到回忆录页面
              wx.reLaunch({
                url: '/pages/interview/index'
              });
            }, 1500);
          }
        });
      } else {
        wx.showToast({
          title: authResult.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (err) {
      console.error('获取用户信息失败', err);
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loaderVisible: false
      });
    }
  },
  
  // 直接完成登录流程（不需要用户信息）
  async completeLoginWithoutUserInfo() {
    // 检查是否已勾选隐私协议
    if (!this.data.agreedToPrivacy) {
      wx.showToast({
        title: '请先同意隐私协议',
        icon: 'none'
      });
      return;
    }

    // 检查是否已同意隐私协议
    if (!this.data.hasAgreedPrivacy) {
      wx.showToast({
        title: '请先同意隐私协议',
        icon: 'none'
      });
      this.setData({
        showPrivacy: true
      });
      return;
    }
    
    if (!this.data.pendingLoginCode) {
      wx.showToast({
        title: '登录凭证失效，请重新点击登录',
        icon: 'none'
      });
      return;
    }
    
    try {
      this.setData({ 
        isLoginInProgress: true,
        loaderVisible: true
      });
      
      // 直接使用code完成登录
      const loginResult = await authService.completeLogin(
        this.data.pendingLoginCode
      );
      
      if (loginResult.success) {
        this.setData({
          hasUserInfo: !!loginResult.userInfo,
          pendingLoginCode: '',
          loadingText: '获取数据中...'
        });
        
        // 成功登录后，检查是否有待上传的头像
        await this.updateAvatarIfNeeded();

        // 初始化用户数据
        try {
          const initResult = await initUserData();
          if (!initResult.success) {
            console.warn('初始化用户数据失败:', initResult.error);
          } else {
            console.log('初始化用户数据成功');
          }
        } catch (initError) {
          console.error('初始化用户数据失败:', initError);
        }
        
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500,
          success: () => {
            setTimeout(() => {
              // 登录成功，重定向到回忆录页面
              wx.reLaunch({
                url: '/pages/interview/index'
              });
            }, 1500);
          }
        });
      } else {
        wx.showToast({
          title: loginResult.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (err) {
      console.error('完成登录失败', err);
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      });
    } finally {
      this.setData({ 
        isLoginInProgress: false,
        loaderVisible: false
      });
    }
  },

  // 授权完成后的处理
  async handleAuthComplete(result) {
    if (result.success) {
      this.setData({
        hasUserInfo: true,
        loadingText: '获取数据中...',
        loaderVisible: true
      });
      
      // 初始化用户数据（获取用户信息和会话列表）
      try {
        const initResult = await initUserData();
        if (!initResult.success) {
          console.warn('初始化用户数据失败:', initResult.error);
          wx.showToast({
            title: '数据初始化失败，部分功能可能不可用',
            icon: 'none',
            duration: 2000
          });
        } else {
          console.log('用户数据初始化成功');
        }
      } catch (initError) {
        console.error('初始化用户数据出错:', initError);
      } finally {
        this.setData({
          loaderVisible: false
        });
      }
      
      // 重定向到回忆录页面
      wx.reLaunch({
        url: '/pages/interview/index'
      });
    } else {
      // 授权失败
      wx.showToast({
        title: result.error || '授权失败',
        icon: 'none'
      });
    }
  }
}) 