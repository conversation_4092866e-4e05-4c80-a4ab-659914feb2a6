<!--pages/home/<USER>
<view class="interview-container">
  <!-- 顶部插图 -->
  <image src="/assets/images/header.png" mode="" class="interview-hd" />
  <view class="interview-header" />
  <!-- 时间轴 -->
  <view class="interview-timelinedot" bindtap="gotoList">
    <!-- 时间节点列表 -->
    <view 
      wx:for="{{timelineNodes}}" 
      wx:key="id"
      class="timeline-node-wrapper"
      style="left: {{item.left}}rpx; top: {{item.top}}rpx;"
    >
      <!-- 节点图片 -->
      <image 
        src="{{item.image}}" 
        mode="" 
        class="timeline-dot {{item.dotClass}}"
        style="transform: translate({{item.animationX}}rpx, {{item.animationY}}rpx); opacity: {{item.opacity}};"
      />
      <!-- 节点标题 -->
      <view 
        wx:if="{{item.id !== 6}}"
        class="timeline-title"
        style="opacity: {{item.titleOpacity}};"
      >
        {{item.title}}
      </view>
      <!-- 发光效果 -->
      <view 
        class="timeline-glow"
        style="transform: translate({{item.animationX}}rpx, {{item.animationY}}rpx); opacity: {{item.glowOpacity}};"
      ></view>
    </view>
  </view>
  <!-- 对话框 -->
  <Dialog 
    id="questionInput"
    question="{{questionObj}}"
    bind:input="onInput"
    bind:voice="onVoice"
    visible="{{questionInputVisible}}"
    bind:refreshquestion="handleRefreshQuestion"
    isPreParing="{{isPreParing}}"
  />
  <!-- 小男孩图片 -->
  <image src="/assets/images/boy.png" mode="" class="interview-img" bind:tap="onTogglePopup"/>
  <!-- 背景图 -->
  <image src="/assets/images/bg.png" mode="" class="interview-bg" />
</view>