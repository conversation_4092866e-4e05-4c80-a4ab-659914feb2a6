import chatApi from '../../api/modules/chat';
import contentApi from '../../api/modules/data';

// 时间轴节点接口
interface TimelineNode {
  id: number;
  image: string;
  title: string;
  left: number;
  top: number;
  dotClass: string;
  titleTop: number;
  animationX: number;
  animationY: number;
  opacity: number;
  titleOpacity: number;
  glowOpacity: number;
  endX: number;
  endY: number;
}

// index.js
Page({
  data: {
    questionInputVisible: false,
    hasAutoPopup: false,
    questionObj: {},
    timelineNodes: [] as TimelineNode[],
    animationTimer: null as any,
    currentAnimationIndex: 0,
    isPreParing:false,
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0 // 当前 tab 的索引，0/1/2
      });
    }
  },

  onLoad() {
    // 初始化时间轴数据
    this.initTimelineNodes();

    this.registerWebSocketListener()
    // 首次进入页面自动弹出
    this.setData({ questionInputVisible: true, hasAutoPopup: true });
    // 3秒后自动关闭（如果不在输入/语音识别中）
    setTimeout(() => {
      this.tryHidePopup();
    }, 3000);
    //  获取当前问题
    this.getQuestion();
    this.startTimelineAnimation();
  },

  // 初始化时间轴节点数据
  async initTimelineNodes() {
      // 调用getChatOverView接口获取数据
      const response = await contentApi.getChatOverView();
      console.log('getChatOverView response:', response);
      // 提取content数据
      const contentData = response.storyList.map((story: { content: any[]; }) => story.content.map((item) => item.title
      )).flat()

      // 去重处理：根据title去重，填充了null
      const uniqueContent = this.removeDuplicateContent(contentData);
      // 把overview的信息存到storage里面
      const uniqueContentWithoutNull = uniqueContent.filter(item => item !== null);
      const lastTitle = uniqueContentWithoutNull[uniqueContentWithoutNull.length - 1];
      wx.setStorageSync('chatOverView', {
        titleList: uniqueContentWithoutNull,
        title: lastTitle, 
        basicProgress: parseFloat((uniqueContentWithoutNull.length/21 * 100).toFixed())
      });
      // 将接口数据转换为时间轴节点格式
      const timelineNodes = this.transformContentToTimelineNodes(uniqueContent);
      console.log('timelineNodes', timelineNodes,contentData,uniqueContent)

      this.setData({ timelineNodes });
  },
    /**
   * 注册WebSocket消息监听器
   * 用于监听章节内容生成的流式返回
   */
  registerWebSocketListener() {
    try {
      const app = getApp();
      if (!app || !app.API || !app.API.websocket) {
        console.error('WebSocket API未初始化');
        return;
      }

      // 注册消息监听器
      app.API.websocket.registerMessageListener('interview-listener', (message:any) => {
        this.handleWebSocketMessage(message);
      });
    } catch (error) {
      console.error('注册WebSocket监听器失败:', error);
    }
  },
    /**
   * 处理WebSocket消息
   * @param {Object} message - 收到的消息对象
   */
  handleWebSocketMessage(message:any) {
    try {
      // 解析消息
      if (typeof message === 'string') {
        try {
          message = JSON.parse(message);
        } catch (error) {
          // 忽略非JSON消息
          return;
        }
      }
      const {type,data} = message;
      if (type === 'period_progress_update') {
       this.handleprogressUpdate(data)
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error);
    }
  },
    /**
   * 处理时期完成
   */
  handleprogressUpdate(data:any) {
    this.navigateToCompletePage(data);
  },

  /**
   * 跳转到完成页面
   */
  navigateToCompletePage(data:any) {
    wx.navigateTo({
      url: '/pages/completePage/index?force=true&data=' + JSON.stringify(data),
    });
  },

  // 去重处理：根据title去重
  removeDuplicateContent(contentData: any[]): any[] {
    const seen = new Set();
    const uniqueContent = contentData.filter(item => {
      const title = item || '';
      if (seen.has(title)) {
        return false;
      }
      seen.add(title);
      return true;
    });
  
    // 填充数据至长度为6
    while (uniqueContent.length < 6) {
      uniqueContent.push(null); // 或者你可以选择填充其他默认值
    }
  
    return uniqueContent;
  },

  // 将接口数据转换为时间轴节点格式
  transformContentToTimelineNodes(contentData: any[]): TimelineNode[] {
    // 默认的时间轴节点配置
    const defaultConfigs = [
      {
        left: 40,
        top: 270,
        dotClass: '',
        titleTop: 120,
        endX: 170,
        endY: -127,
        glowOpacity: 0.6
      },
      {
        left: 260,
        top: 250,
        dotClass: 'dot1',
        titleTop: 85,
        endX: -230,
        endY: -295,
        glowOpacity: 0.4
      },
      {
        left: 360,
        top: 140,
        dotClass: 'dot2',
        titleTop: 100,
        endX: -280,
        endY: -130,
        glowOpacity: 0.3
      },
      {
        left: 180,
        top: 100,
        dotClass: 'dot3',
        titleTop: 80,
        endX: 0,
        endY: -100,
        glowOpacity: 0.2
      },
      {
        left: -10,
        top: 80,
        dotClass: 'dot4',
        titleTop: 60,
        endX: 110,
        endY: -80,
        glowOpacity: 0.1
      },
      {
        left: 50,
        top: 10,
        dotClass: 'dot5',
        titleTop: 40,
        endX: 70,
        endY: -10,
        glowOpacity: 0.05
      }
    ];

    // 默认图片数组
    const defaultImages = [
      'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group.png',
      'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group1.png',
      'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group2.png',
      'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group3.png',
      'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group4.png',
      'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group5.png'
    ];

    // 转换数据
    return contentData.slice(0, 6).map((item, index) => {
      const config = defaultConfigs[index] || defaultConfigs[0];
      const image = defaultImages[index] || defaultImages[0];

      return {
        id: index + 1,
        image: image,
        title: item||  '暂未解锁',
        left: config.left,
        top: config.top,
        dotClass: config.dotClass,
        titleTop: config.titleTop,
        animationX: 0,
        animationY: 0,
        opacity: 1,
        titleOpacity: 1,
        glowOpacity: config.glowOpacity,
        endX: config.endX,
        endY: config.endY
      };
    });
  },

  // 设置默认时间轴节点数据（当接口调用失败时使用）
  setDefaultTimelineNodes() {
    const timelineNodes: TimelineNode[] = [
      {
        id: 1,
        image: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group.png',
        title: '1/4·家的温暖记忆',
        left: 40,
        top: 270,
        dotClass: '',
        titleTop: 120,
        animationX: 0,
        animationY: 0,
        opacity: 1,
        titleOpacity: 1,
        glowOpacity: 0.6,
        endX: 170,
        endY: -127
      },
      {
        id: 2,
        image: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group1.png',
        title: '童年的旅行记忆',
        left: 260,
        top: 250,
        dotClass: 'dot1',
        titleTop: 85,
        animationX: 0,
        animationY: 0,
        opacity: 1,
        titleOpacity: 1,
        glowOpacity: 0.4,
        endX: -230,
        endY: -295
      },
      {
        id: 3,
        image: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group2.png',
        title: '学校的美好时光',
        left: 360,
        top: 140,
        dotClass: 'dot2',
        titleTop: 100,
        animationX: 0,
        animationY: 0,
        opacity: 1,
        titleOpacity: 1,
        glowOpacity: 0.3,
        endX: -280,
        endY: -130
      },
      {
        id: 4,
        image: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group3.png',
        title: '青春的梦想',
        left: 180,
        top: 100,
        dotClass: 'dot3',
        titleTop: 80,
        animationX: 0,
        animationY: 0,
        opacity: 1,
        titleOpacity: 1,
        glowOpacity: 0.2,
        endX: 0,
        endY: -100
      },
      {
        id: 5,
        image: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group4.png',
        title: '未来的憧憬',
        left: -10,
        top: 80,
        dotClass: 'dot4',
        titleTop: 60,
        animationX: 0,
        animationY: 0,
        opacity: 1,
        titleOpacity: 1,
        glowOpacity: 0.1,
        endX: 110,
        endY: -80
      },
      {
        id: 6,
        image: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group5.png',
        title: '人生的总结',
        left: 50,
        top: 10,
        dotClass: 'dot5',
        titleTop: 40,
        animationX: 0,
        animationY: 0,
        opacity: 1,
        titleOpacity: 1,
        glowOpacity: 0.05,
        endX: 70,
        endY: -10
      }
    ];
    this.setData({ timelineNodes });
  },

  // 开始时间轴动画
  startTimelineAnimation() {
    // 清除可能存在的旧定时器
    if (this.data.animationTimer) {
      clearInterval(this.data.animationTimer);
    }

    // 设置自动循环定时器（每8秒一个周期）
    this.data.animationTimer = setInterval(() => {
      this.animateNextNode();
    }, 8000);
  },

  // 动画下一个节点
  animateNextNode() {
    const { timelineNodes, currentAnimationIndex } = this.data;
    const totalNodes = timelineNodes.length;

    // 重置所有节点到初始状态
    const resetNodes = timelineNodes.map(node => ({
      ...node,
      animationX: 0,
      animationY: 0,
      opacity: 1,
      titleOpacity: 1,
      glowOpacity: 0.1
    }));

    this.setData({ timelineNodes: resetNodes });

    // 延迟后开始动画
    setTimeout(() => {
      this.animateNode(currentAnimationIndex);

      // 更新动画索引
      const nextIndex = (currentAnimationIndex + 1) % totalNodes;
      this.setData({ currentAnimationIndex: nextIndex });
    }, 500);
  },

  // 动画指定节点
  animateNode(nodeIndex: number) {
    const { timelineNodes } = this.data;
    const node = timelineNodes[nodeIndex];

    if (!node) return;

    // 第一步：淡出并移动到终点
    this.updateNodeAnimation(nodeIndex, {
      opacity: 0,
      titleOpacity: 0,
      glowOpacity: 0
    });

    setTimeout(() => {
      // 第二步：移动到终点位置
      this.updateNodeAnimation(nodeIndex, {
        animationX: node.endX,
        animationY: node.endY,
        opacity: 1,
        titleOpacity: 1,
        glowOpacity: 0.8
      });

      // 第三步：延迟后回到起点
      setTimeout(() => {
        this.updateNodeAnimation(nodeIndex, {
          opacity: 0,
          titleOpacity: 0,
          glowOpacity: 0
        });

        setTimeout(() => {
          // 第四步：回到起点位置
          this.updateNodeAnimation(nodeIndex, {
            animationX: 0,
            animationY: 0,
            opacity: 1,
            titleOpacity: 1,
            glowOpacity: 0.1
          });
        }, 500);
      }, 2000);
    }, 500);
  },

  // 更新节点动画状态
  updateNodeAnimation(nodeIndex: number, updates: Partial<TimelineNode>) {
    const { timelineNodes } = this.data;
    const updatedNodes = [...timelineNodes];
    updatedNodes[nodeIndex] = { ...updatedNodes[nodeIndex], ...updates };
    this.setData({ timelineNodes: updatedNodes });
  },

  onTogglePopup() {
    const questionInput = this.selectComponent('#questionInput');
    if (this.data.questionInputVisible) {
      // 关闭时判断是否允许
      if (questionInput && questionInput.isBusy && questionInput.isBusy()) {
        wx.showToast({ title: '输入中，无法关闭', icon: 'none' });
        return;
      }
      this.setData({ questionInputVisible: false });
    } else {
      this.setData({ questionInputVisible: true });
    }
  },

  tryHidePopup() {
    const questionInput = this.selectComponent('#questionInput');
    if (questionInput && questionInput.isBusy && questionInput.isBusy()) {
      // 输入/语音识别中，延迟再试
      setTimeout(() => {
        this.tryHidePopup();
      }, 1000);
    } else {
      this.setData({ questionInputVisible: false });
    }
  },

  onInput(e: any) {
    console.log('手动输入内容:', e.detail.value);
  },

  onVoice(e: any) {
    console.log('语音识别内容:', e.detail.text);
  },

  gotoList() {
    wx.navigateTo({
      url: '/pages/interviewList/index'
    });
  },

  // 获取当前问题
  async getQuestion() {
    const maxRetries = 40;
    const retryInterval = 1000; // 1000ms

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const { code, question } = await chatApi.getQuestion();
        if (code === 2000) {
          this.setData({
            questionObj: question,
            isPreParing: false
          });
          return; // 成功获取，退出轮询
        } else {
          // code 不是 2000，设置 isPreParing 状态
          this.setData({
            isPreParing: true
          });
        }

        // 如果不是最后一次尝试，等待后继续
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryInterval));
        }
      } catch (err: any) {
        console.error(`获取问题失败 (第${attempt}次尝试):`, err);

        // 异常情况也设置 isPreParing 状态
        this.setData({
          isPreParing: true
        });

        // 如果不是最后一次尝试，等待后继续
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryInterval));
        }
      }
    }

    // 所有尝试都失败了
    console.error('获取问题失败: 已达到最大重试次数');
    wx.showToast({ title: '获取问题失败', icon: 'none' });
  },

  handleRefreshQuestion() {
    this.getQuestion(); // 重新获取问题
  },

  onUnload() {
    // 页面卸载时清除定时器
    if (this.data.animationTimer) {
      clearInterval(this.data.animationTimer);
      this.data.animationTimer = null;
    }
  }
});