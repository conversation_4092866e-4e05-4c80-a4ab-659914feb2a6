
.interview-container{
  // position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
    .interview-header{
        height: 400rpx;
        
    }
    .interview-timeline{
        height: 450rpx;
    }
    .interview-img{
      width: 528rpx;
      height: 440rpx;
    }
    .interview-bg{
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: -1;
    }
    .interview-hd{
      width: 100%;
      height: 400rpx;
      position: absolute;
    }
    
    .interview-timelinedot{
      position: relative;
      width: 560rpx;
      height: 448rpx;
    }

    // 时间节点包装器
    .timeline-node-wrapper {
      position: absolute;
      z-index: 10;
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      &:hover {
        transform: scale(1.05);
      }
    }

    // 时间节点基础样式
    .timeline-dot{
      position: relative;
      width: 160rpx;
      height: 160rpx;
      // margin-bottom: 2rpx;
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.2));
      
      &.dot1 {
        width: 120rpx;
        height: 120rpx;
      }
      
      &.dot2 {
        width: 100rpx;
        height: 100rpx;
      }
      
      &.dot3 {
        width: 100rpx;
        height: 100rpx;
      }
      
      &.dot4 {
        width: 90rpx;
        height: 90rpx;
      }
      
      &.dot5 {
        width: 80rpx;
        height: 80rpx;
      }
    }

    // 节点标题样式
    .timeline-title {
      color: #fff;
      position: relative;
      font-size: 28rpx;
      font-weight: 500;
      text-align: center;
      white-space: nowrap;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
      transition: all 0.6s ease;
      
      // 添加渐变背景
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.1) 0%, 
        rgba(255, 255, 255, 0.05) 100%);
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      backdrop-filter: blur(10rpx);
      border: 1rpx solid rgba(255, 255, 255, 0.2);
    }

    // 发光效果
    .timeline-glow {
      position: absolute;
      width: 200rpx;
      height: 200rpx;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: radial-gradient(
        circle,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.1) 40%,
        transparent 70%
      );
      border-radius: 50%;
      pointer-events: none;
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: -1;
    }

    // 动画关键帧
    @keyframes pulse {
      0%, 100% {
        transform: scale(1);
        opacity: 0.6;
      }
      50% {
        transform: scale(1.1);
        opacity: 1;
      }
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-10rpx);
      }
    }

    @keyframes glow {
      0%, 100% {
        box-shadow: 0 0 20rpx rgba(255, 255, 255, 0.4);
      }
      50% {
        box-shadow: 0 0 40rpx rgba(255, 255, 255, 0.8);
      }
    }

    // 为不同节点添加不同的动画效果
    .timeline-node-wrapper:nth-child(1) .timeline-dot {
      animation: float 3s ease-in-out infinite;
    }
    
    .timeline-node-wrapper:nth-child(2) .timeline-dot {
      animation: pulse 4s ease-in-out infinite;
    }
    
    .timeline-node-wrapper:nth-child(3) .timeline-dot {
      animation: float 3.5s ease-in-out infinite reverse;
    }
    
    .timeline-node-wrapper:nth-child(4) .timeline-dot {
      animation: pulse 3.8s ease-in-out infinite;
    }
    
    .timeline-node-wrapper:nth-child(5) .timeline-dot {
      animation: float 4.2s ease-in-out infinite;
    }
    
    .timeline-node-wrapper:nth-child(6) .timeline-dot {
      animation: pulse 3.2s ease-in-out infinite;
    }

    // 响应式设计
    @media (max-width: 750rpx) {
      .timeline-title {
        font-size: 20rpx;
        padding: 6rpx 12rpx;
      }
      
      .timeline-dot {
        width: 120rpx;
        height: 120rpx;
        
        &.dot1, &.dot2, &.dot3 {
          width: 80rpx;
          height: 80rpx;
        }
        
        &.dot4, &.dot5 {
          width: 70rpx;
          height: 70rpx;
        }
      }
    }
}
