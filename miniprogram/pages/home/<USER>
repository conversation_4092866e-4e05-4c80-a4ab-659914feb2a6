/* pages/home/<USER>/
.home-container {
  height: 100vh;
  overflow-y: scroll;
  background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/mineBg.png');
  background-repeat: no-repeat;
  background-size: auto 460rpx;
  background-position: top center;
  background-color: #f7f7f7;
  position: relative;
  .home-header {
    padding-top:40rpx;
    height: 340rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .avatar-container {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      .avatar {
        width: 112rpx;
        height: 112rpx;
        border-radius: 50%;
        position: absolute;
        background-color: #fff;
      }
      .sex {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        right: 0;
        bottom: 20rpx;
      }
    }
    .username {
      font-size: 32rpx;
      color: #282624;
      margin-top: 15rpx;
    }
  }

  .time-quote-container {
    width: 85%;
    margin: 0 auto;
    margin-top: -40rpx;
    position: relative;
    z-index: 10;
  }
  .questions-progress {
    background-color: #fff;
    padding: 40rpx 30rpx;
    border-radius: 24rpx;
    // position: absolute;
    width: 85%;

    right: 0;
    left: 0;
    margin: auto;
    margin-top: 20rpx;
    &-header-title {
      display: flex;
      align-items: center;
      &-h1 {
        font-size: 32rpx;
        color: #282624;
        font-weight: 500;
        margin-right: 18rpx;
      }
      &-icon {
        font-size: 28rpx;
        color: #28262466;
        font-weight: 700;
        border: 1px solid #2826241a;
        padding: 4rpx 10rpx;
        border-radius: 28rpx;
      }
    }

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;
    }
  }
  .font-size-container {
    width: 93%;
    margin-top: 30rpx;
    margin-left: auto;
    margin-right: auto;
    .card-text {
      font-size: 32rpx;
      color: #282624;
      font-weight: 500;
      margin-bottom: 30rpx;
      margin-bottom: 60rpx;
    }
    .slider-container{
      width: 90%;
    }
    .custom-button {
      width: 40rpx;
      height: 40rpx;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 999 !important; /* 使用更高的z-index和!important确保按钮在最上层 */
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1); /* 添加阴影增强视觉层次 */
    }
  }
  /* van-slider 组件样式覆盖 */
  .van-slider {
    position: relative;
    z-index: 100 !important;
  }

  .van-slider__button {
    z-index: 998 !important;
  }

  .ticks {
    position: relative;
    height: 20px;
    z-index: 1; /* 确保ticks容器在较低层级 */
    margin-top: -20rpx; /* 调整ticks位置，避免与滑块重叠 */
  }
  .cell-container {
    margin: auto;
    margin-top: 30rpx;
  }

  .tick {
    position: absolute;
    width: 4rpx;
    height: 16rpx;
    background-color: #e6e6e6;
    top: 10rpx; /* 调整tick位置，放在滑块下方 */
    z-index: 2; /* tick刻度线在中间层级 */
    transform: translateX(-50%); /* 居中对齐 */
  }

  .tick-label {
    position: absolute;
    top: 30rpx; /* 调整标签位置，放在tick下方 */
    left: -20rpx; /* 调整标签位置，居中对齐 */
    font-size: 24rpx;
    color: #e6e6e6;
    width: 40rpx;
    text-align: center;
    z-index: 3; /* tick标签在较高层级，但仍低于按钮 */
  }
  .preview {
    margin-top: 50rpx;
    &-title {
      color: #28262466;
      font-size: 24rpx;
    }
  }
}
