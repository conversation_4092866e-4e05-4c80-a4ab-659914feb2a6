import { resolveImageSrc } from '../../utils/image-cache';
import chatApi from '../../api/modules/chat';
// pages/home/<USER>
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo:{
      avatar:'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/mine.png',
      username:'未登录用户'
    },
    sexLogo:'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/sex_man.png',
    currentValue: 12,
    ticks: [] as Array<{label: string, left: number}>,
    chatOverView:{},
    quoteTheme: 'default', // 时光卡片主题
    customQuotes: [], // 自定义语句库
    fontSizeIcon:'',
    buyImage:'',
    usImage:'',
    feedbackImage:'',
    settingImage:'',
    groupImage:'',
    userProgress:{}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {
    this.initCachedImages();
    this.generateTicks();
    this.loadUserInfo();
    this.loadChatOverView();
    this.initQuoteTheme();
    this.getProgress()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      });
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  async getProgress(){
    const res = await chatApi.getProgress();
    const periodsArray = wx.getStorageSync('periodsArray')
    const allTopicsObjList = periodsArray.map((period:any) => {
      return period.topics.map((topic:any)=>{
        return {topic_id:topic.topic_id,topic_name:topic.topic_name}
      });
    }).flat();
    const topicTitle = allTopicsObjList.find((item:any)=>item.topic_id === res.user_progress.current_topic).topic_name

    if(!res) return;
    console.log('???',res,allTopicsObjList)
    this.setData({
      userProgress:{
        current_topic:res.user_progress.current_topic,
        completion_rate:Math.floor(res.user_progress.overall_stats.completion_rate*100),
        completed_topics:res.user_progress.overall_stats.completed_topics,
        total_topics:res.user_progress.overall_stats.total_topics,
        topicTitle
      }
    })
  },

  async initCachedImages() {
    const fontSizeIcon = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/fontsize-icon1.png';
    const buyImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/buy.png';
    const usImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/us.png';
    const feedbackImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/feedback.png';
    const settingImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/setting.png';
    const groupImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group.png';
    const fontSizeIconlocal = await resolveImageSrc(fontSizeIcon,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    const buyImagelocal = await resolveImageSrc(buyImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    const usImagelocal = await resolveImageSrc(usImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    const feedbackImagelocal = await resolveImageSrc(feedbackImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    const settingImagelocal = await resolveImageSrc(settingImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    const groupImagelocal = await resolveImageSrc(groupImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    this.setData({
      fontSizeIcon:fontSizeIconlocal,
      buyImage:buyImagelocal,
      usImage:usImagelocal,
      feedbackImage:feedbackImagelocal,
      settingImage:settingImagelocal,
      groupImage:groupImagelocal
    })
  },

  onCardTap(e: any) {
    const { currentProgress, totalProgress, progressPercentage } = e.detail;
    
    wx.showToast({
      title: `进度: ${currentProgress}/${totalProgress} (${progressPercentage}%)`,
      icon: 'none',
      duration: 2000
    });
    
    console.log('卡片被点击', { currentProgress, totalProgress, progressPercentage });
  },
   onDrag(event:any) {
    this.setData({
      currentValue: event.detail.value,
    });
    wx.setStorageSync('fontSize', event.detail.value);
  },
  onChange(event:any) {
    this.setData({
      currentValue: event.detail,
    });
    wx.setStorageSync('fontSize', event.detail);
  },
  generateTicks() {
    const min = 12;
    const max = 40;
    const step = 4;
    const ticks = [];
    for (let i = min; i <= max; i += step) {
      ticks.push({
        label: i.toString(),
        // 修正left计算：(当前值 - 最小值) / (最大值 - 最小值) * 100
        left: ((i - min) / (max - min)) * 100,
      });
    }
    this.setData({
      ticks: ticks,
    });
  },
    // 从本地存储加载用户信息
  loadUserInfo() {
    try {
      // 从storage中获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({
          userInfo: userInfo,
          // 用户有数据，默认为会员 (实际项目中应该根据用户数据判断)
          isVip: true
        });
        console.log('加载用户信息成功', userInfo);
      } else {
        console.log('本地存储中没有用户信息');
        // 如果没有用户信息，可以尝试从app.globalData获取
        const app = getApp();
        const globalUserInfo = app.globalData.userInfo;
        if (globalUserInfo) {
          this.setData({
            userInfo: globalUserInfo,
            isVip: true
          });
          console.log('从globalData加载用户信息成功', globalUserInfo);
        }
      }
    } catch (e) {
      console.error('获取用户信息失败', e);
    }
  },
  getWxUserInfo(){
    console.log('点击获取用户信息')
    // 跳转到用户信息编辑页面
    wx.navigateTo({
      url: '/pages/userProfile/index'
    });
  },
  // 从本地存储家在当前字体和进度
  loadChatOverView(){
    try {
      const chatOverView = wx.getStorageSync('chatOverView');
      const fontSize = wx.getStorageSync('fontSize');
      if (fontSize) {
        this.setData({
          currentValue: fontSize
        });
      }
      if (chatOverView) {
        this.setData({
          chatOverView
        });
        console.log('加载进度成功', chatOverView);
      }
    } catch (e) {
      console.error('获取进度失败', e);
    }
  },
  gotoInterview(){
    wx.navigateTo({
      url: '/pages/interviewList/index'
    });
  },

  // 初始化时光卡片主题
  initQuoteTheme() {
    // 根据时间或用户偏好设置主题
    const hour = new Date().getHours();
    let theme = 'default';

    if (hour >= 6 && hour < 12) {
      theme = 'warm'; // 早晨使用温暖主题
    } else if (hour >= 12 && hour < 18) {
      theme = 'elegant'; // 下午使用优雅主题
    } else if (hour >= 18 && hour < 22) {
      theme = 'classic'; // 傍晚使用经典主题
    } else {
      theme = 'default'; // 夜晚使用默认主题
    }

    this.setData({
      quoteTheme: theme
    });
  },

  // 时光卡片点击事件
  onQuoteCardTap(e: any) {
    const { quote } = e.detail;
    console.log('时光卡片被点击:', quote);

    // 可以添加分享功能或其他交互
    wx.showToast({
      title: '感谢阅读',
      icon: 'none',
      duration: 1500
    });
  },

  // 时光卡片刷新事件
  onQuoteRefresh(e: any) {
    const { quote } = e.detail;
    console.log('时光卡片已刷新:', quote);

    wx.showToast({
      title: '已更换语录',
      icon: 'success',
      duration: 1000
    });
  }
});
