<!--pages/home/<USER>
<view class="home-container">
<view style="margin-bottom:200rpx">
  <view class="home-header">
    <view class="avatar-container" bind:tap="getWxUserInfo">
      <image class="avatar" src="{{userInfo.avatar}}" mode="aspectFill"></image>
      <!--<image class="sex" src="{{sexLogo}}" mode="aspectFill"></image> -->
    </view>
    <view class="username">{{userInfo.username || '未登录用户'}}</view>
  </view>
  <view class="font-size-container">
    <Card>
      <view class="card-text">
        字体调节
      </view>
      <van-slider value="{{ currentValue }}" use-button-slot bind:change="onChange" bind:drag="onDrag" step="1" min="12" max="40" active-color="#F5A630">
        <image src="{{fontSizeIcon || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/fontsize-icon1.png'}}" class="custom-button" slot="button" />
      </van-slider>
      <view class="ticks">
        <view wx:for="{{ticks}}" wx:key="index" class="tick" style="left: {{item.left}}%;">
          <text class="tick-label">{{item.label}}</text>
        </view>
      </view>
			<view class="preview">
				<view class="preview-title">
					预览
				</view>
				<view class="preview-content" style="font-size: {{currentValue}}px;">
					这是使用当前字体大小的文本示例
				</view>
			</view>
    </Card>
  </view>
	
	<view class="cell-container">
		<van-cell-group inset>
			<van-cell title="产品购买" title-style="font-size:32rpx;" icon="{{buyImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/buy.png'}}" is-link link-type="navigateTo"  url="/pages/purchase/index"/>
			<van-cell title="关于我们" title-style="font-size:32rpx;" icon="{{usImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/us.png'}}" is-link link-type="navigateTo"  url="/pages/aboutUs/index"/>
			<van-cell title="意见反馈" title-style="font-size:32rpx;" icon="{{feedbackImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/feedback.png'}}" is-link link-type="navigateTo"  url="/pages/feedBack/index"/>
			<van-cell title="设置" title-style="font-size:32rpx;" icon="{{settingImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/setting.png'}}" is-link link-type="navigateTo"  url="/pages/settings/index"/>
		</van-cell-group>
	</view>
</view>
</view>