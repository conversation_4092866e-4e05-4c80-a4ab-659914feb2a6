<view class="book-container">
  <BookCard bookCover="{{bookCover}}" bookContent="{{bookContent}}" bookUpdateTime="{{bookUpdateTime}}" totalCount="{{totalCount}}" bookTitle="{{bookTitle}}" />


  <view class="book-container-catalogtitle">
    <view class="catalog-item">
      <image src="{{iconcatalogImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/iconcatalog.png'}}" mode="" />
      <view class="title">目录</view>
    </view>
    <view class="catalog-item">
      <view class="update" bind:tap="getGenerateOutline">
        <image src="{{clickImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Click.png'}}" mode="aspectFit" />
        <text class="">感觉不满意？点击重新生成</text>
      </view>
      <image src="{{preImageImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/pre.png'}}" mode="aspectFit" bind:tap="previousVersion" />
      <image src="{{nextImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/next.png'}}" mode="aspectFit" bind:tap="nextVersion" />
        
    </view>
      
  </view>
  <!-- Loading 状态显示 -->
  <view class="book-container-empty" wx:if="{{loading}}">
    <view class="book-container-empty-text">大纲正在生成中，请稍等...</view>
  </view>

  <!-- 非 Loading 状态下的内容显示 -->
  <block wx:else>
    <!-- 有内容时显示 -->
    <view class="book-container-content" wx:if="{{bookContent.length > 0}}">
      <view class="book-container-content-item" wx:for="{{bookContent}}" wx:key="index">
        <view class="book-container-content-item-title">
          第{{index + 1}}章 {{item.title || item.name || '未命名章节'}}
        </view>
        <view class="book-container-content-item-content">
          <!-- 如果有子章节，显示子章节 -->
          
          <view wx:if="{{item.subchapters && item.subchapters.length > 0}}" class="book-container-content-item-content-item" wx:for="{{item.subchapters}}" wx:for-item="subchapter" wx:for-index="subIndex" wx:key="subIndex" data-subchapters-obj="{{subchapter}}" data-chapter-id="{{item.chapter_id}}" data-chapter-title="{{item.title}}" bind:tap="handleJumpToChapter">
           <van-icon name="flower-o" color="{{subchapter.content_text?'pink':''}}" />
            第{{subIndex + 1}}节 {{subchapter.subtitle || subchapter.name || '未命名节'}}
          </view>
        </view>
      </view>
      <view class="book-container-update">
        <image class="book-container-update-img" src="{{updateImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/update.png'}}" mode="aspectFit" />
        <text class="update-text" bind:tap="handleUpdate">更新</text>
      </view>
    </view>

    <!-- 无内容时显示 -->
    <view class="book-container-empty" wx:else>
      <view class="book-container-empty-text">暂无书籍内容</view>
      <view class="book-container-empty-desc">请先完成采访并生成大纲</view>
    </view>
  </block>
</view>