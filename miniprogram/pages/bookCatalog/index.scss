.book-container {
  background-color: #F7F7F7;
  background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/catalog.png');
  background-size: 100% 100%;
  background-position: center;
  padding: 20rpx;
  height: calc(100vh - 40rpx);

  &-catalogtitle{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .catalog-item{
      display: flex;
      align-items: center;
      image{
        width: 32rpx;
        height: 32rpx;
        margin: 0 10rpx;
      }
    }
    image{
      width: 40rpx;
      height: 40rpx;
      margin: 0 30rpx;
    }
    .title{
      color: #C8C7C6;
      font-size: 40rpx;
      font-weight: 500;
    }
    .update{
      color: #535150;
      font-size: 28rpx;
      margin-left: 20rpx;
      background-color: #fff;
      padding: 2rpx 20rpx;
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      image{
        width: 32rpx;
        height: 32rpx;
        margin: 0 5rpx 0 0;
      }
    }
  }
  &-header {
    text-align: center;
    margin-bottom: 40rpx;

    &-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    &-subtitle {
      font-size: 28rpx;
      color: #666;
    }
  }

  &-content {
    height:760rpx;
    overflow-y: auto;
    &-item {
      margin-bottom: 20rpx;
      // background: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      // box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

      &-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #282624CC;
        margin-bottom: 20rpx;
        // border-left: 6rpx solid #007aff;
        padding-left: 20rpx;
      }

      &-content {
        &-item {
          font-size: 28rpx;
          color: #282624CC;
          margin-bottom: 15rpx;
          padding: 10rpx 20rpx 20rpx 40rpx;
          // background: #f8f9fa;
          border-radius: 8rpx;
        
          // text-indent: 2em;
          &:last-child {
            margin-bottom: 0;
          }
        }

        &-text {
          font-size: 28rpx;
          color: #666;
          line-height: 1.6;
          padding: 20rpx;
          background: #f8f9fa;
          border-radius: 8rpx;
        }
      }
    }
  }
  .book-container-update{
    padding-left: 50rpx;
    margin-top: -30rpx;
    display: flex;
    align-items: center;
    .book-container-update-img{
      width: 32rpx;
      height: 32rpx;
    }
    .update-text{
      font-size: 28rpx;
      font-weight: 500;
      color: #F5A630;
      margin-left: 8rpx;
    }
  }

  &-empty {
    text-align: center;
    padding: 100rpx 40rpx;

    &-text {
      font-size: 32rpx;
      color: #999;
      margin-bottom: 20rpx;
    }

    &-desc {
      font-size: 28rpx;
      color: #ccc;
    }
  }
}