import contentApi from '../../api/modules/data';
import bookService from '../../service/bookServices';
import book from '../../api/modules/book';
import { resolveImageSrc } from '../../utils/image-cache';
Page({
  data: {
    bookContent: [],
    generating_outline_status: null,
    bookCover: '',
    bookUpdateTime: '',
    totalCount: '0',
    bookTitle: '',
    loading: true,
    versionList: [],
    versionId:1,
    iconcatalogImage:'',
    clickImage:'',
    preImage:'',
    nextImage:'',
    updateImage:'',
  },
  async onLoad() {
    this.initCachedImages();
    this.getBook();
    this.registerWebSocketListener();
    this.getBookVersions();
  },

  onShow() {
    // 页面显示时刷新数据
    this.getBook();
  },

  onUnload() {
    // 页面卸载时移除 WebSocket 监听器
    try {
      const app = getApp();
      if (app && app.API && app.API.websocket) {
        app.API.websocket.removeMessageListener('question-detail-listener');
        console.log('已移除章节生成消息监听器');
      }
    } catch (error) {
      console.error('移除WebSocket监听器失败:', error);
    }
  },
  async initCachedImages() {
    const iconcatalogImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/iconcatalog.png';
    const clickImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Click.png';
    const preImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/pre.png';
    const nextImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/next.png';
    const updateImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/update.png';
    const iconcatalogImagelocal = await resolveImageSrc(iconcatalogImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    const clickImagelocal = await resolveImageSrc(clickImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    const preImagelocal = await resolveImageSrc(preImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    const nextImagelocal = await resolveImageSrc(nextImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    const updateImagelocal = await resolveImageSrc(updateImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    this.setData({
      iconcatalogImage:iconcatalogImagelocal,
      clickImage:clickImagelocal,
      preImageImage:preImagelocal,
      nextImage:nextImagelocal,
      updateImage:updateImagelocal
    })
  },
  /**
   * 生成回忆录大纲
   */
  async getGenerateOutline() {
    wx.showModal({
      content: '您有3次免费生成大纲的机会，确认要重写吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            if (this.data.versionList.length >= 3){
              wx.showToast({
                title: '您已经使用了3次生成大纲机会，请购买更多版本，开启无限制生成回忆录模式',
                icon: 'none',
                duration: 4000,
              });
              return;
            }
            this.setData({ isGeneratingOutline: true });
            const response = await bookService.generateOutline();
            if (response && response.code === 0) {
              wx.showToast({
                title: '开始生成回忆录',
                icon: 'none',
                duration: 2000,
              });
            } else {
              throw new Error(response?.message || '生成失败');
            }
          } catch (error) {
            console.error('生成大纲失败:', error);
            this.setData({ isGeneratingOutline: false });
            wx.showToast({
              title: '生成失败，请重试',
              icon: 'none',
            });
          }
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      },
    });
    if (this.data.versionList.length >= 3) return;
  },

  /**
   * 注册WebSocket消息监听器
   * 用于监听章节内容生成的流式返回
   */
  registerWebSocketListener() {
    try {
      const app = getApp();
      if (!app || !app.API || !app.API.websocket) {
        console.error('WebSocket API未初始化');
        return;
      }

      // 注册消息监听器
      app.API.websocket.registerMessageListener('question-detail-listener', (message: any) => {
        this.handleWebSocketMessage(message);
      });

      console.log('已注册章节生成消息监听器');
    } catch (error) {
      console.error('注册WebSocket监听器失败:', error);
    }
  },
  /**
   * 处理WebSocket消息
   * @param {Object} message - 收到的消息对象
   */
  handleWebSocketMessage(message: any) {
    try {
      // 解析消息
      if (typeof message === 'string') {
        try {
          message = JSON.parse(message);
        } catch {
          // 忽略非JSON消息
          return;
        }
      }
      const { event, status } = message;
      console.log('收到大纲生成完成消息，关闭loading状态', event, status);
      if (event === 'outline_update' && status === 'no_new_content') {
        wx.showModal({
          content: '当前已经是最新版本，如需更新请先完成剩余话题的回答哦～',
          showCancel: false,
          confirmText: '知道了',
        });
      }
      if (event === 'send_outline' && status === 'success') {
        this.setData({ loading: false });
        // 收到成功消息后，重新获取书籍内容
        this.getBook();
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error);
    }
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.getBook().finally(() => {
      wx.stopPullDownRefresh();
    });
  },
  // 获取书籍的版本
  async getBookVersions() {
    const res = await contentApi.getBookVersion();
    if (!res) return;
    this.setData({ versionList: res.versions });
  },

  // 切换到上一个版本
  previousVersion() {
    const { versionList } = this.data;
    if (!versionList || versionList.length === 0) return;
    const currentVersionIndex = versionList.findIndex((v) => v.is_current);
    if (currentVersionIndex > 0) {
      const previousVersion = versionList[currentVersionIndex - 1];
      // 更新版本标记
      const updatedVersions = versionList.map((v, i) => ({
        ...v,
        is_current: i === currentVersionIndex - 1,
      }));
      this.setData({ versionList: updatedVersions,versionId:previousVersion.version_number });
      console.log('previousVersion', previousVersion);
      this.loadVersion(previousVersion.version_number);
    } else {
      wx.showToast({
        title: '已经是第一个版本',
        icon: 'none',
      });
    }
  },

  // 切换到下一个版本
  nextVersion() {
    const { versionList } = this.data;
    if (!versionList || versionList.length === 0) return;
    const currentVersionIndex = versionList.findIndex((v) => v.is_current);
    if (currentVersionIndex < versionList.length - 1) {
      const nextVersion: any = versionList[currentVersionIndex + 1];
      // 更新版本标记
      const updatedVersions = versionList.map((v, i) => ({
        ...v,
        is_current: i === currentVersionIndex + 1,
      }));
      this.setData({ versionList: updatedVersions,versionId:nextVersion.version_number });
      console.log('nextVersion', nextVersion);
      this.loadVersion(nextVersion.version_number);
    } else {
      wx.showToast({
        title: '已经是最后一个版本',
        icon: 'none',
      });
    }
  },

  // 加载指定版本的书籍内容
  async loadVersion(versionId: number) {
    try {
      this.setData({ loading: true, });
      const res = await contentApi.getBookByVersion(versionId);
      if (!res) return;
      this.setData({
        bookContent: res.messages || [],
        bookCover: res.book_cover,
        bookTitle: res.title,
        loading: false,
      });
    } catch (error) {
      console.error('加载版本失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    }
  },

  //获取书籍内容
  async getBook() {
    try {
      const res = await contentApi.getBook('');
      console.log('bookcontent API响应:', res);

      if (!res) {
        console.log('API返回空数据');
        return;
      }
      // 尝试多种可能的数据结构
      let bookContent = [];

      if (res.chapters && Array.isArray(res.chapters)) {
        bookContent = res.chapters;
      } else if (res.messages && Array.isArray(res.messages)) {
        bookContent = res.messages;
      } else if (res.outline && Array.isArray(res.outline)) {
        bookContent = res.outline;
      } else if (res.data && Array.isArray(res.data)) {
        bookContent = res.data;
      }

      console.log('最终使用的bookContent:', bookContent, this.formatDate(res.created_at));
      // 设置数据，不管状态如何都尝试显示
      const formattedTime = this.formatDate(res.created_at);

      // 如果有书籍内容，则关闭 loading 状态
      const shouldStopLoading = bookContent.length > 0;

      this.setData({
        bookContent: bookContent,
        generating_outline_status: res.generating_outline_status,
        bookCover: res.book_cover,
        bookUpdateTime: formattedTime || '暂无更新时间',
        bookTitle: res.title,
        totalCount: this.formatNumberToTenThousands(res.total_text_count),
        loading: shouldStopLoading ? false : this.data.loading, // 有内容时关闭loading，无内容时保持原状态
        versionId:res.version_info.version_number
      });

      // 如果没有数据，显示提示
      if (bookContent.length === 0) {
        wx.showToast({
          title: '暂无书籍内容',
          icon: 'none',
        });
      }
    } catch (error) {
      console.error('获取书籍内容失败:', error);
      wx.showToast({
        title: '获取内容失败',
        icon: 'none',
      });
    }
  },

  formatNumberToTenThousands(num: number, decimalPlaces: number = 2): string {
    if (num < 10000) {
      return num.toString();
    } else {
      const numInTenThousands = num / 10000;
      return `${numInTenThousands.toFixed(decimalPlaces)}万`;
    }
  },

  // 格式化日期
  formatDate(dateInput: any): string {
    if (!dateInput) return '';

    try {
      let date: Date;

      // 如果已经是 Date 对象
      if (dateInput instanceof Date) {
        date = dateInput;
      }
      // 如果是字符串，尝试解析
      else if (typeof dateInput === 'string') {
        date = new Date(dateInput);
      }
      // 如果是数字（时间戳）
      else if (typeof dateInput === 'number') {
        date = new Date(dateInput);
      }
      // 如果有 format 方法（可能是 moment 对象或类似的）
      else if (dateInput && typeof dateInput.format === 'function') {
        return dateInput.format('YYYY.MM.DD');
      }
      // 其他情况，尝试直接转换
      else {
        date = new Date(dateInput);
      }

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateInput);
        return '';
      }

      // 格式化为 YYYY.MM.DD
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}.${month}.${day}`;
    } catch (error) {
      console.error('日期格式化失败:', error, dateInput);
      return '';
    }
  },

  async handleJumpToChapter(e: any) {
    // 从事件对象中获取 data- 属性
    const dataset = e.currentTarget.dataset;
    const subchapterObj = dataset.subchaptersObj;
    const chapterId = dataset.chapterId;
    const chapterTitle = dataset.chapterTitle;
    console.log('subchapterObj', subchapterObj,this.data.versionId);

    wx.navigateTo({
      url: `/pages/bookContent/index?text=${subchapterObj.content_text}&chapterId=${chapterId}&subchapterId=${subchapterObj?.subchapter_id}&referenceId=${subchapterObj?.reference_id}&subtitle=${subchapterObj.subtitle}&chapterTitle=${chapterTitle}&versionId=${this.data.versionId}`,
    });
  },

  async handleUpdate() {
    try {
      // 开始更新时显示 loading
      this.setData({ loading: true });
      const params = {
        version_number:this.data.versionId
      }
      const response = await book.updateOutlineByVersion(params);
      if(!response) return;
      console.log('handleUpdate',params);
      wx.showToast({
        title: '更新已提交',
        icon: 'none',
      });

      // 重新获取书籍内容
      this.loadVersion(this.data.versionId);
    } catch (error) {
      console.error('更新大纲失败:', error);
      // 更新失败时关闭 loading
      this.setData({ loading: false });
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none',
      });
    }
  },
});
