import contentApi from '../../api/modules/data';
import { removeDuplicateContent } from '../../utils/util';
Page({
  data: {
    steps: [
      {
        title: '通用问题',
        desc: '使用自定义图片作为图标',
        customIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_grey.png',
        customActiveIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_1.png',
      },
      {
        title: '自定义图标步骤',
        desc: '使用自定义图片作为图标',
        customIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_grey.png',
        customActiveIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_1.png',
      },
      {
        title: '自定义图标步骤',
        desc: '使用自定义图片作为图标',
        customIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_grey.png',
        customActiveIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_1.png',
      },
      {
        title: '自定义图标步骤',
        desc: '使用自定义图片作为图标',
        customIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_grey.png',
        customActiveIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_1.png',
      },
    ],
    active: 1,
    shouldShow: false, // 控制页面是否应该显示
    hasUpdate: false, // 标记是否有更新
    data: {}, // 完成原因
    currentPeriod: {},
    chatOverView: {},
    currentIndex: 0,
  },

  /**
   * 页面加载时初始化
   */
  onLoad(options: any) {
    // 检查是否强制显示
    if (options && options.force === 'true') {
      this.setData({
        hasUpdate: true,
        shouldShow: true,
      });
      const periodsArray = wx.getStorageSync('periodsArray');
      const chatOverView = wx.getStorageSync('chatOverView');
      this.setData({ chatOverView });
      // 如果有原因参数，显示相应的完成信息
      if (options.data) {
        const newArr = periodsArray
          .map((item: any) =>
            item.topics.map((topic: any) => {
              return {
                title: topic.topic_name,
                desc: topic.description,
                period_id: topic.topic_id,
                customIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_grey.png',
                customActiveIcon: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_1.png',
              };
            })
          )
          .flat();
        const data = JSON.parse(options.data);
        const currentPeriod = newArr.find((item: { period_id: any }) => {
          return item.period_id === data.current_period;
        });
        const currentIndex = newArr.findIndex((item: { period_id: any }) => {
          return item.period_id === data.current_period;
        });

        // 检查是否找到了当前章节
        if (currentIndex === -1) {
          console.error('未找到当前章节:', data.current_period);
          console.log(
            '可用章节:',
            newArr.map((item: any) => item.period_id)
          );
        }

        console.log('periodsArray', newArr, data, 'currentPeriod', currentPeriod, 'currentIndex', currentIndex);

        this.setData({ data, currentPeriod, steps: newArr, currentIndex });
      }
    }
    this.initTimelineNodes();
  },

  /**
   * 页面显示时检查是否应该显示
   */
  onShow() {
    // 如果没有更新且不是强制显示，则隐藏页面（返回上一页）
    if (!this.data.hasUpdate && !this.data.shouldShow) {
      // 延迟执行，避免页面闪烁
      setTimeout(() => {
        wx.navigateBack({
          delta: 1,
          fail: () => {
            // 如果返回失败，显示页面内容
            this.setData({ shouldShow: true });
          },
        });
      }, 100);
      return;
    }

    this.setData({ shouldShow: true });
  },

  /**
   * 页面隐藏时清理状态
   */
  onHide() {
    this.setData({
      shouldShow: false,
      hasUpdate: false,
    });
  },

  gotoDetail() {
    this.setData({ hasUpdate: true });

    // 检查当前索引是否有效
    if (this.data.currentIndex === -1) {
      console.error('当前章节索引无效');
      wx.showToast({
        title: '当前章节数据异常',
        icon: 'none',
      });
      return;
    }

    // 计算下一个章节的索引
    const nextIndex = this.data.currentIndex + 1;

    // 检查是否超出数组范围
    if (nextIndex >= this.data.steps.length) {
      console.warn('已经是最后一个章节，无法进入下一章节');
      wx.showToast({
        title: '已经是最后一个章节',
        icon: 'none',
      });
      return;
    }

    const nextStep = this.data.steps[nextIndex] as any;

    // 检查下一个步骤是否有 period_id
    if (!nextStep.period_id) {
      console.error('下一个章节缺少 period_id');
      wx.showToast({
        title: '章节数据异常',
        icon: 'none',
      });
      return;
    }

    const data = { topic_id: nextStep.period_id };
    console.log('进入下一章节:', {
      currentIndex: this.data.currentIndex,
      nextIndex: nextIndex,
      data: data,
      totalSteps: this.data.steps.length,
    });

    wx.navigateTo({
      url: '/pages/questionDetail/index?question=' + JSON.stringify(data),
    });
  },
  gotoInterView(){
    wx.switchTab({
      url: '/pages/interview/index'
    });
  },

  async initTimelineNodes() {
    // 调用getChatOverView接口获取数据
    const response = await contentApi.getChatOverViewV2();
    // 提取content数据
    const contentData = response.storyList
      .map((story: { content: any[] }) => story.content.map((item) => item.title))
      .flat();
    const contentCodeData = response.storyList
      .map((story: { content: any[] }) => story.content.map((item) => item.topic_id))
      .flat();

    // 去重处理：根据title去重
    const uniqueContent = removeDuplicateContent(contentData);
    const uniqueContentCode = removeDuplicateContent(contentCodeData);
    // 把overview的信息存到storage里面
    const lastTitle = uniqueContent[uniqueContent.length - 1];
    wx.setStorageSync('chatOverView', {
      titleList: uniqueContent,
      titleCodeList: uniqueContentCode,
      title: lastTitle,
      basicProgress: parseFloat(((uniqueContent.length / 21) * 100).toFixed()),
    });
  },
});
