.complete-page{
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100vh;
  .complete-page-bg{
    position: absolute;
    width: 726rpx;
    height: 896rpx;
    top: 0;
    left: 80rpx;
  }
  .complete-page-flag{
    position: absolute;
    top: 350rpx;
    width: 100%;
    image{
      width: 650rpx;
      height: 560rpx;
      position: absolute;
      right: 0;
      left: 0;
      margin: auto;
    }
    .complete-page-flag-title{
      font-size: 56rpx;
      font-weight: 600;
      text-align: center;
      position: absolute;
      top: 70rpx;
      right: 0;
      left: 0;
      margin: auto;      
    }
    .complete-page-flag-char{
      position: absolute;
      text-align: center;
      top: 200rpx;
      right: 0;
      left: 0;
      margin: auto;      
      width: 148rpx;
      height: 66rpx;
      background-color: #F7F6F5;
      border: 1rpx solid #f4a914;
      color: #f4a914;
      background: #fff7e6;
      border-radius: 40rpx;
      line-height: 66rpx;
      font-size: 32rpx;
      font-weight: 500;
    }
    .complete-page-flag-pro{
      position: absolute;
      text-align: center;
      top: 290rpx;
      right: 0;
      left: 0;
      margin: auto;   
      font-size: 56rpx; 
      font-weight: 600;
      color: #7A7A7D99;
    }
  }
  .complete-page-button{
    position: absolute;
    bottom: 140rpx;
    display: flex;
    justify-content: space-around;
    width: 100%;
    .complete-page-button-back{
      width: 300rpx;
      height: 80rpx;
      background-color:#FFFFFF ;
      border: 1rpx solid #f4a914;
      border-radius: 16rpx;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 40rpx #F5A630;
    }
    .next-btn-wrap {
      position: relative;
      display: flex;
      justify-content: center;
    }
    .next-btn {
      width: 260rpx;
      height: 80rpx;
      background: #fff;
      border-radius: 16rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #282624;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2rpx 8rpx 0 rgba(255, 153, 0, 0.08);
      border: 2rpx solid #FFD591;
      position: relative;
      z-index: 2;
    }
    .next-btn-bottom {
      position: absolute;
      left: 0; right: 0; top: 28rpx;
      height: 60rpx;
      border-radius: 0 0 16rpx 16rpx;
      background: linear-gradient(90deg, #FFD591 0%, #FF9900 100%);
      z-index: 1;
      pointer-events: none;
    }
  }
}
