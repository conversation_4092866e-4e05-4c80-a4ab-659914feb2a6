<wxs module="filter">
  function exChangeNum(n) {
    return Number(n).toLocaleString('zh-Hans-CN-u-nu-hanidec');
  };
  module.exports.exChangeNum = exChangeNum;
</wxs>
<view class="interview-container">
  <!-- 顶部下拉面板 -->
  <view class="top-panel" style="transform: translateY({{offsetY}}%); transition: {{transition}}">
    <view class="panel-content"></view>

  </view>
  <view class="main-page" catchtouchstart="onTouchStart" catchtouchmove="onTouchMove" catchtouchend="onTouchEnd">
    <!-- 头部区域 -->
    <view class="interview-header">
      <view class="interview-header-nav">
        <image class="interview-header-icon" src="{{cachedHeaderIcon || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/listicon.png'}}" mode="aspectFit" bindtap="onTopicListIconTap" />
        <text class="interview-header-text">{{titleList[currentIndex]}}</text>
        <image class="interview-header-arrow" src="{{checkIconImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/check-icon.png'}}" mode="aspectFit" bindtap="onTopicListIconTap" />
      </view>

      <TimeQuoteCard theme="{{quoteTheme}}" custom-quotes="{{customQuotes}}" bind:cardtap="onQuoteCardTap" bind:refresh="onQuoteRefresh" />
    </view>

    <!-- Skyline增强的Swiper组件 -->
    <swiper id="interview-swiper" class="interview-swiper" style="{{swiperStyle}}" layout-type="stackRight" circular="{{true}}" bindchange="onSwiperChange" indicator-dots="{{false}}" autoplay="{{false}}" interval="3000" duration="500">
      <swiper-item class="swiper-item" wx:for="{{periodsArray}}" wx:for-item="item" wx:key="period_id" data-index="{{index}}">
        <view class="card-content">
          <view class="card-header">
            <view class="card-title">第{{filter.exChangeNum(index + 1)}}章 · {{item.name}}</view>
          </view>
          <view class="card-body" wx:for="{{item.topics}}" wx:for-item="topic" wx:for-index="topicIndex" data-item="{{topic}}" bindtap="onCardTap" data-index="{{topicIndex}}" data-periodid="{{item.period_id}}">
            <view wx:if="{{topicIndex < item.topics.length - 1}}" class="card-item-line" style="background-image:url({{topic.status === 'completed'? 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Rectangle (1).png' :'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Line.png'}})">
            </view>

            <view class="card-item">
              <image wx:if="{{topic.status === 'in_progress'}}" class="card-icon-img" src="{{inProgressIcon || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_1.png'}}" mode="aspectFit" />
              <image wx:else class="card-icon-img" src="{{ellipseGrey || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_grey.png'}}" mode="aspectFit" />
              <text wx:if="{{topic.status === 'in_progress'}}" class="card-text inprogress">{{topic.topic_name}}</text>
              <text wx:elif="{{topic.status === 'not_started'}}" class="card-text notstarted">{{topic.topic_name}}</text>
              <text wx:else class="card-text">{{topic.topic_name}}</text>
            </view>


            <icon wx:if="{{topic.status === 'completed'}}" class="icon-box-img" type="success" size="14"></icon>
            <view class="icon-box-text" wx:elif="{{topic.status === 'in_progress'}}">
              正在回答中
            </view>
            <image wx:else class="icon-box-img" mode="aspectFit" src="{{vectorImage || 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Vector.png'}}" />
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 用户指引组件 -->
    <UserGuide id="user-guide" visible="{{showUserGuide}}" bind:guideComplete="onGuideComplete" />

  </view>
</view>