import chatApi from '../../api/modules/chat';
import contentApi from '../../api/modules/data';
import { convertPeriodsToArray, PeriodInfo, countTopicIdOccurrences } from '../../utils/util';
import { checkLoginAndInitialize, initUserData } from '../../utils/init';
import { authService } from '../../service/auth/index';
import { resolveImageSrc } from '../../utils/image-cache';

Page({
  data: {
    periodsArray: [] as PeriodInfo[],
    currentIndex: 0,
    swiperStyle: '', // 动态swiper样式
    isFirstLoad: true, // 标记是否首次加载
    isFirstLogin: true,
    customQuotes: [], // 自定义语句库
    showUserGuide: false, // 是否显示用户指引
    titleList:['第一章 · 基本信息','第二章 · 童年时光', '第三章 · 青春年华','第四章 · 成年立业','第五章 · 晚年时光','第六章 · 通用话题'],
    cachedHeaderIcon:'',
    inProgressIcon:'',
    ellipseGrey:'',
    rectangleImage:'',
    lineImage:'',
    vectorImage:'',
    checkIconImage:'',
    timeInfo:{},
    offsetY: -100,     // 初始隐藏 (-100%)
    transition: 'none',
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0, // 当前 tab 的索引，0/1/2
      });
    }

    // 如果不是首次加载，说明是从其他页面返回，需要刷新数据
    if (!this.data.isFirstLoad) {
      console.log('从其他页面返回，刷新数据');
      this.refreshData();
    }
  },

  async onLoad() {
    this.initCachedImages();
    this.checkLoginStatus();
    this.adaptSwiperSize();
    this.getTopicsList();
    this.initTimelineNodes();
    this.getIsFirstLogin();
    this.checkUserGuide();

    // 标记首次加载完成
    this.setData({
      isFirstLoad: false,
    });
  },

  // 图片缓存
  async initCachedImages() {
    try {
      const cachedHeader = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/listicon.png';
      const inProgressIcon = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_1.png';
      const ellipseGrey = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Ellipse_grey.png';
      const vectorImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Vector.png';
      const checkIconImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/check-icon.png';
      
      const cachedHeaderIconlocal = await resolveImageSrc(cachedHeader,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
      const inProgressIconlocal = await resolveImageSrc(inProgressIcon,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
      const ellipseGreylocal = await resolveImageSrc(ellipseGrey,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
      const vectorImagelocal = await resolveImageSrc(vectorImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
      const checkIconImagelocal = await resolveImageSrc(checkIconImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
      this.setData({cachedHeaderIcon:cachedHeaderIconlocal,inProgressIcon:inProgressIconlocal,ellipseGrey:ellipseGreylocal,vectorImage:vectorImagelocal,checkIconImage:checkIconImagelocal});
    } catch (e) {
      console.error('initCachedImages', e);
    }
  },
    /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '我在「时光笔迹」写回忆录…',
      path: '/pages/interview/index',
      imageUrl: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/share.png' // 可以设置分享图片
    };
  },

    /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '我在「时光笔迹」写回忆录…',
      query: '/pages/interview/index',
      imageUrl: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/share.png' // 可以设置分享图片
    };
  },



  onTopicListIconTap(){
    wx.navigateTo({
      url: '/pages/interviewList/index',
    });
  },

  // 页面查找swiper位置并传递给UserGuide组件，首次登录时展示指引
  getSwiperRectAndShowGuide() {
    const query = wx.createSelectorQuery();
    query.select('#interview-swiper').boundingClientRect();
    query.exec(res => {
      if (res && res[0]) {
        const swiperRect = res[0];
        // 传递给 UserGuide 组件
        const userGuideComponent = this.selectComponent('#user-guide');
        if (userGuideComponent) {
          userGuideComponent.setGuideRect(swiperRect);
        }
        this.setData({ showUserGuide: true });
      }
    });
  },

  // 检查是否需要显示用户指引
  checkUserGuide() {
    try {
      const userGuideCompleted = wx.getStorageSync('userGuideCompleted');
      if (!userGuideCompleted) {
        // 延迟显示指引，确保页面完全加载
        setTimeout(() => {
          this.getSwiperRectAndShowGuide();
        }, 1000);
      }
    } catch (e) {
      console.error('检查用户指引状态失败', e);
    }
  },

  // 用户指引完成事件
  onGuideComplete() {
    this.setData({
      showUserGuide: false,
    });
    console.log('用户指引已完成');
  },

  async checkLoginStatus() {
    const isLoggedIn = await checkLoginAndInitialize();
    console.log('isLoggedIn22', isLoggedIn);
    if (!isLoggedIn) {
      try {
        const loginResult: any = await authService.startLogin();
        if (loginResult.success) {
          this.setData({
            hasUserInfo: true,
            pendingLoginCode: '',
            loadingText: '获取数据中...',
          });

          // 初始化用户数据（获取用户信息和会话列表）
          try {
            const initResult = await initUserData();
            if (!initResult.success) {
              console.warn('初始化用户数据失败:', initResult.error);
              wx.showToast({
                title: '数据初始化失败，部分功能可能不可用',
                icon: 'none',
                duration: 2000,
              });
            } else {
              console.log('用户数据初始化成功');
            }
          } catch (initError) {
            console.error('初始化用户数据出错:', initError);
          }

          // 登录成功，重定向到回忆录页面
          wx.reLaunch({
            url: '/pages/interview/index',
          });
        } else if (loginResult.needUserAuth && loginResult.code) {
          this.setData({
            pendingLoginCode: loginResult.code,
          });
          // 页面展示登录按钮，等待用户点击授权
        } else {
          // 其他登录失败
          if (loginResult.error) {
            wx.showToast({
              title: loginResult.error,
              icon: 'none',
            });
          }
        }
      } catch (err) {
        console.error('登录失败', err);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none',
        });
      }
    }
  },

  getIsFirstLogin() {
    const isFirstLogin = wx.getStorageSync('hasLoggedInBefore');
    this.setData({ isFirstLogin });
    console.log('getIsFirstLogin', isFirstLogin);
  },
  // 刷新数据方法
  async refreshData() {
    try {
      // 并行执行数据刷新
      await Promise.all([
        this.getTopicsList().catch((error) => {
          console.error('刷新话题列表失败:', error);
          return Promise.resolve(); // 继续执行其他任务
        }),
        this.initTimelineNodes().catch((error) => {
          console.error('刷新时间轴节点失败:', error);
          return Promise.resolve(); // 继续执行其他任务
        }),
      ]);

      wx.hideLoading();
      console.log('数据刷新完成');
    } catch (error) {
      wx.hideLoading();
      console.error('数据刷新失败:', error);
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 2000,
      });
    }
  },

  // 根据设备信息适配swiper尺寸
  adaptSwiperSize() {
    try {
      const systemInfo = wx.getDeviceInfo();
      const windowInfo = wx.getWindowInfo();
      const { model, brand } = systemInfo;
      const { screenWidth, screenHeight } = windowInfo;

      console.log('设备信息:', { screenWidth, screenHeight, model, brand });

      let swiperStyle = '';

      // 华为Mate系列等大屏设备特殊适配
      const isHuaweiMate = model?.toLowerCase().includes('mate') || brand?.toLowerCase().includes('huawei');
      const isLargeScreen = screenWidth > 400 || screenHeight > 800;

      if (isHuaweiMate || isLargeScreen) {
        // 大屏设备使用更大的尺寸
        swiperStyle = `
                    width: 82vw !important;
                    height: 50vh !important;
                    max-width: 750rpx !important;
                    max-height: 850rpx !important;
                `;
        console.log('检测到大屏设备，应用大屏适配');
      } else if (screenWidth < 375) {
        // 小屏设备使用较小的尺寸
        swiperStyle = `
                    width: 88vw !important;
                    height: 54vh !important;
                    max-width: 600rpx !important;
                    max-height: 650rpx !important;
                    margin-left: 60rpx !important;
                `;
        console.log('检测到小屏设备，应用小屏适配');
      }

      this.setData({
        swiperStyle,
      });
    } catch (error) {
      console.error('设备适配失败:', error);
    }
  },

  // 下拉刷新处理
  onPullDownRefresh() {
    console.log('用户下拉刷新');
    this.refreshData().finally(() => {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    });
  },

  async getTopicsList() {
    const res = await chatApi.getTopics();
    if (res.success) {
      const periodsArray = convertPeriodsToArray(res.data.periods);
      this.setData({
        periodsArray,
      });
      const allTopicIdsList = periodsArray.map((item: any) => item.topics.map((topic: any) => topic.topic_id)).flat();
      const allTopicNameList = periodsArray
        .map((item: any) => item.topics.map((topic: any) => topic.topic_name))
        .flat();
      wx.setStorageSync('allTopicIdsList', allTopicIdsList);
      wx.setStorageSync('allTopicNameList', allTopicNameList);
      wx.setStorageSync('periodsArray', periodsArray);
    }
  },

  // 初始化时间轴节点数据
  async initTimelineNodes() {
    // 调用getChatOverView接口获取数据
    const response = await contentApi.getChatOverViewV2();
    console.log('getChatOverView response111:', response);

    const { topicIdCounts, titleCounts } = countTopicIdOccurrences(response.storyList);

    // 筛选符合条件的topic_id
    const filteredTopicIds: string[] = [];
    const filteredTitles: string[] = [];
    let firstUnqualifiedKey: string | null = null;
    let firstUnqualifiedTitleKey: string | null = null;

    topicIdCounts.forEach((item: { [key: string]: number }) => {
      const key = Object.keys(item)[0];
      const count = item[key];

      if (key === 'basic_1') {
        // basic_1 直接加入
        filteredTopicIds.push(key);
      } else {
        // 其他topic_id，判断是否为10
        if (count >= 10) {
          filteredTopicIds.push(key);
        } else if (firstUnqualifiedKey === null) {
          // 记录第一个不满足条件的key
          firstUnqualifiedKey = key;
        }
      }
    });

    // 如果有不满足条件的，把第一个也加进去
    if (firstUnqualifiedKey && !filteredTopicIds.includes(firstUnqualifiedKey)) {
      filteredTopicIds.push(firstUnqualifiedKey);
    }

    titleCounts.forEach((item: { [key: string]: number }) => {
      const key = Object.keys(item)[0];
      const count = item[key];

      if (key === '个人基础信息') {
        // basic_1 直接加入
        filteredTitles.push(key);
      } else {
        // 其他topic_id，判断是否为10
        if (count >= 10) {
          filteredTitles.push(key);
        } else if (firstUnqualifiedTitleKey === null) {
          // 记录第一个不满足条件的key
          firstUnqualifiedTitleKey = key;
        }
      }
    });

    // 如果有不满足条件的，把第一个也加进去
    if (firstUnqualifiedTitleKey && !filteredTitles.includes(firstUnqualifiedTitleKey)) {
      filteredTitles.push(firstUnqualifiedTitleKey);
    }
    console.log('筛选后的topic_ids:', filteredTopicIds,filteredTitles,topicIdCounts);
    // 把overview的信息存到storage里面
    const lastTitle = filteredTitles[filteredTitles.length - 1];
    wx.setStorageSync('chatOverView', {
      titleList: filteredTitles,
      titleCodeList: filteredTopicIds,
      title: lastTitle,
      basicProgress: parseFloat(((filteredTopicIds.length / 21) * 100).toFixed()),
      storyList:response.storyList
    });
  },

  // Skyline swiper事件处理
  onSwiperChange(e: any) {
    const { current } = e.detail;
    this.setData({
      currentIndex: current,
    });

    // 可以在这里添加切换动画或其他逻辑
    console.log('当前卡片索引:', current);
  },

  // 卡片点击事件
  onCardTap(e: any) {
    const item = e.currentTarget.dataset.item;
    const periodId = e.currentTarget.dataset.periodid;
    const newItem = {
      ...item,
      period_id: periodId,
    }
    if (item.status === 'completed') {
      wx.navigateTo({
        url: '/pages/interviewList/index',
      });
    } else {
      wx.navigateTo({
        url: '/pages/questionDetail/index?question=' + JSON.stringify(newItem),
      });
    }
  },
   // 手指按下
  onTouchStart(e: any) {
    this.startY = e.touches[0].clientY
    this.setData({ transition: 'none' })
  },

  // 手指滑动
  onTouchMove(e: any) {
    const moveY = e.touches[0].clientY - this.startY
    const percent = (moveY / 300) * 100
    if (percent > 0) {
      this.setData({ offsetY: Math.min(percent - 100, 0) })
    }
  },

  // 手指松开
  onTouchEnd(e: any) {
    if (this.data.offsetY > -40) {
       wx.navigateTo({
        url: '/pages/interviewList/index'
      })
      // 回弹复位
      this.setData({ offsetY: -100, transition: 'transform 0.3s' })
    } else {
      this.setData({ offsetY: -100, transition: 'transform 0.3s' })
    }
  },

  // 点击关闭按钮
  hidePanel() {
    this.setData({ offsetY: -100, transition: 'transform 0.3s' })
  }
});
