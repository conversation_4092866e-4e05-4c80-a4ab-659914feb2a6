/* Skyline渲染器优化样式 */
.interview-container {
    height: 100vh;
    // background-color: #FCF8F2;
    background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/background.png');
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: center center;

    /* Skyline性能优化 */
    contain: layout style paint;
    will-change: transform;
    .card-item{
        display: flex;
        align-items: center;
    }
    .card-icon-img{
        width: 24rpx;
        height: 24rpx;
    }
    .icon-box-img{
        margin-right:10rpx;
        width: 32rpx;
        height: 32rpx;
    }
    .icon-box-text{
        font-size: 24rpx;
        color: #F5A630;
    }
    .inprogress {
        color: #F5A630 !important;
    }
    .notstarted{
        color: #28262466 !important;
    }

    .interview-header {
        // height: 400rpx;
        /* 使用Skyline的CSS Grid布局 */
        display: grid;
        place-items: center;
        text-align: center;
        padding:100rpx 40rpx 0rpx 40rpx;
        background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/headerBG.png');
        background-size: 100%;
        background-repeat: no-repeat;
        .interview-header-nav{
            display: flex;
            align-items: center;
            position: relative;
            .interview-header-text{
                font-size: 28rpx;
                color: #282624B2;
                text-align: center;
                position: absolute;
                right: 0;
                left: 0;
                margin: auto;
            }
            .interview-header-icon{
                width: 72rpx;
                height: 72rpx;
                position: relative;
                z-index: 2;
            }
            .interview-header-arrow{
                width: 56rpx;
                height: 40rpx;
                position: absolute;
                right: 0;
                left: 0;
                bottom: -70rpx;
                margin: auto;
            }
        }
        .header-title {
            font-size: 34rpx;
            color: #382A28;
            margin-bottom: 10rpx;
        }
    }

    .interview-swiper {
        width: 85vw;
        height: 60vh;
        max-width: 700rpx;
        max-height: 750rpx;
        min-width: 600rpx;
        min-height: 600rpx;
        margin: auto;
        margin-left: 80rpx;

        /* Skyline swiper优化 */
        will-change: transform;
        contain: layout;

        .swiper-item {
            width: 100%;
            height: 100%;
            border-radius: 40rpx;
            box-shadow: 20rpx 8rpx 8rpx rgba(55, 55, 55, 0.05);
            overflow: hidden;
            cursor: pointer;
            background-color: #fff;
            // background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/bamboo_white_background.webp');
            background-size: cover;
            background-position: center;

            /* Skyline卡片堆叠效果优化 */
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            will-change: transform, opacity;
            contain: layout style paint;

            /* 3D变换优化 */
            transform-style: preserve-3d;
            backface-visibility: hidden;
FcF8F2
            &:hover {
                transform: translateY(-10rpx) scale(1.02);
                box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
            }

            .card-content {
                height: 100%;
                display: flex;
                flex-direction: column;
                padding: 40rpx;
                position: relative;

                .card-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 20rpx;
                    padding-bottom: 20rpx;
                    background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/dashed.png');
                    background-size: 100%;
                    background-repeat: no-repeat;
                    background-position: bottom;


                    .card-title {
                        font-size: 32rpx;
                        color: #282624;
                        font-weight: 500;
                    }
                }
                .card-item-line{
                    width: 4rpx;
                    height: 96rpx;
                    background-image: url('https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Rectangle (1).png');
                    background-size: 4rpx 100%;
                    background-repeat: no-repeat;
                    // background-position: center center;
                    position: absolute;
                    top: 37rpx;
                    left: 10rpx;
                }

                .card-body {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-top: 50rpx;
                    position: relative;

                    .card-text {
                        font-size: 30rpx;
                        color: #282624;
                        // text-align: center;
                        line-height: 1.6;
                        border-radius: 20rpx;
                        backdrop-filter: blur(10rpx);
                        margin-left: 40rpx;
                    }
                }

                .card-footer {
                    margin-top: 40rpx;
                    text-align: center;

                    .tap-hint {
                        font-size: 24rpx;
                        color: rgba(255, 255, 255, 0.8);
                        padding: 10rpx 20rpx;
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 15rpx;
                        display: inline-block;
                    }
                }
            }
        }

        /* Skyline堆叠布局特定样式 */
        &[layout-type="stackRight"] .swiper-item {
            transform-origin: left center;
        }
    }
    .guid-img{
        width: 150rpx;
        height: 150rpx;
        position: absolute;
        right: 50rpx;
        bottom: 200rpx;
    }

    /* 操作按钮区域 */
    .action-buttons {
        padding: 40rpx;
        display: flex;
        gap: 20rpx;
        justify-content: center;

        .action-btn {
            flex: 1;
            max-width: 200rpx;
            height: 80rpx;
            border-radius: 40rpx;
            font-size: 28rpx;
            font-weight: 500;
            border: none;
            transition: all 0.3s ease;

            &.primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);

                &:active {
                    transform: translateY(2rpx);
                    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
                }
            }

            &.secondary {
                background: rgba(255, 255, 255, 0.9);
                color: #333;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

                &:active {
                    transform: translateY(2rpx);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                }
            }
        }
    }
}

/* Skyline特有的CSS变量支持 */
:root {
    --swiper-card-scale: 0.95;
    --swiper-card-opacity: 0.8;
    --animation-duration: 0.3s;
    --swiper-width: 85vw;
    --swiper-height: 60vh;
    --swiper-margin-left: 80rpx;
}

/* 响应式设计 - Skyline支持更好的媒体查询 */
/* 小屏设备 */
@media (max-width: 750rpx) {
    .interview-swiper {
        width: 90vw;
        height: 58vh;
        min-width: 500rpx;
        min-height: 550rpx;
        margin-left: 40rpx;
    }
}

/* 大屏设备 (华为Mate70等) */
@media (min-width: 751rpx) and (min-height: 1600rpx) {
    .interview-swiper {
        width: 80vw;
        height: 55vh;
        max-width: 800rpx;
        max-height: 800rpx;
        margin-left: 100rpx;
    }
}

/* 超大屏设备 */
@media (min-width: 900rpx) {
    .interview-swiper {
        width: 75vw;
        height: 50vh;
        max-width: 900rpx;
        max-height: 850rpx;
        margin-left: 120rpx;
    }
}

/* 华为Mate系列等高分辨率设备 */
@media (min-resolution: 3dppx), (min-device-pixel-ratio: 3) {
    .interview-swiper {
        width: 80vw;
        height: 55vh;
        max-width: 780rpx;
        max-height: 780rpx;

        .swiper-item {
            border-radius: 45rpx;
            box-shadow: 25rpx 10rpx 12rpx rgba(55, 55, 55, 0.08);
        }
    }
}

/* 深色模式支持 - Skyline原生支持 */
@media (prefers-color-scheme: dark) {
    .interview-container {
        background-color: #1a1a1a;
    }

    .swiper-item {
        background-color: #2a2a2a;
        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
    }
}

.top-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fcf8f2;
  z-index: 10;
}

.panel-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 18px;
  .interview-time-info {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    margin: 120rpx 0 60rpx 0;
    padding: 0 50rpx;
    font-size: 28rpx;
    .interview-time-info-yi {
      margin: 10rpx 0;
      display: flex;
      align-items: center;
      font-size: 30rpx;
      width: 400rpx;
      .interview-time-info-yi-title {
        padding: 5rpx 10rpx;
        background-color: #cb7e18;
        color: #fff;
        font-weight: 500;
        border-radius: 8rpx;
        margin-right: 30rpx;
        flex-shrink: 0;
      }
      .interview-time-info-yi-content {
        color: #cb7e18;
      }
    }
  }
}
