.story-container {
  background-color: #f7f7f7;
  min-height: calc(100vh - 40rpx);
  padding-top: 40rpx;
  .selected-info {
    margin: 32rpx 24rpx;
    padding: 24rpx;
    background: white;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
    
    .info-title {
      font-size: 28rpx;
      color: #666;
      margin-right: 16rpx;
    }

    .info-content {
      font-size: 32rpx;
      color: #333;
      font-weight: 600;
    }
  }
  .story-container-book{
    display: flex;
    justify-content: center;
    padding: 70rpx 0 30rpx 0;
    position: relative;
    .share-icon{
      position: absolute;
      right: 150rpx;
      top: 120rpx;
    }
    image{
      width: 550rpx;
      height: 700rpx;
      border-radius: 12rpx;
       box-shadow:
    0 0 10px 2px rgba(43, 42, 42, 0.2), /* 第一层较小的模糊 */
    0 0 20px 5px rgba(0, 0, 0, 0.1); /* 第二层更大的模糊 */
      // margin-left: 20rpx;
    }
    &-title{
      font-size: 40rpx;
      font-weight: 600;
      color: #fff;
      position: absolute;
      top:160rpx
    }
    .story-container-book-share{
      width: 300rpx;
      height: 80rpx;
      border: 2rpx solid #fff;
      background-color: #fff;
      text-align: center;
      line-height: 80rpx;
      border-radius: 40rpx;
      color: #333;
      position: absolute;
      bottom:80rpx;
    }
  }

  .progress-demo-section {
    margin: 32rpx 24rpx;
    padding: 32rpx;
    background: white;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .section-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 32rpx;
      text-align: center;
    }

    .demo-item {
      margin-bottom: 40rpx;

      .demo-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        font-weight: 500;
      }
    }

    .control-buttons {
      display: flex;
      gap: 16rpx;
      margin-top: 40rpx;
      flex-wrap: wrap;

      .control-btn {
        flex: 1;
        min-width: 200rpx;
        height: 80rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 12rpx;
        font-size: 28rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
  .story-container-current{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 500rpx;
    height: 80rpx;
    margin: auto;
    margin-top: 50rpx;
    background-color: #fff;
    border-radius: 40rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
    image{
      width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
    }
    .story-container-current-title{
      font-family: PingFang SC;
      font-weight: 500;
      font-style: Medium;
      font-size: 28rpx;
      color: #282624;
      margin-right: 30rpx;
      width: 350rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-align: center;
    }
  }
}