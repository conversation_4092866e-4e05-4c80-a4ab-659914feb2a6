import contentApi from '../../api/modules/data';
import bookService from '../../service/bookServices';
import book from '../../api/modules/book';
import { resolveImageSrc } from '../../utils/image-cache';
import chatApi from '../../api/modules/chat';
import { countTopicIdOccurrences } from '../../utils/util';
Page({
  data: {
    selectedDate: null as any,
    // 进度条
    basicProgress: 0,
    titleList: [] as any[],
    booksPic: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/3851752820902_.pic.jpg',
    title: '',
    isGeneratingOutline: false,
    isFirstGetBook: true,
    bookName: '',
    uniqueContentCode: [] as any[],
    chatImage:'',
    userProgress:{}
  },
  async onLoad() {
    this.initCachedImages();
    this.getBook();
    this.initTimelineNodes();
    this.getProgress();
  },
  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1,
      });
    }
    this.getBook();
    this.initTimelineNodes();
  },

  async initCachedImages(){
    const chatImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/chat.png';
    const chatImagelocal = await resolveImageSrc(chatImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
    this.setData({
      chatImage:chatImagelocal
    })
  },
    async getProgress(){
      const res = await chatApi.getProgress();
      const periodsArray = wx.getStorageSync('periodsArray')
      const allTopicsObjList = periodsArray.map((period:any) => {
        return period.topics.map((topic:any)=>{
          return {topic_id:topic.topic_id,topic_name:topic.topic_name}
        });
      }).flat();
      const topicTitle = allTopicsObjList.find((item:any)=>item.topic_id === res.user_progress.current_topic).topic_name
      if(!res) return;
      console.log('???',res,allTopicsObjList)
      this.setData({
        userProgress:{
          current_topic:res.user_progress.current_topic,
          completion_rate:Math.floor(res.user_progress.overall_stats.completion_rate*100),
          completed_topics:res.user_progress.overall_stats.active_topics,
          total_topics:res.user_progress.overall_stats.total_topics,
          topicTitle
        }
      })
    },
  // 初始化时间轴节点数据
  async initTimelineNodes() {
    const { titleList, basicProgress, title } = wx.getStorageSync('chatOverView');
    const response = await contentApi.getChatOverViewV2();
    const { topicIdCounts } = countTopicIdOccurrences(response.storyList);
    const canCometoABookList = [] as any[];
    topicIdCounts.forEach((item: { [key: string]: number }) => {
      const key = Object.keys(item)[0];
      const count = item[key];
      if (count >= 3) {
        canCometoABookList.push(key);
      }
    });
    if (canCometoABookList.length < 4 && this.data.isFirstGetBook) {
      const firstBookImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/3851752820902_.pic.jpg'
      const firstBookPiclocal = await resolveImageSrc(firstBookImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
      this.setData({
        booksPic: firstBookPiclocal,
      });
    } else {
      const booksImage = 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/3841752820885_.pic.jpg'
      const booksPiclocal = await resolveImageSrc(booksImage,{ ttlMs: 30 * 24 * 60 * 60 * 1000});
      this.setData({
        booksPic: booksPiclocal,
      });
    }

    this.setData({
      titleList,
      title: title || '个人基础信息',
      basicProgress: basicProgress,
    });
    return canCometoABookList; // 返回处理后的数据
  },
  // 去重处理：根据title去重
  removeDuplicateContent(contentData: any[]): any[] {
    const seen = new Set();
    const uniqueContent = contentData.filter((item) => {
      const title = item || '';
      if (seen.has(title)) {
        return false;
      }
      seen.add(title);
      return true;
    });
    return uniqueContent;
  },
  /**
   * 生成回忆录大纲
   */
  async getGenerateOutline() {
    // 先刷新数据确保获取最新的titleList
    const latestTitleList = await this.initTimelineNodes();
    if (latestTitleList.length < 4) {
      wx.showToast({
        title: `请先完成至少4个话题`,
        icon: 'none',
      });
      return;
    }
    try {
      this.setData({ isGeneratingOutline: true });
      const response = await bookService.generateOutline();
      if (response && response.code === 0) {
        wx.showToast({
          title: '开始生成回忆录',
          icon: 'none',
          duration: 2000,
        });
        wx.navigateTo({
          url: '/pages/bookCatalog/index',
        });
      } else {
        throw new Error(response?.message || '生成失败');
      }
    } catch (error) {
      console.error('生成大纲失败:', error);
      this.setData({ isGeneratingOutline: false });
      wx.showToast({
        title: '生成失败，请重试',
        icon: 'none',
      });
    }
  },

  //获取书籍内容
  async getBook() {
    const res = await contentApi.getBook('');
    if (res && res.generating_outline_status === 0) {
      this.setData({ isFirstGetBook: true });
      // 如果用户没有大纲，先生成大纲
      // this.getGenerateOutline();
    }
    if (res && res.generating_outline_status === 2) {
      this.setData({ isFirstGetBook: false, booksPic: res.book_cover, bookName: res.title });
    }
  },

  /**
   * 处理书本点击事件
   */
  handleBookTap() {
    if (this.data.isFirstGetBook) {
      this.getGenerateOutline();
    } else {
      this.togoBookCatalog();
    }
  },

  togoBookCatalog() {
    wx.navigateTo({
      url: '/pages/bookCatalog/index',
    });
  },
  gotoQuestionDetail() {
    const question = {
      topic_id: (this.data.userProgress as any).current_topic || 'basic_1',
      topic_name: (this.data.userProgress as any).topicTitle || '个人基础信息'
    };
    // console.log('this.data', topic_id,chatOverView.titleCodeList.length,allTopicIdsList);
    wx.navigateTo({
      url: '/pages/questionDetail/index?question=' + JSON.stringify(question),
    });
  },

  onShare() {
    wx.showModal({
      title: '温馨提示',
      content: '请先点击书本查看正文后再进行分享哦～',
      success (res) {
        if (res.confirm) {
          wx.showLoading({
            title: '正在生成PDF...',
            mask: true,
          });
          book
            .generatePDF()
            .then((response) => {
              console.log('response', response);
              wx.hideLoading();
              if (response && response.code === 0 && response.data) {
                const pdfUrl = response.data.direct_url || '';
                console.log('PDF生成成功:', pdfUrl);
      
                // 导航到分享页面，并传递PDF URL参数
                wx.navigateTo({
                  url: `/pages/bookShare/index?pdfUrl=${encodeURIComponent(pdfUrl)}&showShare=true`,
                  success: () => {
                    console.log('成功跳转到分享页面');
                  },
                  fail: (err) => {
                    console.error('跳转到分享页面失败:', err);
                    wx.showToast({
                      title: '无法打开分享页面',
                      icon: 'none',
                    });
                  },
                });
              }
            })
            .catch((error) => {
              wx.hideLoading();
              console.error('生成PDF出错:', error);
              wx.showToast({
                title: '生成pdf失败，请先去完成4个话题的回答哦～',
                icon: 'none',
              });
            });
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },
});
