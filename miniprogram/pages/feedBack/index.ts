// pages/feedBack/index.ts
import feedbackApi from '../../api/modules/feedback';
Page({
  /**
   * 页面的初始数据
   */
  data: {
    feedBackValue: '',
    contactValue: '',
    fileList: [] as any[],
    imgParams: {
      image_file_base64: '',
      image_format: ''
    } as {
      image_file_base64: string;
      image_format: string;
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {},

  onInput(e: { detail: { value: any } }) {
    this.setData({ feedBackValue: e.detail.value });
  },
  onContactInput(e: { detail: { value: any } }) {
    this.setData({ contactValue: e.detail.value });
  },
  onAfterRead(event: any) {
    const { file } = event.detail;
    const files = Array.isArray(file) ? file : [file];

    // 先将新图片添加到fileList中显示
    const currentFileList = this.data.fileList || [];
    const newFileList = currentFileList.slice(); // 使用 slice() 代替扩展运算符

    files.forEach((item) => {
      // 添加新图片到列表
      newFileList.push(item);
    });

    // 立即更新UI显示新图片
    this.setData({ fileList: newFileList });

    // 然后处理上传逻辑
    files.forEach((item) => {
      wx.getFileSystemManager().readFile({
        filePath: item.url,
        encoding: 'base64',
        success: async (res) => {
          item.base64 = res.data;
          const suffix = item.url.split('.').pop();
          const imgParams: any = {
            image_file_base64: item.base64,
            image_format: suffix,
          };
          this.setData({ imgParams });
        },
        fail: () => {
          wx.showToast({ title: '图片读取失败', icon: 'none' });
        },
      });
    });
  },
  async onDeleteImage(event: any) {
    const { index } = event.detail;
    const fileList = this.data.fileList.slice();
    fileList.splice(index, 1);
    this.setData({ fileList });
  },
  // 提交反馈
  async submitFeedback() {
    if (!this.data.feedBackValue) {
      wx.showToast({
        title: '请填写问题描述',
        icon: 'none',
      });
      return;
    }
    if (!this.data.contactValue) {
      wx.showToast({
        title: '请填写联系方式',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '提交中...',
      mask: true,
    });
    const params = {
      feedback_text: this.data.feedBackValue,
      contact_info: this.data.contactValue,
      image_file_base64: this.data.imgParams.image_file_base64,
      image_format: this.data.imgParams.image_format,
    };

    console.log('params', params);
    // 这里应该调用实际的API上传反馈和图片
    // 示例代码，实际项目中需要替换为真实API调用
    const res = await feedbackApi.submitFeedback(params);
    wx.hideLoading();
    if (!res) return;
    wx.showToast({
      title: '提交成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 清空表单
        this.setData({
          feedBackValue: '',
          contactValue: '',
          fileList: [],
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      },
    });
  },
});
