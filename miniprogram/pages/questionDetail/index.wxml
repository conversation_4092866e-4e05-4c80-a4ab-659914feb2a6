<view class="qd-container">
  <view wx:if="{{!isPreParing}}" style="padding-bottom:280rpx">
    <!-- 进度条 -->
    <view class="qd-progress-wrapper">
      <view class="qd-progress-label">话题{{topicIdIndex}}：</view>
      <!-- <view class="qd-progress-bar-bg">
        <view class="qd-progress-bar" style="width: {{percent}}%"></view>
      </view> -->
        <!-- 进度条 -->
    <ProgressBar min="{{minQuestionCount}}" max="{{maxQuestionCount}}" current="{{count}}" showMinLabel="{{true}}"/>

    </view>

    <!-- 问题卡片 -->
    <view class="qd-question-card">
      <view class="qd-question-header">
        <view class="qd-question-tag">问题{{question.question_index + 1 || currentIdx}}{{ topicId === 'basic_1'? '':'/'}}{{ topicId === 'basic_1'?'':titleid}}</view>
        <van-icon color="#282624E5" size="40rpx" name="volume" bindtap="speakQuestion" style="margin-left:15rpx" />
        <van-icon color="#282624E5" size="40rpx"  name="stop-circle-o" bindtap="stopSpeak" style="margin-left:15rpx"  />
      </view>
      <view class="qd-question-content" style="font-size: {{ fontSize * 2}}rpx;">
        {{question.q}}
      </view>
      <view class="qd-question-skip" bindtap="skipQuestion" wx:if="{{shouldCallGetQuestion}}">
        <image src="http://**********:58008/frontend-images/AR.png" mode="aspectFit" lazy-load="false"/>
        不想回答？下一题
      </view>
        
    </view>

    <!-- 提示卡片 -->
    <view class="qd-question-card border" wx:if="{{!!question.hint}}">
      <view class="tips-title">
        <image class="tips-icon" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/TipsGreen.png" />
        提示
      </view>
      <view class="tips-content" style="font-size: {{ fontSize * 2}}rpx;">
        {{question.hint}}
      </view>
    </view>

    <!-- 多选区域 -->
    <view wx:if="{{question.is_options}}">
      <view class="qd-question-header" style="margin-left:40rpx;font-size:28rpx;color:#28262499">
        可多选
      </view>
      <van-checkbox-group value="{{ checkboxResult }}" bind:change="handleCheckBoxChange" class="checkbox-container">
        <view wx:for="{{optionsWithChecked}}" wx:key="value">
          <van-checkbox use-icon-slot name="{{item.value}}">
            <view class="checkbox-item {{item.checked ? 'choosed' : ''}}">
              {{item.value}}
            </view>
          </van-checkbox>
        </view>
      </van-checkbox-group>
    </view>
    <!-- 问题回答区 样式2-->
    <view class="my-answer-card" wx:if="{{myAnswer}}">
      <image src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Edit.png" bindtap="handleEdit" />
      <view class="my-answer-card-edit-content"  style="font-size: {{ fontSize * 2}}rpx;"> {{myAnswer}}</view>
    </view>
    <!-- 新增：录音播放控件 -->
    <view wx:if="{{audioFilePath}}" class="answer-audio">
      <view class="audio-info">
        <image class="audio-icon" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/yuyin.png" mode="aspectFit" />
        <view bindtap="toggleAudioPlay" style="margin-left:10px">
          <van-icon size="20px" wx:if="{{audioPlaying}}" name="pause-circle-o" />
          <van-icon size="20px" wx:else name="play-circle-o" />
        </view>
        <van-icon size="20px" bindtap="deleteAudio" name="close" style="margin-left:10px" />
      </view>
    </view>

    <!-- 图片上传区域 -->
    <van-uploader class="qd-upload-box" file-list="{{fileList}}" max-count="1" bind:after-read="onAfterRead" bind:delete="onDeleteImage" accept="image">
      <view class="qd-upload">
        <view class="qd-upload-icon">
          <image style="height: 56rpx;width: 56rpx;" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/addPic.png" mode="" />
        </view>
        <view class="qd-upload-text">添加图片</view>
      </view>
    </van-uploader>

    <!-- 语音输入按钮 -->
    <view class="qd-voice-box">
      <view class="qd-voice-btn" bindtouchstart="startVoice">
        <image src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/speak.png"  />
      </view>
       <view class="qd-voice-tip">{{isRecognizing ? '点击停止' : '点击说话'}}</view>
    </view>

    <!-- 底部输入框 -->
    <view class="qd-bottom-bar" style="bottom:{{bottom * 2}}rpx">
      <view class="qd-bottom-bar" style="bottom:60rpx;">
        <textarea style="font-size: {{ fontSize * 2}}rpx;" value="{{inputValue}}" class="qd-input" placeholder="文字回答..." placeholder-class="qd-input-placeholder" bindinput="onInput" confirm-type="send" bindconfirm="sendMsg" focus="{{inputFocus}}" bindfocus="onFocus" bindblur="onBlur" adjust-position="{{false}}" maxlength="{{-1}}" />
        <image class="input-full" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/full.png" mode="" bindtap="showPopup" />
      </view>
      <view class="qd-bottom-bar-tip">
        内容由AI生成，仅供参考
      </view>
        
      <!-- <van-icon name="guide-o" size="40rpx" color="#b0b0b0" bindtap="sendMsg" /> -->
      <van-popup show="{{ show }}" round position="bottom" custom-style="height: {{keyboardHeight > 0 ? '70%' : '40%'}};" bind:close="onClose" closeable safe-area-inset-bottom close-icon="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/tiny.png" class="{{keyboardHeight > 0 ? 'keyboard-active' : ''}}">
        <view class="popup-container">
          <view class="input-icon"></view>
          <textarea style="font-size: {{ fontSize * 2}}rpx;" placeholder="文字回答..." confirm-type="send" value="{{inputValue}}" focus="{{popupFocus}}" bindinput="onInput" bindconfirm="sendMsg" maxlength="{{-1}}">
          </textarea>
        </view>
        <image class="input-send-icon" src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Group_1.png" mode="" bindtap="sendMsg" />
      </van-popup>
    </view>

    <!-- 语音遮罩弹窗 -->
    <view wx:if="{{showVoiceMask}}" class="voice-mask">
      <view class="voice-popup">
        <view class="voice-diglog">
          <view class="voice-timer">{{voiceDurationStr}}</view>
          <view class="voice-wave">
            <block wx:for="{{waveData}}" wx:key="index">
              <view class="wave-bar" style="height: {{item}}rpx"></view>
            </block>
          </view>
          <view class="triangle"></view>
        </view>

        <view class="voice-actions">
          <view class="voice-cancel" bindtap="cancelVoice">取消</view>
          <view class="voice-to-text" bindtap="stopVoice">语音转文字</view>
        </view>
        <view class="voice-btn-outer">
          <image src="https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/Frame.png" />
          <!-- <view class="voice-tip">松手发送</view> -->
        </view>
      </view>
    </view>
  </view>
  <view wx:else style="margin-top: 400rpx;">
    <van-loading type="spinner" size="24px" vertical color="#F5A631">正在为您准备下一个问题，请稍候...</van-loading>
    <view class="triangle"></view>
  </view>
  <van-dialog
    use-slot
    show="{{ topicShow }}"
    show-confirm-button="{{false}}"
    close-on-click-overlay="{{false}}"
  >
    <view class="topic-content">
      {{wsMsg}}
    </view>
    <view class="topic-btns">
      <view bindtap="onTopicClose">
        留在这个话题
      </view>
      <view class="topic-btn-confirm" bindtap="onTopicConfirm">
        进入下一个话题
      </view>
    </view>
  </van-dialog>
    
</view>