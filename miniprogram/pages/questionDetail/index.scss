// 顶部导航栏
.qd-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 96rpx;
  padding: 0 32rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  position: relative;
  z-index: 10;
  .qd-navbar-left, .qd-navbar-right {
    width: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
    color: #282624;
  }
  .qd-navbar-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: 600;
    color: #282624;
  }
}

// 进度条
.qd-progress-wrapper {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 0 32rpx;
  .qd-progress-label {
    font-size: 28rpx;
    color: #28262466;
    font-weight: 500;
    margin-right: 16rpx;
  }
}

// 问题卡片
.qd-question-card {
  margin: 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  padding: 32rpx;
  overflow: auto;
  .qd-question-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    .qd-question-tag {
      border:1rpx solid #f4a914;
      background: #fff7e6;
      color: #f4a914;
      font-size: 24rpx;
      border-radius: 25rpx;
      padding: 4rpx 20rpx;
      font-weight: 500;
      display: inline-block;
    }
  }
  .qd-question-content {
    font-size: 30rpx;
    color: #555;
    line-height: 1.7;
    margin-top: 8rpx;
    font-weight: 400;
  }
}

// 图片上传区域
.qd-upload-box {
  margin: 0 32rpx;
  background: #FFFFFF;
  border: 2rpx dashed #E5E5E5;
  border-radius: 20rpx;
  height: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 300rpx;
  float: right;
  .qd-upload-icon {
    width: 56rpx;
    height: 56rpx;
    font-size: 60rpx;
    color: #c0c0c0;
    margin-bottom: 8rpx;
  }
  .qd-upload-text {
    font-size: 24rpx;
    color: #b0b0b0;
  }
}

// 语音输入按钮
.qd-voice-box {
  position: fixed;
  bottom: 310rpx;
  right: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 48rpx auto 0 auto;
  width: 130rpx;
  .qd-voice-btn {
    width: 130rpx;
    height: 130rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48rpx;
    color: #f4a914;
    box-shadow: 0 2rpx 12rpx rgba(244,169,20,0.08);
    margin-bottom: 12rpx;
    image {
      width:130rpx;
      height:130rpx;
    }
  }
  .qd-voice-tip {
    font-size: 28rpx;
    color: #b0b0b0;
  }
}

// 底部输入栏
.qd-bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx 32rpx 32rpx;
  box-shadow: 0 -2rpx 12rpx rgba(0,0,0,0.04);
  z-index: 20;
  .qd-input {
    // flex: 1;
    width: 78%;
    height: 150rpx;
    border-radius: 16rpx;
    background: #f7f7f7;
    border: none;
    font-size: 28rpx;
    padding: 24rpx;
    color: #282624;
    margin-right: 10rpx;
  }
  .qd-input-placeholder {
    color: #b0b0b0;
    font-size: 28rpx;
  }
  .qd-bottom-bar-tip{
    font-size: 24rpx;
    margin:auto;
    color: #b0b0b0;
    padding-right:66rpx;
  }
}
// 容器整体
.qd-container {
  min-height: 100vh;
  background: #f7f7f7;
  display: flex;
  justify-content: center;
  // align-items: center;
}

// iconfont 占位
.iconfont {
  font-family: 'iconfont';
}
.qd-upload{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.recording-tip {
  color: #007aff;
  text-align: center;
  margin-top: 20rpx;
  font-size: 26rpx;
}
.input-full{
  width: 32rpx;
  height: 32rpx;
  margin-left: 10rpx;
}
.popup-container{
  display: flex;
  padding: 100rpx 50rpx 50rpx 50rpx;
}
.input-icon{
  font-weight: 500;
  margin-right: 8rpx;
  margin-top: 14rpx;
  width: 4rpx;
  height: 30rpx;
  background-color: #F5A630;
}
.input-send-icon{
  width: 80rpx;
  height: 80rpx;
  float: right;
  margin-right: 50rpx;
}

// Popup 样式优化
.popup-container {
  display: flex;
  padding: 100rpx 50rpx 50rpx 50rpx;
  position: relative;
  
  textarea {
    flex: 1;
    min-height: 200rpx;
    max-height: 400rpx;
    border: none;
    background: transparent;
    font-size: 28rpx;
    line-height: 1.6;
    color: #282624;
    padding: 0;
    resize: none;
    
    &::placeholder {
      color: #b0b0b0;
      font-size: 28rpx;
    }
  }
}

.input-icon {
  color: #F5A630;
  font-weight: 600;
  margin-right: 8rpx;
  font-size: 32rpx;
}


// 键盘弹起时的动画效果
.van-popup {
  transition: all 0.3s ease-in-out;
  
  &.keyboard-active {
    transform: translateY(-20rpx);
  }
}

// 确保 popup 紧贴键盘
.van-popup--bottom {
  &.van-popup--round {
    border-radius: 20rpx 20rpx 0 0;
  }
}

// 当键盘弹起时，确保 popup 紧贴键盘
.van-popup--bottom.van-popup--show {
  bottom: 0 !important;
}

// 键盘弹起时的 popup 样式优化
.van-popup--bottom {
  transition: all 0.3s ease-in-out;
  
  // 当键盘弹起时，增加内边距确保内容不被遮挡
  &.keyboard-active {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

// 确保 textarea 在键盘弹起时有足够的空间
.popup-container {
  display: flex;
  padding: 100rpx 50rpx 50rpx 50rpx;
  position: relative;
  min-height: 300rpx; // 确保最小高度
  
  textarea {
    flex: 1;
    min-height: 200rpx;
    max-height: 600rpx; // 增加最大高度
    border: none;
    background: transparent;
    font-size: 28rpx;
    line-height: 1.6;
    color: #282624;
    padding: 0;
    resize: none;
    
    &::placeholder {
      color: #b0b0b0;
      font-size: 28rpx;
    }
  }
}

// 输入框聚焦时的样式
.popup-container textarea:focus {
  outline: none;
  background: rgba(245, 166, 48, 0.05);
  border-radius: 8rpx;
  padding: 16rpx;
  margin: -16rpx;
}

.voice-mask {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.voice-popup {
  width: 100%;
  height: 80%;
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}
.voice-timer {
  font-size: 36rpx;
  color: #b0b0b0;
  padding: 20rpx;
}
.voice-diglog{
  width: 440rpx;
  background-color: #F7F6F5;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-bottom: 90rpx;
}
.voice-actions {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-bottom: 30rpx;
  .voice-cancel, .voice-to-text {
    width: 300rpx;
    height: 80rpx;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.24);
    color: #fff;
    text-align: center;
    line-height: 80rpx;
    font-weight: 500;
    font-size: 28rpx;
    border: none;
    margin:0 20rpx;

  }
}
.voice-btn-outer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 180rpx;
  image {
    width: 200rpx;
    height: 200rpx;
  }
}
.voice-tip {
  color: #b0b0b0;
  font-size: 28rpx;
  margin-top: 8rpx;
}
.triangle {
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-top: 20rpx solid #fff;
  margin: 0 auto;
  /* 让三角形紧贴对话框底部 */
  position: absolute;
  bottom: -16rpx;
  right: 0;
  left: 0;
}
.tips-title{
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #282624E5;
  font-weight: 500;
}
.tips-icon{
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}
.tips-content{
  font-size: 28rpx;
  color: #28262499;
  padding: 10rpx 48rpx;
}
.border{
  border: 1px solid #2826241A;
}
.my-answer-card{
  display: flex;
  margin: 32rpx;
  justify-content: flex-end;
  image{
    width: 64rpx;
    height: 64rpx;
  }
  .my-answer-card-edit-content{
    padding: 32rpx;
    background-color: #2826240A;
    margin-left: 20rpx;
    width: 450rpx;
    border-radius: 24rpx;
  }
 
}
.voice-wave {
  display: flex;
  align-items: center;
  height: 80rpx;
  margin: 0 auto 20rpx auto; 
  justify-content: center;
}
.wave-bar {
  width: 4rpx;
  margin: 20rpx 3rpx;
  background: #FFD180;
  border-radius: 4rpx;
  transition: height 0.2s;
}
.checkbox-container .van-checkbox-group{
  display: flex;
  padding: 10rpx;
  flex-wrap: wrap;
  justify-content: space-around;
  .checkbox-item{
    width: 152rpx;
    text-align: center;
    padding: 15rpx 36rpx;
    border: 1rpx solid #2826241A;
    background-color: #fff;
    border-radius: 60rpx;
    color: #28262499;
    font-size: 28rpx;
    font-weight: 500;
    margin: 15rpx;
  }
  .choosed{
    border: 1rpx solid #f4a914;
    color: #f4a914;
    background: #fff7e6;
  }
}
.answer-audio{
  display: flex;
  align-items: center;
  justify-content:flex-end;
  margin-bottom: 30rpx;
  padding:0 32rpx;

  .audio-info{
    padding: 20rpx;
    border-radius: 10rpx;
    background: #fff;
    display: flex;
    align-items: center;
    border-radius: 20rpx;
    border: 1px solid #2826241A;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  }
  .audio-icon{
    width: 32rpx;
    height: 32rpx;
  }
  
}
.topic-content{
  padding:40rpx;
}
.topic-btns{
  display: flex;
  align-items: center;
  justify-content: center;
  view{
    width: 260rpx;
    margin: 20rpx;
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    border: 1px solid #f4a914;
    text-align: center;
    color:#f4a914;
  }
  .topic-btn-confirm{
    background: #f4a914;
    color: #fff;
  }
}
.qd-question-skip{
  width: 290rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 40rpx;
  border: 2rpx solid #2826241A;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  float: right;
  font-weight: 500;
  margin-top: 20rpx;
  image{
    width: 32rpx;
    height: 32rpx;
    margin-right: 10rpx;
  }
}

