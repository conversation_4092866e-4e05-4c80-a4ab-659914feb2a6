const plugin = requirePlugin('WechatSI');
import chatApi from '../../api/modules/chat';
import contentApi from '../../api/modules/data';
let manager: any = null;
let voiceTimer: any = null;
let startTimestamp: number = 0;
let ttsAudio:WechatMiniprogram.InnerAudioContext | null = null;

Page({
  data: {
    inputValue: '',
    images: '',
    recording: false,
    isRecognizing: false,
    fileList: [] as any[],
    question: {},
    myAnswer: '',
    currentIdx: 1,
    questionLength: 1,
    percent: 0,
    show: false,
    keyboardHeight: 0,
    showVoiceMask: false,
    voiceDuration: 0, // 录音时长（秒）
    voiceDurationStr: '0:00',
    inputFocus: false,
    isEdit: false,
    waveData: Array.from({ length: 35 }, () => Math.floor(Math.random() * 28) + 28), // 初始高度，可自定义数量
    waveTimer: null as any,
    msgId: null,
    checkboxResult: [], // 选中的项
    optionsWithChecked: [], // 处理后的选项
    titleid: 1,
    isPreParing: false,
    fontSize: '28rpx',
    topicId: '',
    bottom: 0,
    audioFilePath: '',
    audioBase64: '',
    audioPlaying: false,
    innerAudioCtx: null as any,
    topicIdIndex:0,
    topicShow: false,
    current_topic_id:'',
    next_topic_id:'',
    shouldCallGetQuestion:true,
    popupFocus: false,
    wsMsg:'',
    minQuestionCount:12,
    maxQuestionCount:40,
    count:0
  },

  onLoad(options: any) {
    let topicId:any;
    const chatOverView = wx.getStorageSync('chatOverView');
    const allTopicIdsList = wx.getStorageSync('allTopicIdsList');
    this.setData({
      titleid: chatOverView.titleList.length || 1,
      percent: chatOverView.basicProgress,
    });

    if (options && options.question) {
      const question = options && JSON.parse(options.question);
       
      topicId = question.topic_id;
      
      const topicIdIndex = allTopicIdsList.findIndex((item:any) => item === topicId)
      console.log('optionsss',question,topicIdIndex,topicId)
      this.setData({
        topicId,
        titleid: question.max_question_count || 10,
        topicIdIndex:topicIdIndex + 1,
      });
      this.getCurrentProgress(question.period_id)
      wx.setNavigationBarTitle({
        title: question.topic_name,
      });
    }
    // 检查来源页面，决定是否调用 getQuestion
    this.checkRouteSourceAndGetQuestion(topicId);
    this.loadFontSize();

    this.registerWebSocketListener();

    // 初始化语音识别管理器
    this.initVoiceManager();
    try{
      if(!ttsAudio){
        ttsAudio = wx.createInnerAudioContext();
        ttsAudio.autoplay = false;
        // ios静音键下仍可播放
        wx.setInnerAudioOption({
          obeyMuteSwitch: false,
        });
        ttsAudio.onError((error)=> {
          console.warn('tts语音播放出错', error)
        })
      }
    }catch(e){
      console.warn('初始化失败',e)
    }

    wx.onKeyboardHeightChange((res) => {
      const extraOffset = 50;
      const adjustedHeight = res.height > 0 ? res.height + extraOffset : 0;
      // console.log('adjustedHeight', adjustedHeight);
      this.setData({ keyboardHeight: adjustedHeight });
    });
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on('acceptDataFromDialog', (data) => {
      console.log('dataaa',data)
      const topicIdIndex = allTopicIdsList.findIndex((item:any) => item === data.question.topic_id)
      this.setData({
        question: Object.assign({}, data.question, {
          q: data.question.question || data.question.q,
        }),
      });
      // 处理图片URL反显
      let fileList = [];
      if (data.question.image_url) {
        // 如果传过来的是单个图片URL
        fileList = [
          {
            url: data.question.image_url,
            status: 'done',
            message: '上传成功',
          },
        ];
      } else if (data.question.images && Array.isArray(data.question.images)) {
        // 如果传过来的是图片URL数组
        fileList = data.question.images.map((imageUrl: string) => ({
          url: imageUrl,
          status: 'done',
          message: '上传成功',
        }));
      }
      wx.setNavigationBarTitle({
        title: data.title || '问题详情',
      });

      this.setData({
        myAnswer: data.question.answer || '',
        currentIdx: data.idx || 1,
        questionLength: data.length || 1,
        fileList: fileList,
        msgId: data.question.message_id,
        titleid: data.question.max_question_count || 10,
        topicIdIndex:topicIdIndex + 1,
        audioFilePath:data.question.audio_url
      });
      this.updateOptionsWithChecked();
    });
  },

  // 添加页面显示生命周期
  onShow() {
    // 确保语音识别状态正确
    this.resetVoiceState();
  },

  // 添加页面隐藏生命周期
  onHide() {
    // 页面隐藏时停止语音识别
    this.stopVoiceRecognition();
    this.data.innerAudioCtx?.stop();
    // 停止TTS播放
    this.stopSpeak();
  },

  // 添加页面卸载生命周期
  onUnload() {
    // 页面卸载时清理所有定时器和语音识别
    this.cleanupVoiceResources();
    this.data.innerAudioCtx.stop();
    try{
      if(ttsAudio){
        ttsAudio.stop();
        ttsAudio.destroy();
        ttsAudio = null;
      }
    }catch(e){}
  },

  async getCurrentProgress(period_id:any) {
    const response = await contentApi.getChatOverViewV2();
    console.log('getChatOverView response111:', response);
    const currentPeriod = response.storyList.find((item:any) => item.title_id === period_id) || {};
    console.log('currentPeriod',currentPeriod)
    if(!currentPeriod) return;
    this.setData({
      minQuestionCount:currentPeriod.min_question_count || 12,
      maxQuestionCount:currentPeriod.max_question_count || 40,
      count:currentPeriod.count || 0
    });
  },

  // 初始化语音识别管理器
  initVoiceManager() {
    // 确保先停止之前的实例
    if (manager) {
      try {
        manager.stop();
      } catch (e) {
        console.log('停止之前的语音识别实例时出错:', e);
      }
    }

    manager = plugin.getRecordRecognitionManager();

    // 重置所有事件监听器
    manager.onRecognize = (res: { result: any }) => {
      if (res.result) {
        this.setData({ inputValue: res.result });
      }
    };

    manager.onStart = () => {
      console.log('语音识别开始');
      this.setData({ isRecognizing: true, recording: true });
      this.startWaveAnim();
    };

    manager.onStop = (res: { result: any }) => {
      console.log('语音识别停止', res);
      this.setData({ recording: false, isRecognizing: false,audioFilePath: res.tempFilePath });
      this.stopWaveAnim();
        wx.getFileSystemManager().readFile({
        filePath: res.tempFilePath,
        encoding: 'base64',
        success: (r) => {
          console.log('录音转base64成功');
          this.setData({ audioBase64: typeof r.data === 'string' ? r.data : '' });
        },
        fail: (error) => {
          console.error('录音转base64失败', error);
          wx.showToast({ title: '录音转码失败', icon: 'none' });
        }
      });
      if (res.result) {
        console.log('res.result', res.result);
        this.setData({ inputValue: res.result });
        console.log('inputValue', this.data.inputValue);
      } else {
        wx.showToast({ title: '未识别到语音', icon: 'none' });
      }
    };

    manager.onError = (error: any) => {
      console.log('语音识别错误', error);
      this.setData({ recording: false, isRecognizing: false });
      this.stopWaveAnim();
      // wx.showToast({ title: '语音识别出错', icon: 'none' });
    };
  },
  // 重置语音状态
  resetVoiceState() {
    this.setData({
      recording: false,
      isRecognizing: false,
      showVoiceMask: false,
      voiceDuration: 0,
      voiceDurationStr: '0:00'
    });

    // 清理定时器
    if (voiceTimer) {
      clearInterval(voiceTimer);
      voiceTimer = null;
    }

    // 停止波动画
    this.stopWaveAnim();
  },

  // 停止语音识别
  stopVoiceRecognition() {
    if (manager) {
      try {
        manager.stop();
      } catch (e) {
        console.log('停止语音识别时出错:', e);
      }
    }
    this.resetVoiceState();
  },

  // 清理语音资源
  cleanupVoiceResources() {
    this.stopVoiceRecognition();

    // 清理全局变量
    if (voiceTimer) {
      clearInterval(voiceTimer);
      voiceTimer = null;
    }

    // 重置全局变量
    startTimestamp = 0;
  },

  loadFontSize() {
    const fontSize = wx.getStorageSync('fontSize');
    this.setData({
      fontSize: fontSize || '28rpx',
    });
  },

  /**
   * 检查路由来源并决定是否调用 getQuestion，如果从编辑页面过来就不需要调用
   */
  checkRouteSourceAndGetQuestion(topicId: string) {
    try {
      // 获取页面栈
      const pages = getCurrentPages();
      let shouldCallGetQuestion = true;

      // 如果页面栈长度大于1，检查上一个页面的路由
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];
        const prevRoute = prevPage.route;

        console.log('上一个页面路由:', prevRoute);

        // 如果是从 /pages/interviewList/index 过来的，不调用 getQuestion
        if (prevRoute === 'pages/interviewList/index') {
          shouldCallGetQuestion = false;
          console.log('从 interviewList 页面过来，不调用 getQuestion');
        }
      }

      // 根据判断结果决定是否调用 getQuestion
      if (shouldCallGetQuestion) {
        console.log('从其他页面过来，调用 getQuestion');
        this.getQuestion(topicId);
      }
      this.setData({
        shouldCallGetQuestion
      })
    } catch (error) {
      console.error('检查路由来源失败:', error);
      // 出错时默认调用 getQuestion
      this.getQuestion(topicId);
    }
  },

  /**
   * 注册WebSocket消息监听器
   * 用于监听章节内容生成的流式返回
   */
  registerWebSocketListener() {
    try {
      const app = getApp();
      if (!app || !app.API || !app.API.websocket) {
        console.error('WebSocket API未初始化');
        return;
      }

      // 注册消息监听器
      app.API.websocket.registerMessageListener('question-detail-listener', (message: any) => {
        this.handleWebSocketMessage(message);
      });

      console.log('已注册章节生成消息监听器');
    } catch (error) {
      console.error('注册WebSocket监听器失败:', error);
    }
  },
  /**
   * 处理WebSocket消息
   * @param {Object} message - 收到的消息对象
   */
  handleWebSocketMessage(message: any) {
    try {
      // 解析消息
      if (typeof message === 'string') {
        try {
          message = JSON.parse(message);
        } catch (e) {
          // 忽略非JSON消息
          return;
        }
      }
      const { event, data:wsData, message: wsMessage} = message;
      const data = { current_period: this.data.question.topic_id };
      console.log(event, 'event','message',wsData);
      if (event === 'topic_completed') {
        this.handleprogressUpdate(data);
      }
      if (event === 'period_progress_update') {
        this.setData({ topicShow: true, current_topic_id:wsData.current_topic_id, next_topic_id:wsData.next_topic_id, wsMsg:wsMessage });
      }

    } catch (error) {
      console.error('处理WebSocket消息失败:', error);
    }
  },
  /**
   * 处理时期完成
   */
  handleprogressUpdate(data: any) {
    this.navigateToCompletePage(data);
  },

  /**
   * 跳转到完成页面，话题完成
   */
  navigateToCompletePage(data: any) {
    wx.navigateTo({
      url: '/pages/completePage/index?force=true&data=' + JSON.stringify(data),
    });
  },
  // 处理选项，给每个选项加 checked 字段
  updateOptionsWithChecked() {
    const { question, checkboxResult } = this.data;
    console.log('question', question);

    const optionsWithChecked = question?.options?.map((item) => ({
      value: item,
      checked: checkboxResult.includes(item),
    }));
    this.setData({ optionsWithChecked });
  },
  showPopup() {
    this.setData({ show: true }, () => {
       setTimeout(() => {
        this.setData({ popupFocus: true });
      }, 100);
    });
   
  },
  onClose() {
    this.setData({ show: false });
  },
  onBack() {
    wx.navigateBack({ delta: 1 });
  },
  onAfterRead(event: any) {
    const { file } = event.detail;
    const files = Array.isArray(file) ? file : [file];

    // 先将新图片添加到fileList中显示
    const currentFileList = this.data.fileList || [];
    const newFileList = currentFileList.slice(); // 使用 slice() 代替扩展运算符

    files.forEach((item) => {
      // 添加新图片到列表
      newFileList.push(item);
    });

    // 立即更新UI显示新图片
    this.setData({ fileList: newFileList });

    // 然后处理上传逻辑
    files.forEach((item) => {
      wx.getFileSystemManager().readFile({
        filePath: item.url,
        encoding: 'base64',
        success: async (res) => {
          item.base64 = res.data;
          const suffix = item.url.split('.').pop();
          let params: any = {
            question: (this.data.question as any).q,
            topic_id: (this.data.question as any).topic_id,
            qa_record_id: (this.data.question as any).qa_record_id,
            image_file_base64: item.base64,
            image_format: suffix,
          };
          const method = !this.data.shouldCallGetQuestion ? 'put' : 'post';
          if (this.data.msgId) {
            params = Object.assign({}, params, {
              message_id: this.data.msgId,
            }) as any;
          }
          console.log('params', params, method, item);
          const { success, saved_messages } = await chatApi.sendReplyV2(params, method);
          if (success) {
            // 清除缓存，确保返回上一页时能获取最新数据
            this.triggerEvent('refreshquestion');
            this.setData({
              msgId: saved_messages.message_id,
              question: {
                ...this.data.question,
                qa_record_id: saved_messages.qa_record_id,
                topic_id: saved_messages.topic_id,
              },
            });
          }
        },
        fail: () => {
          wx.showToast({ title: '图片读取失败', icon: 'none' });
        },
      });
    });
  },
  async onDeleteImage(event: any) {
    const { index } = event.detail;
    const fileList = this.data.fileList.slice();
    fileList.splice(index, 1);
    const params = {
      message_id: this.data.msgId,
    };
    const res = await chatApi.delImage(params);
    if (res) {
      this.setData({ fileList });
    }
  },
  // 切换语音识别状态
  toggleVoice() {
    if (this.data.isRecognizing) {
      // 如果正在识别，则停止
      this.stopVoice();
    } else {
      // 如果没有识别，则开始
      this.startVoice();
    }
  },

  startVoice() {
    // 确保状态正确
    this.resetVoiceState();
    // 开始录制语音的时候，停止播放语音
    this.stopSpeak();
    this.setData({ isRecognizing: true, showVoiceMask: true, voiceDuration: 0 });
    
    // 清理之前的定时器
    if (voiceTimer) {
      clearInterval(voiceTimer);
      voiceTimer = null;
    }
    
    startTimestamp = Date.now();
    voiceTimer = setInterval(() => {
      // 精准计时
      const seconds = Math.floor((Date.now() - startTimestamp) / 1000);
      if (seconds !== this.data.voiceDuration) {
        this.setData({ voiceDuration: seconds, voiceDurationStr: this.formatTime(seconds) });
      }
    }, 60);
    
    // 确保语音识别管理器存在且状态正确
    if (!manager) {
      this.initVoiceManager();
    }
    
    try {
      manager.start({ lang: 'zh_CN' });
    } catch (error) {
      console.log('启动语音识别失败:', error);
      wx.showToast({ title: '语音识别启动失败', icon: 'none' });
      this.resetVoiceState();
    }
  },
  
  stopVoice() {
    this.setData({ showVoiceMask: false, isRecording: false, });
    
    // 清理定时器
    if (voiceTimer) {
      clearInterval(voiceTimer);
      voiceTimer = null;
    }
    
    // 停止语音识别
    if (manager) {
      try {
        manager.stop();
      } catch (error) {
        console.log('停止语音识别失败:', error);
      }
    }
  },
  formatTime(sec: number) {
    const m = Math.floor(sec / 60);
    const s = sec % 60;
    console.log('sec', sec);
    return `${m}:${s < 10 ? '0' : ''}${s}`;
  },
  onInput(e: { detail: { value: any } }) {
    this.setData({ inputValue: e.detail.value });
  },
  // 发送回答
  async sendMsg() {
    const params: any = {
      topic_id: (this.data.question as any).topic_id,
      qa_record_id: (this.data.question as any).qa_record_id,
      reply: this.data.inputValue,
    };
    // 如果有录音文件，添加录音的base64数据
    if (this.data.audioBase64) {
      params.audio_file_base64 = this.data.audioBase64;
      params.audio_format = 'mp3';
    }
    if(!this.data.inputValue) {
      wx.showToast({
        title: '请输入回答',
        icon: 'none',
      });
      return;
    }
    if(!this.data.inputValue){
      params.reply = (this.data.question as any).answer
    }
    if(this.data.msgId){
      params.message_id = this.data.msgId
    }
    wx.showLoading({
      title: '正在发送...',
      mask: true,
    });
    if(this.data.msgId){
      params.message_id = this.data.msgId;
    }
    console.log('shouldCallGetQuestion',this.data.shouldCallGetQuestion)
    const method = !this.data.shouldCallGetQuestion  ? 'put' : 'post';
    const res = await chatApi.sendReplyV2(params, method);
    wx.hideLoading();
    if (res) {
      // 清除缓存，确保返回上一页时能获取最新数据
      contentApi.invalidateChatCache('default');
      this.triggerEvent('refreshquestion');
      this.setData({
        myAnswer: this.data.inputValue,
        question: {
          ...this.data.question,
          qa_record_id: res.saved_messages.qa_record_id,
          topic_id: res.saved_messages.topic_id,
        },
      });
      console.log('res', res);
      // 只有在回答新问题的状态下才更新问题，编辑情况下不切换新问题，并且如果回答的不正确也不切换下一个问题
      if (method === 'post' && !this.data.isEdit && res.success) {
        // console.log('text');
        this.getQuestion(this.data.topicId);
        this.setData({
          inputValue: '',
          fileList: [],
          audioFilePath:'',
          audioBase64:''
        });
      }
      // 如果回答的不正确，显示建议
      if (!res.success) {
        wx.showToast({
          title: res.suggestion,
          icon: 'none',
        });
      }
      if (method === 'put' && this.data.isEdit  && res.success) {
        this.onBack();
        this.setData({
          inputValue: '',
          fileList: [],
          msgId: res.saved_messages?.message_id,
        });
      }
     
      this.onClose();
    }
  },
  // 点击取消
  cancelVoice() {
    // 停止语音识别
    if (manager) {
      try {
        manager.stop();
      } catch (error) {
        console.log('取消语音识别失败:', error);
      }
    }
    
    // 清理定时器
    if (voiceTimer) {
      clearInterval(voiceTimer);
      voiceTimer = null;
    }
    
    // 重置状态
    this.setData({
      showVoiceMask: false,
      voiceDuration: 0,
      isRecognizing: false,
      voiceDurationStr: '0:00',
      recording: false
    });
    
    // 停止波动画
    this.stopWaveAnim();
  },
  onFocus(e: any) {
    console.log('????', e.detail.height);
    // this.setData({ bottom: e.detail.height });
    this.setData({ show: true,inputFocus:false});
    setTimeout(() => {
      this.setData({ popupFocus: true });
    }, 100);
   
  },
  onBlur() {
    this.setData({ bottom: 0,inputFocus: false });
  },

  // 编辑回答
  handleEdit() {
    this.setData({
      inputValue: this.data.myAnswer,
      isEdit: true,
    });
    // 让输入框聚焦（需在input上加focus属性绑定）
    this.setData({ inputFocus: true });
  },
  // 动态更新波形
  updateWave() {
    const waveData = this.data.waveData.map(() => Math.floor(Math.random() * 28) + 28);
    this.setData({ waveData });
  },

  // 开始动画
  startWaveAnim() {
    console.log('开始波动画, 当前定时器:', this.data.waveTimer);
    
    // 确保先停止之前的动画
    this.stopWaveAnim();
    
    // 重置波形数据
    const waveData = Array.from({ length: 35 }, () => Math.floor(Math.random() * 28) + 28);
    this.setData({ waveData });
    
    // 启动新的动画
    const waveTimer = setInterval(() => {
      this.updateWave();
    }, 150);
    
    this.setData({ waveTimer });
    console.log('波动画定时器已设置:', waveTimer);
  },

  // 停止动画
  stopWaveAnim() {
    console.log('停止波动画, 当前定时器:', this.data.waveTimer);
    if (this.data.waveTimer) {
      clearInterval(this.data.waveTimer);
      this.setData({ waveTimer: null });
      console.log('波动画定时器已清理');
    }
  },

  // 监听多选变化
  async handleCheckBoxChange(e: { detail: any }) {
    const checkboxResult = e.detail; // van-checkbox-group 返回的选中数组
    console.log('checkboxResult', checkboxResult);
    const inputVal = checkboxResult.join('、');
    this.setData({ checkboxResult }, () => {
      this.updateOptionsWithChecked();
    });
    let params: any = {
      chain_id: (this.data.question as any).chain_id,
      question: (this.data.question as any).q,
      reply: inputVal,
    };
    const method = this.data.isEdit || this.data.msgId ? 'put' : 'post';
    if (this.data.isEdit || this.data.msgId) {
      params = Object.assign({}, params, {
        message_id: this.data.msgId,
      }) as any;
    }
    const res = await chatApi.sendReplyV2(params, method);
    if (res.code === 2000) {
      // 清除缓存，确保返回上一页时能获取最新数据
      contentApi.invalidateChatCache('default');
      this.triggerEvent('refreshquestion');
      this.setData({
        msgId: res.saved_messages.message_id || res.data.message_id,
      });
    }
  },

  // 获取当前问题轮询每1秒轮询一次，轮询40次
  async getQuestion(topicId: string) {
    this.setData({
      isPreParing: true,
    });
    const { question: questionObj, status, period_id } = await chatApi.getQuestionUrlV2(topicId);
    this.getCurrentProgress(period_id)
    if (status === 'ready') {
      console.log('questionquestion1', questionObj, this.data.question);
      this.setData({
        question: {
          ...this.data.question,
          q: questionObj.q,
          hint: questionObj.hint,
          chain_id: questionObj.chain_id,
          topic_id: questionObj.topic_id,
          qa_record_id: questionObj.qa_record_id,
          question_index: questionObj.question_index,
        },
        isPreParing: false,
        inputValue: '',
        myAnswer: '',
        fileList: [],
      });
      // console.log('questionquestion', q);
      return; // 成功获取，退出轮询
    } else {
      // code 不是 2000，设置 isPreParing 状态
      this.setData({
        isPreParing: false,
      });
    }
  },
  toggleAudioPlay() {
    console.log('this.data.audioFilePath',this.data.audioFilePath, this.data.audioPlaying, this.data.innerAudioCtx)
    if (!this.data.audioFilePath) return;
    if (this.data.audioPlaying) {
      if (this.data.innerAudioCtx) this.data.innerAudioCtx.pause();
      this.setData({ audioPlaying: false });
    } else {
      if (!this.data.innerAudioCtx) {
        const ctx = wx.createInnerAudioContext();
        ctx.onEnded(() => {
          this.setData({ audioPlaying: false });
        });
        this.setData({ innerAudioCtx: ctx });
      }
      this.data.innerAudioCtx.src = this.data.audioFilePath;
      this.data.innerAudioCtx.play();
      this.setData({ audioPlaying: true });
    }
  },
  deleteAudio() {
    this.setData({ audioFilePath: '', audioBase64: '', audioDuration: 0, audioPlaying: false });
    if (this.data.innerAudioCtx) this.data.innerAudioCtx.stop();
  },

  speakText(content:string){
    const text = (content || '').trim();
    console.log('text', text);
    if(!text){
      wx.showToast({
        title:'没有可播放的内容',
        icon: 'none'
      })
      return;
    }
    try{
      plugin.textToSpeech({
        lang:'zh_CN',
        tts:true,
        content:text,
        success:(res: any) =>{
          const src = res?.filename || res?.filepath || res?.tempFilePath || res?.audioFilePath;
          if(!src){
            wx.showToast({
              title:'生成语音失败',
              icon:'none'
            })
            return;
          }
          if(!ttsAudio){
            ttsAudio = wx.createInnerAudioContext();
            ttsAudio.obeyMuteSwitch = false;
          }
          ttsAudio!.src = src;
          ttsAudio!.play()
        },
        fail:(err:any) => {
          console.warn('tts失败',err);
          wx.showToast({title:'播报失败',icon:'none'})
        }
      })
    }catch (e:any){
      wx.showToast({
        title:'播报异常',
        icon:'none'
      })
      console.error('合成失败', e);
    }
  },
  speakQuestion(){
    const q = (this.data.question as any)?.q || '';
    const hint = (this.data.question as any)?.hint || '';
    const content = `${q}提示：${hint}`;
    this.speakText(content);
  },
  stopSpeak(){
    try{
      if(ttsAudio){
        ttsAudio.stop();
      }
    }catch(e:any){
      console.error('停止播报失败:',e)
    }
  },
  onTopicClose() {
    this.setData({ topicShow: false });
  },
  onTopicConfirm(){
    this.getQuestion(this.data.next_topic_id);
    this.setData({ topicShow: false, topicId: this.data.next_topic_id});
    const periodsArray = wx.getStorageSync('periodsArray')
    const allTopicsObjList = periodsArray.map((period:any) => {
      return period.topics.map((topic:any)=>{
        return {topic_id:topic.topic_id,topic_name:topic.topic_name}
      });
    }).flat();
    const topicTitle = allTopicsObjList.find((item:any)=>item.topic_id === this.data.next_topic_id).topic_name
    wx.setNavigationBarTitle({
      title: topicTitle || '问题回答',
    });
  },
  // 不想回答跳过当前问题，获取下一个问题
  async skipQuestion(){
    const topic_id = this.data.current_topic_id;
    const qa_record_id = (this.data.question as any).qa_record_id;
    const res = await chatApi.skipQuestion(topic_id, qa_record_id);
    if(!res) return;
    this.getQuestion(this.data.topicId);
  }
});
