/**
 * 数据服务 - 统一管理数据访问，减少重复请求
 * 使用单例模式
 */
import API from '../../api/index';

class DataService {
  constructor() {
    this.sessionCache = {}; // 会话数据本地缓存
    this.chainCache = {}; // 问题链数据本地缓存
  }

  /**
   * 获取会话数据
   * @param {string} sessionId - 会话ID
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise} 会话数据Promise
   */
  async getSessionData(sessionId, forceRefresh = false) {
    if (!sessionId) {
      return Promise.reject(new Error('SessionID不能为空'));
    }

    console.log(`[DataService] 获取会话数据: ${sessionId}, 强制刷新: ${forceRefresh}`);

    // 如果本地有缓存且不需要强制刷新，直接返回
    if (!forceRefresh && this.sessionCache[sessionId]) {
      console.log(`[DataService] 使用本地缓存数据: ${sessionId}`);
      return Promise.resolve(this.sessionCache[sessionId]);
    }

    // 调用API获取数据
    try {
      // 如果是强制刷新，也要告诉API强制刷新
      const response = await API.content.getChat(sessionId, forceRefresh);
      
      // 更新本地缓存
      this.sessionCache[sessionId] = response;
      
      // 处理数据格式，确保统一的输出格式
      return this.normalizeSessionData(response);
    } catch (error) {
      console.error(`[DataService] 获取会话数据失败: ${error.message}`);
      return Promise.reject(error);
    }
  }

  /**
   * 获取完整的聊天数据
   * @param {string} sessionId - 会话ID
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise} 完整的聊天数据Promise，包含所有消息按时间排序
   */
  async getChatData(sessionId, forceRefresh = false) {
    try {
      // 获取会话数据
      const sessionData = await this.getSessionData(sessionId, forceRefresh);
      
      // 确保消息按时间排序
      const messages = sessionData.messages || [];
      const sortedMessages = messages.sort((a, b) => {
        return new Date(a.created_at) - new Date(b.created_at);
      });
      
      // 按chain_id分组消息
      const messagesByChain = {};
      sortedMessages.forEach(message => {
        if (message.chain_id) {
          if (!messagesByChain[message.chain_id]) {
            messagesByChain[message.chain_id] = [];
          }
          messagesByChain[message.chain_id].push(message);
        }
      });
      
      // 返回完整数据
      return {
        session_id: sessionData.session_id,
        messages: sortedMessages,
        messagesByChain: messagesByChain,
        chainIds: Object.keys(messagesByChain)
      };
    } catch (error) {
      console.error(`[DataService] 获取完整聊天数据失败: ${error.message}`);
      return Promise.reject(error);
    }
  }

  /**
   * 获取问题链数据
   * @param {string} sessionId - 会话ID
   * @param {string} chainId - 问题链ID
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise} 问题链数据Promise
   */
  async getChainData(sessionId, chainId, forceRefresh = false) {
    if (!sessionId || !chainId) {
      return Promise.reject(new Error('SessionID和ChainID不能为空'));
    }

    const cacheKey = `${sessionId}_${chainId}`;
    console.log(`[DataService] 获取问题链数据: ${cacheKey}, 强制刷新: ${forceRefresh}`);

    // 如果本地有缓存且不需要强制刷新，直接返回
    if (!forceRefresh && this.chainCache[cacheKey]) {
      console.log(`[DataService] 使用本地问题链缓存数据: ${cacheKey}`);
      return Promise.resolve(this.chainCache[cacheKey]);
    }

    try {
      // 先获取会话数据
      const sessionData = await this.getSessionData(sessionId, forceRefresh);
      
      // 从会话数据中提取该问题链的消息
      const messages = sessionData.messages || [];
      const chainMessages = messages.filter(msg => msg.chain_id === chainId);
      
      // 按时间排序
      const sortedMessages = chainMessages.sort((a, b) => {
        return new Date(a.created_at) - new Date(b.created_at);
      });
      
      // 提取问题链信息
      const chainInfo = {
        id: chainId,
        messages: sortedMessages,
        tags: sortedMessages.length > 0 ? (sortedMessages[0].tags || '[]') : '[]'
      };
      
      // 更新本地缓存
      this.chainCache[cacheKey] = chainInfo;
      
      return chainInfo;
    } catch (error) {
      console.error(`[DataService] 获取问题链数据失败: ${error.message}`);
      return Promise.reject(error);
    }
  }

  /**
   * 获取会话的所有问题链ID
   * @param {string} sessionId - 会话ID
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<string[]>} 问题链ID数组Promise
   */
  async getChainIds(sessionId, forceRefresh = false) {
    try {
      // 获取会话数据
      const sessionData = await this.getSessionData(sessionId, forceRefresh);
      const messages = sessionData.messages || [];
      
      // 提取所有不同的问题链ID
      const uniqueChainIds = [...new Set(messages
        .filter(msg => msg.chain_id) // 过滤掉没有chain_id的消息
        .map(msg => msg.chain_id))]; // 提取chain_id
      
      return uniqueChainIds;
    } catch (error) {
      console.error(`[DataService] 获取问题链ID列表失败: ${error.message}`);
      return Promise.reject(error);
    }
  }

  /**
   * 清除会话缓存
   * @param {string} sessionId - 会话ID，如果为空则清除所有缓存
   */
  clearSessionCache(sessionId) {
    if (sessionId) {
      // 清除指定会话的缓存
      delete this.sessionCache[sessionId];
      
      // 清除与该会话相关的所有问题链缓存
      Object.keys(this.chainCache).forEach(key => {
        if (key.startsWith(`${sessionId}_`)) {
          delete this.chainCache[key];
        }
      });
      
      console.log(`[DataService] 已清除会话缓存: ${sessionId}`);
    } else {
      // 清除所有缓存
      this.sessionCache = {};
      this.chainCache = {};
      console.log('[DataService] 已清除所有缓存');
    }
    
    // 同时清除API层的缓存
    API.content.invalidateChatCache(sessionId);
  }

  /**
   * 标准化会话数据格式
   * @param {any} data - 原始会话数据
   * @returns {object} 标准化后的会话数据
   */
  normalizeSessionData(data) {
    // 确保返回统一格式的数据对象
    let messagesArray = [];
    
    if (data.messages && Array.isArray(data.messages)) {
      messagesArray = data.messages;
    } else if (Array.isArray(data)) {
      messagesArray = data;
    }
    
    return {
      session_id: data.session_id || '',
      messages: messagesArray,
      // 可以添加其他会话相关信息
    };
  }
}

// 导出单例
export const dataService = new DataService();
export default dataService; 