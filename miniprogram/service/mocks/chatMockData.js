// 模拟聊天数据
const mockChatData = {
  session_id: "mock-session-123",
  messages: [
    // 第一条问题链
    {
      id: "q1",
      text: "你对自己的童年有什么印象深刻的记忆？",
      sender: "Interviewer",
      created_at: "2023-05-15T09:00:00.000Z",
      chain_id: "chain-1",
      session_id: "mock-session-123"
    },
    {
      id: "a1",
      text: "我童年印象最深刻的是在老家的院子里种植植物。每年春天，我和爷爷会一起挑选种子，然后小心翼翼地把它们种在院子里。我记得我种过向日葵、玉米和西红柿。看着它们从小苗长成大植物，然后开花结果，这个过程让我很着迷。",
      sender: "User",
      created_at: "2023-05-15T09:05:00.000Z",
      chain_id: "chain-1",
      session_id: "mock-session-123"
    },
    {
      id: "q2",
      text: "这些种植经历对你产生了什么影响？",
      sender: "Interviewer",
      created_at: "2023-05-15T09:10:00.000Z",
      chain_id: "chain-1",
      session_id: "mock-session-123"
    },
    {
      id: "a2",
      text: "这些经历让我明白了耐心和坚持的重要性。植物不会一夜之间长大，需要时间、阳光、水和关爱。我学会了等待和观察的价值，以及如何照顾好一个有生命的东西。后来这些品质也帮助我在学业和工作中取得了一些成绩。",
      sender: "User",
      created_at: "2023-05-15T09:15:00.000Z",
      chain_id: "chain-1",
      session_id: "mock-session-123"
    },
    
    // 第二条问题链
    {
      id: "q3",
      text: "谈谈你人生中的一个重要转折点？",
      sender: "Interviewer",
      created_at: "2023-05-15T09:20:00.000Z",
      chain_id: "chain-2",
      session_id: "mock-session-123"
    },
    {
      id: "a3",
      text: "我人生中最重要的转折点是大学毕业后决定到一个陌生的城市工作和生活。之前我一直生活在家乡，身边都是熟悉的人和事。这个决定让我完全走出了舒适区，独自面对各种挑战，从找房子到建立新的社交圈，一切都得靠自己。",
      sender: "User",
      created_at: "2023-05-15T09:25:00.000Z",
      chain_id: "chain-2",
      session_id: "mock-session-123"
    },
    {
      id: "q4",
      text: "这种独立生活的经历给你带来了什么变化？",
      sender: "Interviewer",
      created_at: "2023-05-15T09:30:00.000Z",
      chain_id: "chain-2",
      session_id: "mock-session-123"
    },
    {
      id: "a4",
      text: "这段经历让我变得更加独立和自信。我学会了解决问题，不再依赖父母或朋友。当面对困难时，我知道自己有能力克服。同时，我也更加珍惜人际关系，因为我明白了建立真诚关系的不易。这段经历塑造了现在的我，让我成为了一个更加坚强、更有适应力的人。",
      sender: "User",
      created_at: "2023-05-15T09:35:00.000Z",
      chain_id: "chain-2",
      session_id: "mock-session-123"
    },
    
    // 第三条问题链
    {
      id: "q5",
      text: "你有什么特别的爱好或兴趣？",
      sender: "Interviewer",
      created_at: "2023-05-15T09:40:00.000Z",
      chain_id: "chain-3",
      session_id: "mock-session-123"
    },
    {
      id: "a5",
      text: "我特别喜欢摄影，尤其是风景和街头摄影。周末我经常带着相机出门，寻找美丽或有意义的瞬间。我觉得摄影是一种很特别的表达方式，它让我能够用自己的视角记录这个世界，同时也让我学会了更用心地观察周围的环境。",
      sender: "User",
      created_at: "2023-05-15T09:45:00.000Z",
      chain_id: "chain-3",
      session_id: "mock-session-123"
    }
  ]
};

module.exports = mockChatData; 