import config from '../../config/index';
import API from '../../api/index';
import { clearUserData } from '../../utils/init';

// 从配置中导入存储键
const { storage } = config;

export default class AuthService {
  constructor() {
    this.userInfo = null;
    this.access_token = wx.getStorageSync(storage.accessToken);
    this.refresh_token = wx.getStorageSync(storage.refreshToken);
    this.uuid = wx.getStorageSync(storage.uuid);
    
    // 如果有有效token，在构造函数中尝试初始化WebSocket
    if (this.refresh_token) {
      this.initWebSocket();
    }
  }

  // 检查登录状态
  async checkLoginStatus() {
    // 首先检查是否有token
    if (!API.isAuthenticated()) {
      return false;
    }
    
    try {
      // 如果有token，尝试与服务器验证token有效性
      const token = wx.getStorageSync(storage.accessToken);
      const checkResult = await API.auth.checkToken(token);
      const isValid = checkResult.valid;
      // 如果token有效但WebSocket未连接，尝试连接
      if (isValid && !API.websocket.isConnected()) {
        this.initWebSocket();
      }
      
      return isValid;
    } catch (err) {
      console.error('Token验证失败', err);
      return false;
    }
  }

  // 初始化WebSocket连接
  async initWebSocket() {
    const token = wx.getStorageSync(storage.accessToken);
    if (!token) {
      console.log('[Auth] 未找到token，无法初始化WebSocket');
      return;
    }
    
    try {
      console.log('[Auth] 初始化WebSocket连接');
      await API.websocket.initWebSocket(token);
      console.log('[Auth] WebSocket连接成功');
    } catch (error) {
      console.error('[Auth] WebSocket连接失败', error);
    }
  }

  // 开始登录流程
  async startLogin() {
    try {
      const { code } = await wx.login();
      if (!code) {
        return { success: false, error: '获取登录凭证失败' };
      }

      // 尝试获取微信头像
      let userInfoFromWx = null;
      try {
        const wxUserInfoResult = await wx.getUserInfo({
          lang: 'zh_CN',
          withCredentials: false
        });
        if (wxUserInfoResult && wxUserInfoResult.userInfo) {
          userInfoFromWx = wxUserInfoResult.userInfo;
          console.log('自动获取微信用户信息成功', userInfoFromWx);
        }
      } catch (wxErr) {
        console.log('无法自动获取微信用户信息，将使用静默登录', wxErr);
      }
      
      try {
        // 如果获取到了微信用户信息，使用它进行登录
        if (userInfoFromWx) {
          const loginResult = await API.auth.login(code, userInfoFromWx);
          if (loginResult.success) {
            // 统一设置认证信息
            this.setAuthData(loginResult);
            
            return { success: true };
          }
        }
        
        // 否则尝试静默登录
        const loginResult = await API.auth.silentLogin(code);
        if (loginResult.success) {
          // 统一设置认证信息
          this.setAuthData(loginResult);
          
          return { success: true };
        }
      } catch (err) {
        return { success: false, needUserAuth: true, code };
      }
    } catch (err) {
      console.error('登录失败', err);
      return { success: false, error: '登录失败' };
    }
  }

  // 处理用户信息授权
  async handleUserInfoAuth(code, userInfo) {
    console.log('AuthService: Handling UserInfoAuth with code:', code, 'and userInfo:', JSON.stringify(userInfo));
    try {
      // 确保从微信获取的用户信息包含头像和昵称
      if (userInfo && userInfo.userInfo) {
        const wxUserInfo = userInfo.userInfo;
        console.log('AuthService: Extracted wxUserInfo:', JSON.stringify(wxUserInfo));
        
        // 确保传递给API的是包含 nickName 和 avatarUrl 的对象
        const userDataToSend = {
          nickName: wxUserInfo.nickName,
          avatarUrl: wxUserInfo.avatarUrl,
          gender: wxUserInfo.gender,
          province: wxUserInfo.province,
          city: wxUserInfo.city,
          country: wxUserInfo.country
        };
        console.log('AuthService: Data being sent to API.auth.login:', JSON.stringify(userDataToSend));

        const authResult = await API.auth.login(code, userDataToSend);
        console.log('AuthService: API.auth.login result:', JSON.stringify(authResult));
        
        if (authResult.success) {
          // 检查后端返回的 userInfo 是否包含 nickName
          if (authResult.userInfo) {
            console.log('AuthService: Backend returned userInfo:', JSON.stringify(authResult.userInfo));
            // 如果后端没有返回头像，尝试使用微信的
            if (!authResult.userInfo.avatarUrl) {
              authResult.userInfo.avatarUrl = wxUserInfo.avatarUrl;
            }
            // 如果后端没有返回昵称，尝试使用微信的
            if (!authResult.userInfo.nickName) {
              authResult.userInfo.nickName = wxUserInfo.nickName;
            }
          } else {
            console.warn('AuthService: Backend did not return userInfo in authResult');
            // 如果后端根本没返回 userInfo，创建一个包含微信信息的
            authResult.userInfo = { ...userDataToSend };
          }
          
          // 统一设置认证信息
          this.setAuthData(authResult);
          
          return { success: true, userInfo: authResult.userInfo };
        }
        return { success: false, error: authResult.error };
      } else {
        console.warn('AuthService: UserInfo or userInfo.userInfo is missing. Trying login without specific wxUserInfo.');
        // 如果没有获取到用户的头像信息或昵称，尝试常规登录
        const authResult = await API.auth.login(code, userInfo);
        console.log('AuthService: Fallback API.auth.login result:', JSON.stringify(authResult));
        if (authResult.success) {
          // 统一设置认证信息
          this.setAuthData(authResult);
          
          return { success: true, userInfo: authResult.userInfo };
        }
        return { success: false, error: authResult.error };
      }
    } catch (err) {
      console.error('用户信息授权失败', err);
      return { success: false, error: '授权失败' };
    }
  }

  // 完成登录（不需要用户信息）
  async completeLogin(code) {
    try {
      const loginResult = await API.auth.loginWithCode(code);
      if (loginResult.success) {
        // 统一设置认证信息
        this.setAuthData(loginResult);
        
        return { success: true, userInfo: loginResult.userInfo };
      }
      return { success: false, error: loginResult.error };
    } catch (err) {
      console.error('登录完成失败', err);
      return { success: false, error: '登录失败' };
    }
  }

  // 统一设置认证数据
  async setAuthData(authData) {
    // 设置令牌
    if (authData.access_token) {
      this.setAccessToken(authData.access_token);
    }
    
    if (authData.refresh_token) {
      this.setRefreshToken(authData.refresh_token);
    }
    
    if (authData.uuid) {
      this.setUUID(authData.uuid);
    }
    
    // 设置用户信息
    if (authData.userInfo) {
      this.setUserInfo(authData.userInfo);
    }
    
    // 初始化WebSocket连接
    await this.initWebSocket();
  }

  // 获取用户信息
  async getUserProfile() {
    try {
      const userInfo = await wx.getUserProfile({
        desc: '用于完善会员资料'
      });
      return userInfo;
    } catch (err) {
      console.error('获取用户信息失败', err);
      throw err;
    }
  }

  // 从缓存获取用户信息
  getCachedUserInfo() {
    return wx.getStorageSync(storage.userInfo);
  }

  // 从服务器获取用户信息
  async fetchUserInfo() {
    try {
      const userInfo = await API.user.getProfile();
      console.log("userInfo : " + userInfo)
      
      if (userInfo) {
        // 如果从服务器获取的userInfo没有头像或头像为默认头像，尝试获取微信头像
        if (!userInfo.avatarUrl || userInfo.avatarUrl.includes('default')) {
          try {
            // 尝试获取微信头像
            const wxUserInfo = await wx.getUserInfo({
              lang: 'zh_CN',
              withCredentials: false
            });
            
            if (wxUserInfo && wxUserInfo.userInfo && wxUserInfo.userInfo.avatarUrl) {
              userInfo.avatarUrl = wxUserInfo.userInfo.avatarUrl;
              console.log('已自动加载微信头像');
            }
          } catch (wxError) {
            console.log('无法自动获取微信头像，需要用户手动选择', wxError);
            // 静默失败，继续使用服务器返回的头像
          }
        }
        
        this.setUserInfo(userInfo);
      }
      return userInfo;
    } catch (err) {
      console.error('获取用户信息失败', err);
      throw err;
    }
  }

  // 退出登录
  async logout() {
    try {
      // 调用API登出
      try {
        await API.auth.logout();
      } catch (err) {
        console.warn('登出API调用失败，但仍将清除本地数据', err);
      }
      
      // 清除本地数据
      clearUserData();
      
      // 返回登出成功
      return { success: true };
    } catch (err) {
      console.error('登出失败', err);
      return { success: false, error: '登出失败' };
    }
  }

  // 设置用户信息
  setUserInfo(userInfo) {
    this.userInfo = userInfo;
    wx.setStorageSync(storage.userInfo, userInfo);
  }

  // 设置uuid
  setUUID(uuid) {
    this.uuid = uuid;
    wx.setStorageSync(storage.uuid, uuid);
  }

  // 设置access_token
  setAccessToken(access_token) {
    this.access_token = access_token;
    wx.setStorageSync(storage.accessToken, access_token);
  }

  // 设置refresh_token
  setRefreshToken(refresh_token) {
    this.refresh_token = refresh_token;
    wx.setStorageSync(storage.refreshToken, refresh_token);
  }

  // 清除用户数据
  clearUserData() {
    clearUserData();
    this.userInfo = null;
    this.access_token = null;
    this.refresh_token = null;
  }

  /**
   * 更新用户的问题链数量（仅更新本地缓存）
   * @param {number} count - 新的问题链数量或增量值
   * @param {boolean} isIncrement - 如果为true，则增加计数而不是设置它
   */
  async updateUserChainCount(count, isIncrement = true) {
    try {
      const currentUserInfo = this.userInfo || this.getCachedUserInfo();
      
      if (currentUserInfo) {
        // 如果是增量更新
        if (isIncrement) {
          const currentCount = currentUserInfo.chain_count || 0;
          currentUserInfo.chain_count = currentCount + count;
        } else {
          // 直接设置为新值
          currentUserInfo.chain_count = count;
        }
        
        // 更新存储的用户信息
        this.setUserInfo(currentUserInfo);
        return { success: true };
      }
      
      return { success: false, error: '无法更新问题链数量：用户信息不存在' };
    } catch (error) {
      console.error('更新用户问题链数量失败:', error);
      return { success: false, error: error.message || '更新问题链数量失败' };
    }
  }
}

// 导出单例
export const authService = new AuthService(); 