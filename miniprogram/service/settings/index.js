// 设置存储key
const SETTINGS_STORAGE_KEY = 'userSettings';
const PROGRESS_STORAGE_KEY = 'readingProgress';

// 默认设置
const DEFAULT_SETTINGS = {
  styleOptions: ['文学散文', '新闻报道', '学术论文', '情感随笔', '故事叙述'],
  styleIndex: 0,
  fontSize: 16,
  currentProgress: 0
};

class SettingsService {
  constructor() {
    this.settings = this.loadSettings();
    this.progress = wx.getStorageSync(PROGRESS_STORAGE_KEY) || 0;
  }

  // 加载设置
  loadSettings() {
    const savedSettings = wx.getStorageSync(SETTINGS_STORAGE_KEY);
    return { ...DEFAULT_SETTINGS, ...savedSettings };
  }

  // 保存设置
  saveSettings() {
    wx.setStorageSync(SETTINGS_STORAGE_KEY, this.settings);
  }

  // 获取当前设置
  getSettings() {
    return this.settings;
  }

  // 更新文章风格
  setStyle(index) {
    if (index >= 0 && index < this.settings.styleOptions.length) {
      this.settings.styleIndex = index;
      this.saveSettings();
    }
  }

  // 更新字体大小
  setFontSize(fontSize) {
    this.settings.fontSize = fontSize;
    this.saveSettings();
  }

  // 获取当前文章风格
  getCurrentStyle() {
    return this.settings.styleOptions[this.settings.styleIndex];
  }

  // 获取当前阅读进度
  getCurrentProgress() {
    return this.progress;
  }

  // 更新当前阅读进度
  setCurrentProgress(progress) {
    this.progress = progress;
    wx.setStorageSync(PROGRESS_STORAGE_KEY, progress);
  }

  // 重置设置
  resetSettings() {
    this.settings = { ...DEFAULT_SETTINGS };
    this.saveSettings();
    this.progress = 0;
    wx.setStorageSync(PROGRESS_STORAGE_KEY, 0);
  }
}

// 导出单例
const settingsService = new SettingsService();
export default settingsService; 