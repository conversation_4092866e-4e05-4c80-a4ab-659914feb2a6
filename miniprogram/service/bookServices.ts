// bookService.js
// 回忆录书籍服务层 - 统一管理书籍相关的业务逻辑

class BookService {
  private api: any;
  private isInitialized: boolean = false;
  private listeners: Map<string, Set<(data: any) => void>> = new Map();

  constructor() {
    this.api = null;
    this.isInitialized = false;
    this.listeners = new Map<string, Set<(data: any) => void>>();
  }

  /**
   * 初始化服务
   */
  async initialize() {
    if (this.isInitialized) return true;

    try {
      // 获取API实例
      const app = getApp();
      if (!app || !app.API) {
        throw new Error('API未初始化');
      }

      this.api = app.API;
      this.isInitialized = true;

      // 注册WebSocket监听器
      this.registerWebSocketListener();

      console.log('[BookService] 初始化成功');
      return true;
    } catch (error) {
      console.error('[BookService] 初始化失败:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * 获取书籍数据
   */
  async getBookData() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // 优先使用book API，其次使用content API
      const getBookMethod = this.api.book?.getBook || this.api.content?.getBook;

      if (!getBookMethod) {
        throw new Error('无可用的获取书籍API');
      }

      const bookData = await getBookMethod();
      console.log('[BookService] 获取书籍数据成功:', bookData);
      return bookData;
    } catch (error) {
      console.error('[BookService] 获取书籍数据失败:', error);
      throw error;
    }
  }

  /**
   * 生成书籍大纲
   */
  async generateOutline() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // 查找可用的生成大纲方法
      const generateOutlineMethod = this.api.book?.generateOutline || this.api.content?.generateOutline;

      if (generateOutlineMethod) {
        const response = await generateOutlineMethod();
        return response;
      }

      // 如果没有专门的方法，使用通用HTTP请求
      if (this.api.http?.post) {
        const baseUrl = this.api.config?.baseUrl || 'https://dev.api.memoiris.zhijiucity.com:51002';
        const url = `${baseUrl}/v1/book/generate_outline`;
        const response = await this.api.http.post(url);
        return response;
      }

      throw new Error('无可用的生成大纲API');
    } catch (error) {
      console.error('[BookService] 生成大纲失败:', error);
      throw error;
    }
  }

  /**
   * 获取采访进度
   */
  getProgress() {
    try {
      const progress = wx.getStorageSync('readingProgress') || 0;
      return parseInt(progress);
    } catch (error) {
      console.error('[BookService] 获取进度失败:', error);
      return 0;
    }
  }

  /**
   * 更新采访进度
   */
  updateProgress(progress:any) {
    try {
      wx.setStorageSync('readingProgress', progress);
      // 通知所有监听器
      this.notifyListeners('progress', progress);
    } catch (error) {
      console.error('[BookService] 更新进度失败:', error);
    }
  }

  /**
   * 获取用户设置
   */
  getUserSettings() {
    try {
      return (
        wx.getStorageSync('userSettings') || {
          fontSize: 16,
          theme: 'light',
        }
      );
    } catch (error) {
      console.error('[BookService] 获取用户设置失败:', error);
      return { fontSize: 16, theme: 'light' };
    }
  }

  /**
   * 更新用户设置
   */
  updateUserSettings(settings:any) {
    try {
      const currentSettings = this.getUserSettings();
      const newSettings = { ...currentSettings, ...settings };
      wx.setStorageSync('userSettings', newSettings);
      // 通知所有监听器
      this.notifyListeners('settings', newSettings);
    } catch (error) {
      console.error('[BookService] 更新用户设置失败:', error);
    }
  }

  /**
   * 注册WebSocket监听器
   */
  registerWebSocketListener() {
    if (!this.api?.websocket) return;

    this.api.websocket.registerMessageListener('book-service-listener', (message) => {
      this.handleWebSocketMessage(message);
    });
  }

  /**
   * 处理WebSocket消息
   */
  handleWebSocketMessage(message:any) {
    try {
      console.log('[BookService] 收到WebSocket消息:', message);

      if (typeof message === 'string') {
        message = JSON.parse(message);
      }

      console.log('[BookService] 解析后的消息:', message);
      console.log('[BookService] 消息类型:', message.type);

      // 处理大纲生成完成消息
      if (message.type === 'outline') {
        console.log('[BookService] 匹配到outline类型消息，通知监听器');
        this.notifyListeners('outline', message);
      } else {
        console.log('[BookService] 消息类型不匹配，期望: outline, 实际:', message.type);
      }
    } catch (error) {
      console.error('[BookService] 处理WebSocket消息失败:', error);
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(event:string, callback:(data:any) => void) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(event:string, callback:(data:any) => void) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
    }
  }

  /**
   * 通知监听器
   */
  notifyListeners(event:string, data:any) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[BookService] 监听器执行失败:`, error);
        }
      });
    }
  }

  /**
   * 销毁服务
   */
  destroy() {
    if (this.api?.websocket) {
      this.api.websocket.unregisterMessageListener('book-service-listener');
    }
    this.listeners.clear();
    this.isInitialized = false;
  }
}

// 导出单例实例
export default new BookService(); 