import API from '../../api/index.js';

class UserService {
  constructor() {
    this.defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';
  }

  // 更新用户信息
  async updateUserInfo(userInfo) {
    try {
      const result = await API.user.updateUserInfo(userInfo);
      return result;
    } catch (err) {
      console.error('更新用户信息失败', err);
      throw err;
    }
  }

  // 更新头像
  async updateAvatar(avatarUrl) {
    try {
      // 直接返回头像URL，不经过服务器
      return { success: true, avatarUrl };
    } catch (err) {
      console.error('更新头像失败', err);
      throw err;
    }
  }

  // 选择头像
  async chooseAvatar() {
    try {
      // 添加一个全局标志，防止多个选择头像操作同时进行
      if (getApp().choosingAvatar) {
        console.log('另一个选择头像操作正在进行中');
        return null;
      }
      
      getApp().choosingAvatar = true;
      
      // 确保之前可能的临时文件被清理
      try {
        const fs = wx.getFileSystemManager();
        const tmpDir = `${wx.env.USER_DATA_PATH}/avatar_tmp/`;
        try {
          fs.accessSync(tmpDir);
          const files = fs.readdirSync(tmpDir);
          for (const file of files) {
            try {
              fs.unlinkSync(`${tmpDir}${file}`);
            } catch (e) {
              console.log('清理临时文件失败', e);
            }
          }
        } catch (e) {
          // 目录不存在，创建
          try {
            fs.mkdirSync(tmpDir, true);
          } catch (err) {
            console.log('创建临时目录失败', err);
          }
        }
      } catch (err) {
        console.log('文件系统操作失败', err);
      }
      
      // 使用 Promise 包装 wx.chooseAvatar
      const result = await new Promise((resolve, reject) => {
        wx.chooseAvatar({
          success: (res) => {
            resolve(res);
          },
          fail: (error) => {
            reject(error);
          },
          complete: () => {
            // 操作完成后释放标志
            getApp().choosingAvatar = false;
          }
        });
      });
      
      return result.avatarUrl;
    } catch (err) {
      // 确保发生错误时也释放标志
      getApp().choosingAvatar = false;
      console.error('选择头像失败', err);
      
      // 如果是用户取消，不提示错误
      if (err.errMsg && err.errMsg.indexOf('cancel') !== -1) {
        return null;
      }
      
      // 其他错误
      wx.showToast({
        title: '选择头像失败',
        icon: 'none'
      });
      
      throw err;
    }
  }

  // 获取默认头像
  getDefaultAvatar() {
    return this.defaultAvatarUrl;
  }

  // 更新昵称
  async updateNickname(nickname) {
    try {
      const result = await API.user.updateNickname(nickname);
      return result;
    } catch (err) {
      console.error('更新昵称失败', err);
      throw err;
    }
  }
}

// 导出单例
const userService = new UserService();
export default userService;