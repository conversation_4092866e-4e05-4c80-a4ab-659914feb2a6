import {resolveImageSrc} from '../utils/image-cache'
// tabbar
Component({
  /**
   * 页面的初始数据
   */
  data: {
    selected: 0,
    list: [
      {
        pagePath: 'pages/interview/index',
        text: '采访',
        iconPath: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/interview_grey.png',
        selectedIconPath: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/interview.png',
      },
      {
        pagePath: 'pages/storys/index',
        text: '回忆录',
        iconPath:'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/story.png',
        selectedIconPath: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/memory.png'
      },
      {
        pagePath: 'pages/home/<USER>',
        text: '服务',
        iconPath:'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/mine.png',
        selectedIconPath: 'https://dev.api.memoiris.zhijiucity.com:51012/frontend-images/mine_1.png'
      },
    ],
  },
  lifetimes:{
    attached(){
      this.cacheIcons();
    }
  },
   methods: {
    async cacheIcons(){
      try {
        const tasks = (this.data.list || []).map(async(item:any) => {
          const [icon,selectedIcon] = await Promise.all([
            resolveImageSrc(item.iconPath, { ttlMs: 90 * 24 * 60 * 60 * 1000 }).catch(() => item.iconPath),
            resolveImageSrc(item.selectedIconPath, { ttlMs: 90 * 24 * 60 * 60 * 1000 }).catch(() => item.selectedIconPath)
          ]);
          return {...item, iconPath:icon, selectedIconPath:selectedIcon};
        });
        const newList = await Promise.all(tasks);
        this.setData({
          list:newList
        });
      } catch (e){
        console.error(e);
      }
    },
    handleClick(e:any) {
      let path = e.currentTarget.dataset.path;
      if (!path.startsWith('/')) {
        path = '/' + path;
      }
      wx.switchTab({ url: path });
    }
  }

});
