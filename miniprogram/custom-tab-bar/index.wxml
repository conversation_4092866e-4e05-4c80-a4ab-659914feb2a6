<view class="tabbar-container">
  <view wx:for="{{list}}" class="bar-item {{index === selected ? 'bar-active' : ''}}" wx:key="index"  bindtap="handleClick" data-index="{{ index }}" data-path="{{ item.pagePath }}">
    <image src="{{index === selected ? item.selectedIconPath : item.iconPath}}" mode="aspectFit" class="tab-bar-icon" />
    <view class="bar-text">{{ item.text }}</view>
  </view>
</view>

