@import '/assets/styles/vant-variables.wxss';
@import '/assets/styles/swiper.wxss';

page {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    'Helvetica Neue',
    Helvetica,
    Segoe UI,
    Arial,
    Roboto,
    'PingFang SC',
    'miui',
    'Hiragino Sans GB',
    'Microsoft Yahei',
    sans-serif;
  font-size: 32rpx;
  font-weight: 300;
  color: #031c24;
  background-color: #fff;
}

view,
text {
  word-break: break-word;
}

/* scroll-view滚动条样式 */
::-webkit-scrollbar {
  display: none;
}

.safeAreaInsetBottom {
  padding-bottom: calc(env(safe-area-inset-bottom));
}
