# PDF预览解决方案

## 问题背景

不同设备对PDF预览的支持程度不同：
- **iOS设备**: 支持web-view直接预览PDF文件
- **Android设备**: web-view不支持PDF预览，需要下载后使用系统预览
- **华为设备**: 可能存在额外的兼容性问题

## 解决方案

### 核心策略
根据设备类型自动选择最佳的PDF预览方式：

1. **iOS设备** → 使用 `web-view` 组件直接预览
2. **Android设备** → 下载PDF文件，使用 `wx.openDocument` 系统预览
3. **华为设备** → 特殊处理，优先使用下载预览

### 技术实现

#### 1. 设备检测工具类 (`DeviceHelper`)

```typescript
import DeviceHelper from '../../utils/device-helper';

const deviceHelper = DeviceHelper.getInstance();
const deviceInfo = deviceHelper.getDeviceInfo();

console.log('设备信息:', deviceHelper.getDeviceSummary());
// 输出: "ios | iOS | Other | WebView PDF: Yes"
```

#### 2. 智能预览逻辑

```typescript
handlePdfPreview(pdfUrl: string) {
  const recommendedMethod = deviceHelper.getRecommendedPreviewMethod();
  
  if (recommendedMethod === 'webview') {
    // iOS设备使用web-view
    this.setData({ isLoading: false });
  } else {
    // Android设备使用下载预览
    this.downloadAndPreview(pdfUrl);
  }
}
```

#### 3. 下载预览实现

```typescript
downloadAndPreview(pdfUrl: string) {
  wx.downloadFile({
    url: pdfUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        wx.openDocument({
          filePath: res.tempFilePath,
          showMenu: true
        });
      }
    }
  });
}
```

### 用户界面适配

#### iOS设备界面
```xml
<!-- 使用web-view直接预览 -->
<web-view 
  wx:if="{{isIOS && pdfUrl}}"
  src="{{pdfUrl}}" 
  bindload="onWebViewLoad"
  binderror="onWebViewError">
</web-view>
```

#### Android设备界面
```xml
<!-- 显示下载预览提示 -->
<view wx:elif="{{isAndroid && pdfUrl}}" class="android-preview-info">
  <view class="preview-title">Android设备预览</view>
  <view class="preview-desc">文档已自动下载并使用系统预览打开</view>
  <button bindtap="openFilePreview">重新预览</button>
</view>
```

### 错误处理

#### 设备特定错误提示
```typescript
getDeviceSpecificErrorMessage(): string {
  if (deviceInfo.isHuawei) {
    return '华为设备可能存在兼容性问题，建议使用系统预览功能';
  }
  
  if (deviceInfo.isAndroid) {
    return 'Android设备不支持在线PDF预览，已自动下载到本地预览';
  }
  
  if (deviceInfo.isIOS) {
    return 'iOS设备预览失败，请检查网络连接或尝试系统预览';
  }
  
  return '预览失败，请尝试其他预览方式';
}
```

#### 多重降级方案
1. **主要方案**: 根据设备类型选择预览方式
2. **备用方案**: 系统预览 (`wx.openDocument`)
3. **最后方案**: 复制链接到剪贴板

## 使用方法

### 1. 页面集成

```typescript
// 页面初始化
onLoad(options) {
  const deviceHelper = DeviceHelper.getInstance();
  const deviceInfo = deviceHelper.getDeviceInfo();
  
  this.setData({
    isIOS: deviceInfo.isIOS,
    isAndroid: deviceInfo.isAndroid
  });
  
  const pdfUrl = options.pdfUrl;
  if (pdfUrl) {
    const processedUrl = deviceHelper.processPDFUrl(pdfUrl);
    this.handlePdfPreview(processedUrl);
  }
}
```

### 2. 页面配置

```json
{
  "navigationBarTitleText": "文档预览",
  "enablePullDownRefresh": false
}
```

### 3. 样式配置

```scss
// iOS web-view样式
web-view {
  width: 100%;
  height: 100%;
  flex: 1;
}

// Android预览提示样式
.android-preview-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40rpx;
  text-align: center;
}
```

## 最佳实践

### 1. URL处理
- 确保使用HTTPS协议
- 华为设备添加时间戳避免缓存
- iOS设备添加平台标识

### 2. 错误处理
- 提供设备特定的错误提示
- 实现多重降级方案
- 记录错误日志便于调试

### 3. 用户体验
- 显示加载状态
- 提供重试功能
- 给出明确的操作指引

### 4. 性能优化
- 避免重复下载
- 合理使用缓存
- 及时释放资源

## 调试技巧

### 1. 设备信息查看
```typescript
console.log('设备摘要:', deviceHelper.getDeviceSummary());
console.log('推荐预览方式:', deviceHelper.getRecommendedPreviewMethod());
```

### 2. 真机测试
- 在不同设备上测试预览效果
- 验证错误处理是否正确
- 检查用户体验是否流畅

### 3. 日志监控
```typescript
// 记录预览方式选择
console.log('选择预览方式:', recommendedMethod);

// 记录错误信息
console.error('预览失败:', error);
```

## 常见问题

### Q: 为什么Android设备不能使用web-view预览PDF？
A: Android设备的WebView内核通常不支持直接渲染PDF文件，需要下载后使用系统应用预览。

### Q: 华为设备有什么特殊处理？
A: 华为设备可能存在网络和缓存相关的兼容性问题，通过添加时间戳和设备标识来优化。

### Q: 如何处理预览失败的情况？
A: 提供多重降级方案：web-view → 系统预览 → 复制链接，确保用户总能找到预览方式。

## 总结

通过设备检测和智能预览策略，可以为不同设备提供最佳的PDF预览体验：
- iOS设备享受流畅的web-view预览
- Android设备使用稳定的系统预览
- 华为设备得到特殊优化处理
- 所有设备都有完善的错误处理和降级方案
