# 介绍
AI回忆录生成
主要模块包含：采访、回忆录、服务三大模块
具体prd：https://shanghai-fodev.yuque.com/org-wiki-shanghai-fodev-yzapxc/wgqdni/prnhtq2e86xy2bcy

## 前序准备

你需要在本地安装  [node](http://nodejs.org/)  和  [git](https://git-scm.com/)。本项目技术栈基于  [ES2015+](http://es6.ruanyifeng.com/)、[vant-weapp](https://github.com/youzan/vant-weapp.git)和[dayjs](https://github.com/iamkun/dayjs.git)，提前了解和学习这些知识会对使用本项目有很大的帮助。

## 目录结构

```js
├── README.md
├── api
│   ├── content-service.js
│   └── user-service.js
├── app.js
├── app.json
├── app.scss
├── assets
│   ├── images
│   └── styles
├── components
├── config
│   ├── development.js
│   ├── env.js
│   ├── index.js
│   ├── local.js
│   ├── preview.js
│   ├── production.js
│   └── test.js
├── miniprogram-ci.js
├── miniprogram_npm
│   ├── @vant
│   └── dayjs
├── package.json
├── packageA
│   └── logs
├── pages
│   ├── home
├── private.wx2f3fed2106f72ceb.key
├── project.config.json
├── project.private.config.json
├── sitemap.json
├── switch-env.js
├── utils
│   ├── request.js
│   ├── router.js
│   ├── util.js
│   └── wxs.wxs
└── yarn.lock
```
